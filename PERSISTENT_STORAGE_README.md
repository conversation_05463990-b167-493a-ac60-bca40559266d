# Persistent Storage - Návod

Tento systém zajišťuje, že se při aktualizaci aplikace zachovají důležitá data:
- `.env` soubor s konfigurací
- `storage` slo<PERSON>ka s logy a daty

## Jak to funguje

### Docker Volumes
V `docker-compose.yml` jsou definovány persistent volumes:
- `persistent_env` - pro .env soubor
- `persistent_storage` - pro celou storage složku

### Automatická správa
Při startu kontejneru se spustí skript `manage_persistent_storage.sh`, který:
1. Obnoví .env soubor z persistent storage (pokud existuje)
2. Zálohuje aktuální .env do persistent storage
3. Nastaví správná práva pro storage složku
4. Vytvoří potřebné složky pokud neexistují

## Postup aktualizace

### 1. Příprava nové verze
```bash
# Zastavení současné aplikace
docker-compose down

# Smazání starého zip souboru a rozbalení nového
rm -rf html/
unzip nova_verze.zip
```

### 2. Spuštění s persistent storage
```bash
# Spuštění s automatickou obnovou dat
docker-compose up -d
```

### 3. Kontrola
```bash
# Zkontroluj, že se data obnovila
docker exec adsys_app ls -la /var/www/html/backend/.env
docker exec adsys_app ls -la /var/www/html/backend/storage/logs/
```

## Ruční správa

### Zálohování .env
```bash
# Zkopírování .env z kontejneru
docker cp adsys_app:/var/www/html/backend/.env ./backup_env

# Obnovení .env do kontejneru
docker cp ./backup_env adsys_app:/var/www/html/backend/.env
```

### Zálohování storage
```bash
# Zálohování celé storage složky
docker cp adsys_app:/var/www/html/backend/storage ./backup_storage

# Obnovení storage složky
docker cp ./backup_storage adsys_app:/var/www/html/backend/
```

## Struktura persistent volumes

### persistent_env
- Obsahuje kopii .env souboru
- Automaticky se synchronizuje při startu kontejneru

### persistent_storage
- Obsahuje celou Laravel storage strukturu:
  - `logs/` - aplikační logy
  - `app/` - nahraná data, zálohy
  - `framework/` - cache, sessions, views

## Řešení problémů

### .env se neobnovil
```bash
# Ruční kopírování z persistent storage
docker exec adsys_app cp /var/www/html/backend/.env_persistent /var/www/html/backend/.env
docker exec adsys_app chown www-data:www-data /var/www/html/backend/.env
```

### Problémy s právy
```bash
# Oprava práv pro storage
docker exec adsys_app chown -R www-data:www-data /var/www/html/backend/storage
docker exec adsys_app chmod -R 775 /var/www/html/backend/storage
```

### Smazání persistent dat (POZOR!)
```bash
# Smaže všechna persistent data - použij pouze v nouzi!
docker-compose down -v
docker volume rm adsys_persistent_env adsys_persistent_storage
```

## Monitoring

### Kontrola persistent volumes
```bash
# Seznam volumes
docker volume ls | grep adsys

# Informace o volume
docker volume inspect adsys_persistent_storage
```

### Kontrola dat v kontejneru
```bash
# Kontrola .env
docker exec adsys_app cat /var/www/html/backend/.env | head -5

# Kontrola logů
docker exec adsys_app ls -la /var/www/html/backend/storage/logs/

# Kontrola velikosti storage
docker exec adsys_app du -sh /var/www/html/backend/storage
```

## Poznámky

1. **První spuštění**: Při prvním spuštění se vytvoří prázdné volumes
2. **Aktualizace**: Při aktualizaci se data automaticky obnoví
3. **Zápis do .env**: Aplikace může zapisovat do .env, změny se automaticky zálohují
4. **Logy**: Všechny logy se zachovají mezi aktualizacemi
5. **Zálohy**: Doporučuje se pravidelně zálohovat persistent volumes

## Bezpečnost

- Persistent volumes obsahují citlivá data (.env s hesly)
- Ujisti se, že máš zálohy před velkými změnami
- Volumes jsou lokální na serveru - při přesunu serveru je třeba je zkopírovat
