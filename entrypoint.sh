#!/bin/sh

# Nastavení práv pro skripty
if [ -d "/var/www/scripts" ]; then
    chmod +x /var/www/scripts/*.sh || true
fi

chmod +x /etc/cron.daily/db_backup

# Nastavení vlastníka pro Laravel skripty
chown -R www-data:www-data /var/www/scripts/laravel_queue.sh
chown -R www-data:www-data /var/www/scripts/laravel_scheduler.sh

# Vytvoření log adres<PERSON>ře pro supervisor
mkdir -p /var/log/supervisor

# Správa persistent storage
/var/www/scripts/manage_persistent_storage.sh

# Spuštění supervisord (který spustí všechny služby včetně Apache)
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf