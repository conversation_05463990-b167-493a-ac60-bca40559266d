import{M as B,a as p,D as L,o as r,b as n,d as s,e as d,w as b,u as m,t as f,x as o,F as h,f as k,c as F,n as w,G as A,h as C}from"./index-c2402147.js";import{d as D,f as N,g as j}from"./index-945c46cd.js";import{A as z,F as E,B as I,E as M}from"./listbox-9ed050a9.js";const S={class:"flex items-center justify-center mt-8 border-t border-gray-200"},G={class:"flex-1"},R={class:"mr-auto"},T={class:"flex items-center gap-2"},U={class:"relative translate-y-1"},q={key:0,class:"block truncate font-semibold"},H={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},J={class:"flex-1"},K={class:"flex items-center justify-between px-4 sm:px-0"},O={class:"-mt-px flex flex-1"},Q={class:"hidden md:-mt-px md:flex mx-10"},W={key:1,class:"inline-flex items-center border-t-2 border-transparent px-4 pt-4 text-sm font-medium text-gray-500"},X=["onClick"],Y={key:2,class:"inline-flex items-center border-t-2 border-transparent px-4 pt-4 text-sm font-medium text-gray-500"},Z={class:"-mt-px flex flex-1 justify-end"},se={__name:"pagination",props:["meta"],emits:["setPage"],setup(l,{emit:P}){const y=B(),x=p(y.perPage),V=p([15,30,50,100]),e=l,u=P,c=p(!1),g=p(!1),i=p([]);_();function _(){e.meta.current_page>2?c.value=!1:c.value=!0,e.meta.current_page<e.meta.last_page-1?g.value=!1:g.value=!0,e.meta.last_page<4&&(g.value=!0,c.value=!0),e.meta.current_page===1||e.meta.current_page===e.meta.last_page?(e.meta.current_page===1&&(e.meta.last_page>2?i.value=[1,2,3]:e.meta.last_page===2?i.value=[1,2]:i.value=[1]),e.meta.current_page===e.meta.last_page&&(e.meta.last_page>2?i.value=[e.meta.last_page-2,e.meta.last_page-1,e.meta.last_page]:e.meta.last_page===2?i.value=[e.meta.last_page-1,e.meta.last_page]:i.value=[e.meta.last_page])):i.value=[e.meta.current_page-1,e.meta.current_page,e.meta.current_page+1]}return L(e,()=>{_()}),(ee,t)=>(r(),n("div",S,[s("div",G,[s("div",R,[s("div",T,[d(m(M),{as:"div",modelValue:x.value,"onUpdate:modelValue":t[0]||(t[0]=a=>x.value=a)},{default:b(()=>[s("div",U,[d(m(z),{class:"relative cursor-pointer rounded-lg w-20 py-1.5 pl-3 pr-10 text-left text-gray-900 focus:outline-none bg-gray-200 hover:bg-gray-300 sm:text-sm sm:leading-6"},{default:b(()=>[x.value?(r(),n("span",q,f(x.value),1)):o("",!0),s("span",H,[d(m(D),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]),_:1}),d(m(E),{class:"absolute bottom-11 z-10 max-h-60 w-20 overflow-auto rounded-md bg-white divide-y divide-gray-100 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:b(()=>[(r(!0),n(h,null,k(V.value,a=>(r(),F(m(I),{as:"template",key:a,value:a,onClick:A(v=>m(y).setPerPage(a),["prevent"])},{default:b(({active:v,selectedPerPage:$})=>[s("li",null,[s("span",{class:w([v?"bg-main-color-600 text-white":"text-gray-900","relative cursor-pointer select-none p-2 m-1 rounded-md block"])},[s("span",{class:w([$?"font-semibold":"font-normal","block truncate"])},f(a),3)],2)])]),_:2},1032,["value","onClick"]))),128))]),_:1})])]),_:1},8,["modelValue"])])])]),s("div",J,[s("nav",K,[s("div",O,[l.meta.current_page!==1?(r(),n("button",{key:0,class:"inline-flex items-center border-t-2 border-transparent pr-1 pt-4 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700",onClick:t[1]||(t[1]=a=>u("setPage",l.meta.current_page-1))},[d(m(N),{class:"mr-3 h-5 w-5 text-gray-400","aria-hidden":"true"}),t[6]||(t[6]=C(" Předchozí "))])):o("",!0)]),s("div",Q,[c.value?o("",!0):(r(),n("button",{key:0,href:"#",class:"inline-flex items-center border-t-2 border-transparent px-4 pt-4 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700",onClick:t[2]||(t[2]=a=>u("setPage",1))},"1")),c.value?o("",!0):(r(),n("span",W,"...")),(r(!0),n(h,null,k(i.value,a=>(r(),n("div",null,[l.meta.current_page!==a?(r(),n("button",{key:0,href:"#",class:"inline-flex items-center border-t-2 border-transparent px-4 pt-4 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700",onClick:v=>u("setPage",a)},f(a),9,X)):o("",!0),l.meta.current_page===a?(r(),n("button",{key:1,href:"#",class:"inline-flex items-center border-t-2 border-main-color-500 px-4 pt-4 text-sm font-medium text-main-color-600","aria-current":"page",onClick:t[3]||(t[3]=v=>u("setPage",2))},f(a),1)):o("",!0)]))),256)),g.value?o("",!0):(r(),n("span",Y,"...")),g.value?o("",!0):(r(),n("button",{key:3,href:"#",class:"inline-flex items-center border-t-2 border-transparent px-4 pt-4 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700",onClick:t[4]||(t[4]=a=>u("setPage",l.meta.last_page))},f(l.meta.last_page),1))]),s("div",Z,[l.meta.current_page!==l.meta.last_page?(r(),n("button",{key:0,class:"inline-flex items-center border-t-2 border-transparent pl-1 pt-4 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700",onClick:t[5]||(t[5]=a=>u("setPage",l.meta.current_page+1))},[t[7]||(t[7]=C(" Další ")),d(m(j),{class:"ml-3 h-5 w-5 text-gray-400","aria-hidden":"true"})])):o("",!0)])])]),t[8]||(t[8]=s("div",{class:"flex-1"},null,-1))]))}};export{se as _};
