import{_ as s}from"./_plugin-vue_export-helper-c27b6911.js";import{r as o,o as n,b as a,d as e,e as r,w as c,h as i}from"./index-bfe6943f.js";const l={},m={class:"grid min-h-full bg-gray-50 place-items-center py-24 px-6 sm:py-32 lg:px-8"},d={class:"text-center"},_=e("p",{class:"text-base font-semibold text-main-color-600"},"404",-1),u=e("h1",{class:"mt-4 text-3xl font-bold tracking-tight text-gray-900 sm:text-5xl"},"Stránka nenalezena",-1),x=e("p",{class:"mt-6 text-base leading-7 text-gray-600"},"Omlouváme se, ale požadovaná stránka nebyla nalezena. Prosím, zkontrolujte URL adresu nebo se vraťte na úvodní stránku.",-1),f={class:"mt-10 flex items-center justify-center gap-x-6"};function p(h,b){const t=o("router-link");return n(),a("main",m,[e("div",d,[_,u,x,e("div",f,[r(t,{to:{name:"users"},href:"#",class:"rounded-md bg-main-color-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-main-color-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-main-color-600"},{default:c(()=>[i("Zpět na výpis")]),_:1})])])])}const k=s(l,[["render",p]]);export{k as default};
