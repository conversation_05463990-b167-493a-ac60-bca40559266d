import{u as k,y as H,S as q,F as Q,o as c,c as J,l as C,f as W,K as Z,b as G,H as B,T as X,t as F,p as Y,N as j,I as _,a as p}from"./hidden-ab0c4efa.js";import{x as $,a as S,u as ee}from"./use-tracked-pointer-f932b80e.js";import{b as te}from"./use-resolve-button-type-23b48367.js";import{d as le,e as ae}from"./use-controllable-eb3312b6.js";import{H as E,a as T,I as g,B as M,M as z,N,F as oe,J as ie,K as ne,Z as I,j as ue,a5 as w,L as re}from"./index-f770c8ab.js";function se(t,f){return t===f}var de=(t=>(t[t.Open=0]="Open",t[t.Closed=1]="Closed",t))(de||{}),ve=(t=>(t[t.Single=0]="Single",t[t.Multi=1]="Multi",t))(ve||{}),pe=(t=>(t[t.Pointer=0]="Pointer",t[t.Other=1]="Other",t))(pe||{});function ce(t){requestAnimationFrame(()=>requestAnimationFrame(t))}let U=Symbol("ListboxContext");function A(t){let f=ue(U,null);if(f===null){let y=new Error(`<${t} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(y,A),y}return f}let Se=E({name:"Listbox",emits:{"update:modelValue":t=>!0},props:{as:{type:[Object,String],default:"template"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],default:()=>se},horizontal:{type:[Boolean],default:!1},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},name:{type:String,optional:!0},multiple:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(t,{slots:f,attrs:y,emit:h}){let e=T(1),s=T(null),b=T(null),m=T(null),d=T([]),l=T(""),a=T(null),R=T(1);function O(o=i=>i){let i=a.value!==null?d.value[a.value]:null,u=_(o(d.value.slice()),x=>c(x.dataRef.domRef)),r=i?u.indexOf(i):null;return r===-1&&(r=null),{options:u,activeOptionIndex:r}}let P=g(()=>t.multiple?1:0),[L,D]=le(g(()=>t.modelValue===void 0?k(P.value,{[1]:[],[0]:void 0}):t.modelValue),o=>h("update:modelValue",o),g(()=>t.defaultValue)),n={listboxState:e,value:L,mode:P,compare(o,i){if(typeof t.by=="string"){let u=t.by;return(o==null?void 0:o[u])===(i==null?void 0:i[u])}return t.by(o,i)},orientation:g(()=>t.horizontal?"horizontal":"vertical"),labelRef:s,buttonRef:b,optionsRef:m,disabled:g(()=>t.disabled),options:d,searchQuery:l,activeOptionIndex:a,activationTrigger:R,closeListbox(){t.disabled||e.value!==1&&(e.value=1,a.value=null)},openListbox(){t.disabled||e.value!==0&&(e.value=0)},goToOption(o,i,u){if(t.disabled||e.value===1)return;let r=O(),x=$(o===S.Specific?{focus:S.Specific,id:i}:{focus:o},{resolveItems:()=>r.options,resolveActiveIndex:()=>r.activeOptionIndex,resolveId:V=>V.id,resolveDisabled:V=>V.dataRef.disabled});l.value="",a.value=x,R.value=u??1,d.value=r.options},search(o){if(t.disabled||e.value===1)return;let i=l.value!==""?0:1;l.value+=o.toLowerCase();let u=(a.value!==null?d.value.slice(a.value+i).concat(d.value.slice(0,a.value+i)):d.value).find(x=>x.dataRef.textValue.startsWith(l.value)&&!x.dataRef.disabled),r=u?d.value.indexOf(u):-1;r===-1||r===a.value||(a.value=r,R.value=1)},clearSearch(){t.disabled||e.value!==1&&l.value!==""&&(l.value="")},registerOption(o,i){let u=O(r=>[...r,{id:o,dataRef:i}]);d.value=u.options,a.value=u.activeOptionIndex},unregisterOption(o){let i=O(u=>{let r=u.findIndex(x=>x.id===o);return r!==-1&&u.splice(r,1),u});d.value=i.options,a.value=i.activeOptionIndex,R.value=1},select(o){t.disabled||D(k(P.value,{[0]:()=>o,[1]:()=>{let i=w(n.value.value).slice(),u=w(o),r=i.findIndex(x=>n.compare(u,w(x)));return r===-1?i.push(u):i.splice(r,1),i}}))}};H([b,m],(o,i)=>{var u;n.closeListbox(),q(i,Q.Loose)||(o.preventDefault(),(u=c(b))==null||u.focus())},g(()=>e.value===0)),re(U,n),J(g(()=>k(e.value,{[0]:C.Open,[1]:C.Closed})));let v=g(()=>{var o;return(o=c(b))==null?void 0:o.closest("form")});return M(()=>{z([v],()=>{if(!v.value||t.defaultValue===void 0)return;function o(){n.select(t.defaultValue)}return v.value.addEventListener("reset",o),()=>{var i;(i=v.value)==null||i.removeEventListener("reset",o)}},{immediate:!0})}),()=>{let{name:o,modelValue:i,disabled:u,...r}=t,x={open:e.value===0,disabled:u,value:L.value};return N(oe,[...o!=null&&L.value!=null?ae({[o]:L.value}).map(([V,K])=>N(W,Z({features:G.Hidden,key:V,as:"input",type:"hidden",hidden:!0,readOnly:!0,name:V,value:K}))):[],B({ourProps:{},theirProps:{...y,...X(r,["defaultValue","onUpdate:modelValue","horizontal","multiple","by"])},slot:x,slots:f,attrs:y,name:"Listbox"})])}}});E({name:"ListboxLabel",props:{as:{type:[Object,String],default:"label"},id:{type:String,default:()=>`headlessui-listbox-label-${F()}`}},setup(t,{attrs:f,slots:y}){let h=A("ListboxLabel");function e(){var s;(s=c(h.buttonRef))==null||s.focus({preventScroll:!0})}return()=>{let s={open:h.listboxState.value===0,disabled:h.disabled.value},{id:b,...m}=t,d={id:b,ref:h.labelRef,onClick:e};return B({ourProps:d,theirProps:m,slot:s,attrs:f,slots:y,name:"ListboxLabel"})}}});let ye=E({name:"ListboxButton",props:{as:{type:[Object,String],default:"button"},id:{type:String,default:()=>`headlessui-listbox-button-${F()}`}},setup(t,{attrs:f,slots:y,expose:h}){let e=A("ListboxButton");h({el:e.buttonRef,$el:e.buttonRef});function s(l){switch(l.key){case p.Space:case p.Enter:case p.ArrowDown:l.preventDefault(),e.openListbox(),I(()=>{var a;(a=c(e.optionsRef))==null||a.focus({preventScroll:!0}),e.value.value||e.goToOption(S.First)});break;case p.ArrowUp:l.preventDefault(),e.openListbox(),I(()=>{var a;(a=c(e.optionsRef))==null||a.focus({preventScroll:!0}),e.value.value||e.goToOption(S.Last)});break}}function b(l){switch(l.key){case p.Space:l.preventDefault();break}}function m(l){e.disabled.value||(e.listboxState.value===0?(e.closeListbox(),I(()=>{var a;return(a=c(e.buttonRef))==null?void 0:a.focus({preventScroll:!0})})):(l.preventDefault(),e.openListbox(),ce(()=>{var a;return(a=c(e.optionsRef))==null?void 0:a.focus({preventScroll:!0})})))}let d=te(g(()=>({as:t.as,type:f.type})),e.buttonRef);return()=>{var l,a;let R={open:e.listboxState.value===0,disabled:e.disabled.value,value:e.value.value},{id:O,...P}=t,L={ref:e.buttonRef,id:O,type:d.value,"aria-haspopup":"listbox","aria-controls":(l=c(e.optionsRef))==null?void 0:l.id,"aria-expanded":e.disabled.value?void 0:e.listboxState.value===0,"aria-labelledby":e.labelRef.value?[(a=c(e.labelRef))==null?void 0:a.id,O].join(" "):void 0,disabled:e.disabled.value===!0?!0:void 0,onKeydown:s,onKeyup:b,onClick:m};return B({ourProps:L,theirProps:P,slot:R,attrs:f,slots:y,name:"ListboxButton"})}}}),Oe=E({name:"ListboxOptions",props:{as:{type:[Object,String],default:"ul"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:()=>`headlessui-listbox-options-${F()}`}},setup(t,{attrs:f,slots:y,expose:h}){let e=A("ListboxOptions"),s=T(null);h({el:e.optionsRef,$el:e.optionsRef});function b(l){switch(s.value&&clearTimeout(s.value),l.key){case p.Space:if(e.searchQuery.value!=="")return l.preventDefault(),l.stopPropagation(),e.search(l.key);case p.Enter:if(l.preventDefault(),l.stopPropagation(),e.activeOptionIndex.value!==null){let a=e.options.value[e.activeOptionIndex.value];e.select(a.dataRef.value)}e.mode.value===0&&(e.closeListbox(),I(()=>{var a;return(a=c(e.buttonRef))==null?void 0:a.focus({preventScroll:!0})}));break;case k(e.orientation.value,{vertical:p.ArrowDown,horizontal:p.ArrowRight}):return l.preventDefault(),l.stopPropagation(),e.goToOption(S.Next);case k(e.orientation.value,{vertical:p.ArrowUp,horizontal:p.ArrowLeft}):return l.preventDefault(),l.stopPropagation(),e.goToOption(S.Previous);case p.Home:case p.PageUp:return l.preventDefault(),l.stopPropagation(),e.goToOption(S.First);case p.End:case p.PageDown:return l.preventDefault(),l.stopPropagation(),e.goToOption(S.Last);case p.Escape:l.preventDefault(),l.stopPropagation(),e.closeListbox(),I(()=>{var a;return(a=c(e.buttonRef))==null?void 0:a.focus({preventScroll:!0})});break;case p.Tab:l.preventDefault(),l.stopPropagation();break;default:l.key.length===1&&(e.search(l.key),s.value=setTimeout(()=>e.clearSearch(),350));break}}let m=Y(),d=g(()=>m!==null?(m.value&C.Open)===C.Open:e.listboxState.value===0);return()=>{var l,a,R,O;let P={open:e.listboxState.value===0},{id:L,...D}=t,n={"aria-activedescendant":e.activeOptionIndex.value===null||(l=e.options.value[e.activeOptionIndex.value])==null?void 0:l.id,"aria-multiselectable":e.mode.value===1?!0:void 0,"aria-labelledby":(O=(a=c(e.labelRef))==null?void 0:a.id)!=null?O:(R=c(e.buttonRef))==null?void 0:R.id,"aria-orientation":e.orientation.value,id:L,onKeydown:b,role:"listbox",tabIndex:0,ref:e.optionsRef};return B({ourProps:n,theirProps:D,slot:P,attrs:f,slots:y,features:j.RenderStrategy|j.Static,visible:d.value,name:"ListboxOptions"})}}}),he=E({name:"ListboxOption",props:{as:{type:[Object,String],default:"li"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},id:{type:String,default:()=>`headlessui-listbox.option-${F()}`}},setup(t,{slots:f,attrs:y,expose:h}){let e=A("ListboxOption"),s=T(null);h({el:s,$el:s});let b=g(()=>e.activeOptionIndex.value!==null?e.options.value[e.activeOptionIndex.value].id===t.id:!1),m=g(()=>k(e.mode.value,{[0]:()=>e.compare(w(e.value.value),w(t.value)),[1]:()=>w(e.value.value).some(n=>e.compare(w(n),w(t.value)))})),d=g(()=>k(e.mode.value,{[1]:()=>{var n;let v=w(e.value.value);return((n=e.options.value.find(o=>v.some(i=>e.compare(w(i),w(o.dataRef.value)))))==null?void 0:n.id)===t.id},[0]:()=>m.value})),l=g(()=>({disabled:t.disabled,value:t.value,textValue:"",domRef:s}));M(()=>{var n,v;let o=(v=(n=c(s))==null?void 0:n.textContent)==null?void 0:v.toLowerCase().trim();o!==void 0&&(l.value.textValue=o)}),M(()=>e.registerOption(t.id,l)),ie(()=>e.unregisterOption(t.id)),M(()=>{z([e.listboxState,m],()=>{e.listboxState.value===0&&m.value&&k(e.mode.value,{[1]:()=>{d.value&&e.goToOption(S.Specific,t.id)},[0]:()=>{e.goToOption(S.Specific,t.id)}})},{immediate:!0})}),ne(()=>{e.listboxState.value===0&&b.value&&e.activationTrigger.value!==0&&I(()=>{var n,v;return(v=(n=c(s))==null?void 0:n.scrollIntoView)==null?void 0:v.call(n,{block:"nearest"})})});function a(n){if(t.disabled)return n.preventDefault();e.select(t.value),e.mode.value===0&&(e.closeListbox(),I(()=>{var v;return(v=c(e.buttonRef))==null?void 0:v.focus({preventScroll:!0})}))}function R(){if(t.disabled)return e.goToOption(S.Nothing);e.goToOption(S.Specific,t.id)}let O=ee();function P(n){O.update(n)}function L(n){O.wasMoved(n)&&(t.disabled||b.value||e.goToOption(S.Specific,t.id,0))}function D(n){O.wasMoved(n)&&(t.disabled||b.value&&e.goToOption(S.Nothing))}return()=>{let{disabled:n}=t,v={active:b.value,selected:m.value,disabled:n},{id:o,value:i,disabled:u,...r}=t,x={id:o,ref:s,role:"option",tabIndex:n===!0?void 0:-1,"aria-disabled":n===!0?!0:void 0,"aria-selected":m.value,disabled:void 0,onClick:a,onFocus:R,onPointerenter:P,onMouseenter:P,onPointermove:L,onMousemove:L,onPointerleave:D,onMouseleave:D};return B({ourProps:x,theirProps:r,slot:v,attrs:y,slots:f,name:"ListboxOption"})}}});export{ye as A,he as B,Se as E,Oe as F};
