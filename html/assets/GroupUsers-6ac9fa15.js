import{a as l,j as G,o,c as C,w as g,e as u,h as Z,d as e,u as d,b as n,v as w,C as z,k as M,q as A,I as Q,M as H,r as N,F as P,f as E,n as B,t as U,y as J,O as W,B as Y,z as T,R as ee,S as te,_ as se}from"./index-f770c8ab.js";import{_ as oe}from"./AppTopbar-bf151018.js";import{p as O}from"./index-3a3ac444.js";import{_ as ae}from"./pagination-af3b0cce.js";import{_ as X}from"./basicModal-bc7db752.js";import{X as q,e as ne,C as le}from"./index-0d59a700.js";import{S as F}from"./transition-a3ece599.js";import{d as re}from"./debounce-d96c9884.js";import{N as ie,$ as de,K as ue,U as ce,_ as me}from"./combobox-441bbe15.js";import"./listbox-331f328c.js";import"./hidden-ab0c4efa.js";import"./use-tracked-pointer-f932b80e.js";import"./use-resolve-button-type-23b48367.js";import"./use-controllable-eb3312b6.js";import"./dialog-e364ba6d.js";import"./use-tree-walker-c3f4727c.js";const pe={key:0,class:"p-6"},ve=e("span",null,"Opravdu si přejete uživatele ze skupiny smazat?",-1),ge=[ve],_e={class:"border-t p-5"},fe={class:"text-right space-x-3"},he={__name:"removeUserModal",emits:["reloadUsers"],setup(K,{expose:y,emit:k}){const b=l(!1);G("debugModeGlobalVar");const _=l(""),c=l("");l(!1);function i(){b.value=!1}async function h(f,m){b.value=!0,c.value=f,_.value=m}async function x(){await M.post("api/groups/"+c.value+"/delete-users",{users:_.value}).then(f=>{A.success(f.data.message)}).catch(f=>{console.log(f)}),i(),k("reloadUsers",!0)}return y({openModal:h}),(f,m)=>(o(),C(d(F),{appear:"",show:b.value,as:"template",onClose:m[3]||(m[3]=v=>i())},{default:g(()=>[u(X,{size:"sm"},{"modal-title":g(()=>[Z("Smazat uživatele")]),"modal-close-button":g(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:m[0]||(m[0]=v=>i())},[u(d(q),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":g(()=>[_.value?(o(),n("div",pe,ge)):w("",!0),e("div",_e,[e("div",fe,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:m[1]||(m[1]=z(v=>i(),["prevent"]))}," Zavřít "),e("button",{onClick:m[2]||(m[2]=z(v=>x(),["prevent"])),class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-red-600",type:"submit"}," Smazat ")])])]),_:1})]),_:1},8,["show"]))}},xe={class:"p-6"},ye={class:"w-full"},be={key:0},we=e("label",{class:"block text-sm font-normal leading-6 text-gray-900"},"Uživatel:",-1),ke={class:"relative mt-1"},$e={class:"relative w-full cursor-default overflow-hidden rounded-md bg-white border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 text-sm"},Ue={key:0},Ce={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},ze={key:1,class:"h-20 max-h-20 overflow-hidden"},Me={key:2},Ve={class:"border-t p-5"},Se={class:"text-right space-x-3"},Re={__name:"createUserModal",emits:["reloadProtocols"],setup(K,{expose:y,emit:k}){const b=l(!1);G("debugModeGlobalVar");const _=l(null);l(!1);const c=l([]),i=l(""),h=l(!1),x=l(""),f=Q(()=>i.value===""?c.value:c.value.filter(p=>`${p.first_name} ${p.last_name}`.toLowerCase().replace(/\s+/g,"").includes(i.value.toLowerCase().replace(/\s+/g,"")))),m=re(async()=>{try{const p=await M.get("/api/users?page=1&perpage=50&search="+i.value);c.value=p.data.data}catch(p){console.error(p)}finally{h.value=!1}},300);H(i,()=>{h.value=!0,m()});function v(){b.value=!1}async function j(p){await m(),b.value=!0,_.value=null,x.value=p}async function D(){const p={user_id:_.value.id};await M.post("api/groups/"+x.value+"/users",p).then(r=>{A.success(r.data.message)}).catch(r=>{console.log(r)}),v(),k("reloadUsers",!0)}return y({openModal:j}),(p,r)=>{const I=N("VueSpinner"),V=N("divk");return o(),C(d(F),{appear:"",show:b.value,as:"template",onClose:r[6]||(r[6]=t=>v())},{default:g(()=>[u(X,{size:"sm"},{"modal-title":g(()=>[Z("Přidání uživatele do skupiny")]),"modal-close-button":g(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:r[0]||(r[0]=t=>v())},[u(d(q),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":g(()=>[e("div",null,[e("div",xe,[u(V,null,{default:g(()=>[e("div",ye,[c.value?(o(),n("div",be,[we,u(d(ie),{modelValue:_.value,"onUpdate:modelValue":r[3]||(r[3]=t=>_.value=t)},{default:g(()=>[e("div",ke,[e("div",$e,[u(d(de),{class:"w-full border-none py-2 pl-4 pr-10 text-sm leading-5 text-gray-900 focus:ring-0 font-medium",placeholder:"Zvolte uživatele",displayValue:t=>t?`${t.first_name||""} ${t.middle_name?t.middle_name+" ":""}${t.last_name||""}`.trim():"",onChange:r[1]||(r[1]=t=>i.value=t.target.value)},null,8,["displayValue"]),u(d(ue),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:g(()=>[u(d(ne),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),u(d(F),{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:r[2]||(r[2]=t=>i.value="")},{default:g(()=>[u(d(ce),{class:"absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},{default:g(()=>[h.value?w("",!0):(o(),n("div",Ue,[d(f).length===0&&i.value!==""?(o(),n("div",Ce," Žádný uživatel nenalezen. ")):w("",!0)])),h.value?(o(),n("div",ze,[u(I,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(o(),n("div",Me,[(o(!0),n(P,null,E(d(f),t=>(o(),C(d(me),{as:"template",key:t.id,value:t},{default:g(({active:s})=>{var S,R,$;return[e("li",{class:B(["relative cursor-default select-none py-2 pl-4 pr-10",{"bg-main-color-600 text-white":s,"text-gray-900":!s}])},[e("span",{class:B(["block truncate",{"font-medium":((S=_.value)==null?void 0:S.id)==t.id,"font-normal":((R=_.value)==null?void 0:R.id)!=t.id}])},U((t==null?void 0:t.first_name)+" "+(t!=null&&t.middle_name?(t==null?void 0:t.middle_name)+" ":"")+(t==null?void 0:t.last_name)),3),(($=_.value)==null?void 0:$.id)==t.id?(o(),n("span",{key:0,class:B(["absolute inset-y-0 right-0 flex items-center pr-3",{"text-white":s,"text-main-color-600":!s}])},[u(d(le),{class:"h-5 w-5","aria-hidden":"true"})],2)):w("",!0)],2)]}),_:2},1032,["value"]))),128))]))]),_:1})]),_:1})])]),_:1},8,["modelValue"])])):w("",!0)])]),_:1})]),e("div",Ve,[e("div",Se,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:r[4]||(r[4]=z(t=>v(),["prevent"]))}," Zavřít "),e("button",{onClick:r[5]||(r[5]=z(t=>D(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Přidat ")])])])]),_:1})]),_:1},8,["show"])}}},Ne={key:0},je=e("span",null,"Přidat uživatele",-1),De=[je],Ie={class:"space-y-6"},Be={class:"px-0"},Pe={class:"bg-white border border-zinc-200/70 rounded-md p-5"},Fe={class:"sm:flex justify-between items-center gap-4"},Ge={class:"w-80"},Ze={class:"flex items-center gap-4"},Ke=e("span",null,"Resetovat",-1),Le=[Ke],Te={class:"flow-root bg-white border border-zinc-200/70 rounded-md"},Oe={class:"sm:-mx-6 lg:-mx-8"},Ae={class:"inline-block min-w-full align-middle sm:px-6 lg:px-8"},Ee={key:0,class:"min-w-full divide-y divide-gray-200"},Xe={scope:"col",class:"pl-5 pr-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"},qe=["checked"],Qe=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Uživatel ",-1),He=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Email ",-1),Je=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Organizační jednotka ",-1),We=e("th",{scope:"col",class:"pl-5 pr-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"},null,-1),Ye={key:0,class:"divide-y divide-gray-200"},et={class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600"},tt=["value"],st={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},ot={key:0},at={key:1},nt={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},lt={key:0},rt={key:1},it={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},dt={key:0},ut={key:1},ct={class:"text-end pr-5 w-40"},mt={key:1},pt=e("tr",null,[e("td",{colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},"Nebyli nalezeni žádní uživatelé.")],-1),vt=[pt],gt={key:2,class:"bg-gray-100/70"},_t={colspan:"10",class:"py-4 pl-5 px-3 text-sm text-gray-900 bg-gray-100/70"},ft={class:"flex items-center gap-2"},ht=e("span",null,"Zrušit výběr",-1),xt=[ht],Bt={__name:"GroupUsers",setup(K){const y=J();W();const k=l(""),b=l("");G("debugModeGlobalVar");const _=l(["group-users"]),c=l([]),i=l(!1),h=l(""),x=l(1),f=l({}),m=l({}),v=l({});Y(()=>{i.value=!0,D(),p()});function j(){c.value.length==v.value.length?c.value=[]:c.value=v.value.map(t=>t.id)}async function D(){await M.get("/api/groups/"+y.params.id).then(t=>{m.value=t.data.data}).catch(t=>{console.log(t)})}async function p(){i.value=!0,await M.get("/api/groups/"+y.params.id+"/users?page="+x.value+"&search="+h.value).then(t=>{v.value=t.data.data,f.value=t.data.meta}).catch(t=>{console.log(t)}),i.value=!1}function r(t){x.value=t,p()}function I(){i.value=!0,x.value=1,h.value="",p()}function V(){i.value=!0,x.value=1,p()}return(t,s)=>{var $;const S=N("spa"),R=N("VueSpinner");return o(),n(P,null,[m.value.name?(o(),n("div",Ne,[u(oe,{breadCrumbs:_.value,customName:m.value.name||t.$route.meta.title,customDesc:m.value.description||null},{topbarButtons:g(()=>[e("button",{onClick:s[0]||(s[0]=a=>t.$refs.createUserRef.openModal(d(y).params.id)),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600"},De)]),_:1},8,["breadCrumbs","customName","customDesc"])])):w("",!0),e("div",Ie,[e("div",Be,[e("div",Pe,[e("div",Fe,[e("div",Ge,[T(e("input",{type:"text",name:"search",id:"search","onUpdate:modelValue":s[1]||(s[1]=a=>h.value=a),onKeyup:s[2]||(s[2]=te(a=>V(),["enter"])),class:"block w-full rounded-md py-1.5 text-gray-900 border border-gray-300 placeholder:text-gray-400 focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte hledaný výraz..."},null,544),[[ee,h.value]])]),e("div",Ze,[e("button",{onClick:s[3]||(s[3]=a=>I()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},Le),e("button",{onClick:s[4]||(s[4]=a=>V()),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700"}," Filtrovat ")])])])]),e("div",null,[e("div",Te,[e("div",Oe,[e("div",Ae,[i.value==!1?(o(),n("table",Ee,[e("thead",null,[e("tr",null,[e("th",Xe,[e("input",{checked:c.value.length==(($=v.value)==null?void 0:$.length),onClick:j,"aria-describedby":"comments-description",type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-indigo-600 ring-transparent focus:ring-transparent cursor-pointer"},null,8,qe)]),Qe,He,Je,We])]),v.value&&v.value.length?(o(),n("tbody",Ye,[(o(!0),n(P,null,E(v.value,a=>(o(),n("tr",{key:a.id},[e("td",et,[T(e("input",{value:a.id,"onUpdate:modelValue":s[5]||(s[5]=L=>c.value=L),id:"comments","aria-describedby":"comments-description",name:"comments",type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-transparent cursor-pointer"},null,8,tt),[[se,c.value]])]),e("td",st,[a.full_name&&a.full_name.length>1?(o(),n("span",ot,U(a.full_name),1)):(o(),n("span",at,U(a.account_name),1))]),e("td",nt,[a.email?(o(),n("span",lt,U(a.email),1)):(o(),n("span",rt,"-"))]),e("td",it,[a.organization_unit?(o(),n("span",dt,U(a.organization_unit.name),1)):(o(),n("span",ut,"-"))]),e("td",ct,[e("button",null,[u(d(O),{class:"h-9 w-9 text-red-500 bg-red-100 hover:bg-red-200/75 duration-150 p-2 rounded-md","aria-hidden":"true",onClick:L=>t.$refs.removeUserRef.openModal(d(y).params.id,[a.id])},null,8,["onClick"])])])]))),128))])):(o(),n("tbody",mt,vt)),c.value&&c.value.length?(o(),n("tfoot",gt,[e("tr",null,[e("td",_t,[e("div",ft,[e("button",{onClick:s[6]||(s[6]=a=>t.$refs.removeUserRef.openModal(d(y).params.id,c.value)),class:"rounded-md text-red-500 bg-red-100 hover:bg-red-200/75 duration-150 px-2.5 h-10 flex gap-1 justify-center items-center"},[u(d(O),{class:"h-5 w-5","aria-hidden":"true"}),u(S,{class:"pl-2"},{default:g(()=>[Z("Smazat hromadně")]),_:1})]),e("button",{onClick:s[7]||(s[7]=z(a=>c.value=[],["prevent"])),class:"rounded-md bg-main-color-200/75 px-4 h-10 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},xt)])])])])):w("",!0)])):(o(),C(R,{key:1,class:"mx-auto text-spinner-color",size:"40"}))])])]),f.value!==null?(o(),C(ae,{key:0,meta:f.value,onSetPage:r,modelValue:x.value,"onUpdate:modelValue":s[8]||(s[8]=a=>x.value=a)},null,8,["meta","modelValue"])):w("",!0)])]),u(he,{ref_key:"removeUserRef",ref:k,size:"sm",onReloadUsers:s[9]||(s[9]=a=>p())},null,512),u(Re,{ref_key:"createUserRef",ref:b,size:"sm",onReloadUsers:s[10]||(s[10]=a=>p())},null,512)],64)}}};export{Bt as default};
