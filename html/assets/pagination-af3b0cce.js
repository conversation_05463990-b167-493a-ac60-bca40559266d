import{O as $,a as p,M as B,o as a,b as s,d as r,e as c,w as b,u,t as f,v as o,F as h,f as k,c as L,n as w,C as F,h as C}from"./index-f770c8ab.js";import{d as A,f as N,g as j}from"./index-0d59a700.js";import{A as z,F as D,B as E,E as I}from"./listbox-331f328c.js";const M={class:"flex items-center justify-center mt-8 border-t border-gray-200"},S={class:"flex-1"},O={class:"mr-auto"},R={class:"flex items-center gap-2"},T={class:"relative translate-y-1"},U={key:0,class:"block truncate font-semibold"},q={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},G={class:"flex-1"},H={class:"flex items-center justify-between px-4 sm:px-0"},J={class:"-mt-px flex flex-1"},K={class:"hidden md:-mt-px md:flex mx-10"},Q={key:1,class:"inline-flex items-center border-t-2 border-transparent px-4 pt-4 text-sm font-medium text-gray-500"},W=["onClick"],X={key:2,class:"inline-flex items-center border-t-2 border-transparent px-4 pt-4 text-sm font-medium text-gray-500"},Y={class:"-mt-px flex flex-1 justify-end"},Z=r("div",{class:"flex-1"},null,-1),se={__name:"pagination",props:["meta"],emits:["setPage"],setup(l,{emit:m}){const e=l,y=$(),x=p(y.perPage),P=p([15,30,50,100]),d=p(!1),g=p(!1),i=p([]);_();function _(){e.meta.current_page>2?d.value=!1:d.value=!0,e.meta.current_page<e.meta.last_page-1?g.value=!1:g.value=!0,e.meta.last_page<4&&(g.value=!0,d.value=!0),e.meta.current_page===1||e.meta.current_page===e.meta.last_page?(e.meta.current_page===1&&(e.meta.last_page>2?i.value=[1,2,3]:e.meta.last_page===2?i.value=[1,2]:i.value=[1]),e.meta.current_page===e.meta.last_page&&(e.meta.last_page>2?i.value=[e.meta.last_page-2,e.meta.last_page-1,e.meta.last_page]:e.meta.last_page===2?i.value=[e.meta.last_page-1,e.meta.last_page]:i.value=[e.meta.last_page])):i.value=[e.meta.current_page-1,e.meta.current_page,e.meta.current_page+1]}return B(e,()=>{_()}),(ee,n)=>(a(),s("div",M,[r("div",S,[r("div",O,[r("div",R,[c(u(I),{as:"div",modelValue:x.value,"onUpdate:modelValue":n[0]||(n[0]=t=>x.value=t)},{default:b(()=>[r("div",T,[c(u(z),{class:"relative cursor-pointer rounded-lg w-20 py-1.5 pl-3 pr-10 text-left text-gray-900 focus:outline-none bg-gray-200 hover:bg-gray-300 sm:text-sm sm:leading-6"},{default:b(()=>[x.value?(a(),s("span",U,f(x.value),1)):o("",!0),r("span",q,[c(u(A),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]),_:1}),c(u(D),{class:"absolute bottom-11 z-10 max-h-60 w-20 overflow-auto rounded-md bg-white divide-y divide-gray-100 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:b(()=>[(a(!0),s(h,null,k(P.value,t=>(a(),L(u(E),{as:"template",key:t,value:t,onClick:F(v=>u(y).setPerPage(t),["prevent"])},{default:b(({active:v,selectedPerPage:V})=>[r("li",null,[r("span",{class:w([v?"bg-main-color-600 text-white":"text-gray-900","relative cursor-pointer select-none p-2 m-1 rounded-md block"])},[r("span",{class:w([V?"font-semibold":"font-normal","block truncate"])},f(t),3)],2)])]),_:2},1032,["value","onClick"]))),128))]),_:1})])]),_:1},8,["modelValue"])])])]),r("div",G,[r("nav",H,[r("div",J,[l.meta.current_page!==1?(a(),s("button",{key:0,class:"inline-flex items-center border-t-2 border-transparent pr-1 pt-4 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700",onClick:n[1]||(n[1]=t=>m("setPage",l.meta.current_page-1))},[c(u(N),{class:"mr-3 h-5 w-5 text-gray-400","aria-hidden":"true"}),C(" Předchozí ")])):o("",!0)]),r("div",K,[d.value?o("",!0):(a(),s("button",{key:0,href:"#",class:"inline-flex items-center border-t-2 border-transparent px-4 pt-4 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700",onClick:n[2]||(n[2]=t=>m("setPage",1))},"1")),d.value?o("",!0):(a(),s("span",Q,"...")),(a(!0),s(h,null,k(i.value,t=>(a(),s("div",null,[l.meta.current_page!==t?(a(),s("button",{key:0,href:"#",class:"inline-flex items-center border-t-2 border-transparent px-4 pt-4 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700",onClick:v=>m("setPage",t)},f(t),9,W)):o("",!0),l.meta.current_page===t?(a(),s("button",{key:1,href:"#",class:"inline-flex items-center border-t-2 border-main-color-500 px-4 pt-4 text-sm font-medium text-main-color-600","aria-current":"page",onClick:n[3]||(n[3]=v=>m("setPage",2))},f(t),1)):o("",!0)]))),256)),g.value?o("",!0):(a(),s("span",X,"...")),g.value?o("",!0):(a(),s("button",{key:3,href:"#",class:"inline-flex items-center border-t-2 border-transparent px-4 pt-4 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700",onClick:n[4]||(n[4]=t=>m("setPage",l.meta.last_page))},f(l.meta.last_page),1))]),r("div",Y,[l.meta.current_page!==l.meta.last_page?(a(),s("button",{key:0,class:"inline-flex items-center border-t-2 border-transparent pl-1 pt-4 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700",onClick:n[5]||(n[5]=t=>m("setPage",l.meta.current_page+1))},[C(" Další "),c(u(j),{class:"ml-3 h-5 w-5 text-gray-400","aria-hidden":"true"})])):o("",!0)])])]),Z]))}};export{se as _};
