import{g as ie,m as J,f as ee,b as te,H as T,h as k,o as w,u as _,O as G,d as D,i as De,t as C,p as Ae,l as V,y as Re,N as oe,a as ke}from"./hidden-bb6f5784.js";import{K as j,a as v,H as $,I as c,B as L,J as F,N as P,F as Me,M as K,L as q,j as B,a6 as Ce,a0 as je,u as Be,a7 as <PERSON>,Z as xe}from"./index-bfe6943f.js";function se(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function Ne(){return/Android/gi.test(window.navigator.userAgent)}function st(){return se()||Ne()}function qe(e,n,t){ie.isServer||j(l=>{window.addEventListener(e,n,t),l(()=>window.removeEventListener(e,n,t))})}var N=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(N||{});function Ie(){let e=v(0);return qe("keydown",n=>{n.key==="Tab"&&(e.value=n.shiftKey?1:0)}),e}function de(e,n,t,l){ie.isServer||j(a=>{e=e??window,e.addEventListener(n,t,l),a(()=>e.removeEventListener(n,t,l))})}function ce(e){typeof queueMicrotask=="function"?queueMicrotask(e):Promise.resolve().then(e).catch(n=>setTimeout(()=>{throw n}))}function fe(e){if(!e)return new Set;if(typeof e=="function")return new Set(e());let n=new Set;for(let t of e.value){let l=w(t);l instanceof HTMLElement&&n.add(l)}return n}var pe=(e=>(e[e.None=1]="None",e[e.InitialFocus=2]="InitialFocus",e[e.TabLock=4]="TabLock",e[e.FocusLock=8]="FocusLock",e[e.RestoreFocus=16]="RestoreFocus",e[e.All=30]="All",e))(pe||{});let H=Object.assign($({name:"FocusTrap",props:{as:{type:[Object,String],default:"div"},initialFocus:{type:Object,default:null},features:{type:Number,default:30},containers:{type:[Object,Function],default:v(new Set)}},inheritAttrs:!1,setup(e,{attrs:n,slots:t,expose:l}){let a=v(null);l({el:a,$el:a});let r=c(()=>J(a)),o=v(!1);L(()=>o.value=!0),F(()=>o.value=!1),Ue({ownerDocument:r},c(()=>o.value&&!!(e.features&16)));let u=Ve({ownerDocument:r,container:a,initialFocus:c(()=>e.initialFocus)},c(()=>o.value&&!!(e.features&2)));Ye({ownerDocument:r,container:a,containers:e.containers,previousActiveElement:u},c(()=>o.value&&!!(e.features&8)));let i=Ie();function s(p){let b=w(a);b&&(E=>E())(()=>{_(i.value,{[N.Forwards]:()=>{G(b,D.First,{skipElements:[p.relatedTarget]})},[N.Backwards]:()=>{G(b,D.Last,{skipElements:[p.relatedTarget]})}})})}let f=v(!1);function y(p){p.key==="Tab"&&(f.value=!0,requestAnimationFrame(()=>{f.value=!1}))}function h(p){if(!o.value)return;let b=fe(e.containers);w(a)instanceof HTMLElement&&b.add(w(a));let E=p.relatedTarget;E instanceof HTMLElement&&E.dataset.headlessuiFocusGuard!=="true"&&(ve(b,E)||(f.value?G(w(a),_(i.value,{[N.Forwards]:()=>D.Next,[N.Backwards]:()=>D.Previous})|D.WrapAround,{relativeTo:p.target}):p.target instanceof HTMLElement&&k(p.target)))}return()=>{let p={},b={ref:a,onKeydown:y,onFocusout:h},{features:E,initialFocus:z,containers:re,...W}=e;return P(Me,[!!(E&4)&&P(ee,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:s,features:te.Focusable}),T({ourProps:b,theirProps:{...n,...W},slot:p,attrs:n,slots:t,name:"FocusTrap"}),!!(E&4)&&P(ee,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:s,features:te.Focusable})])}}}),{features:pe}),A=[];if(typeof window<"u"&&typeof document<"u"){let e=function(n){n.target instanceof HTMLElement&&n.target!==document.body&&A[0]!==n.target&&(A.unshift(n.target),A=A.filter(t=>t!=null&&t.isConnected),A.splice(10))};window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})}function We(e){let n=v(A.slice());return K([e],([t],[l])=>{l===!0&&t===!1?ce(()=>{n.value.splice(0)}):l===!1&&t===!0&&(n.value=A.slice())},{flush:"post"}),()=>{var t;return(t=n.value.find(l=>l!=null&&l.isConnected))!=null?t:null}}function Ue({ownerDocument:e},n){let t=We(n);L(()=>{j(()=>{var l,a;n.value||((l=e.value)==null?void 0:l.activeElement)===((a=e.value)==null?void 0:a.body)&&k(t())},{flush:"post"})}),F(()=>{k(t())})}function Ve({ownerDocument:e,container:n,initialFocus:t},l){let a=v(null),r=v(!1);return L(()=>r.value=!0),F(()=>r.value=!1),L(()=>{K([n,t,l],(o,u)=>{if(o.every((s,f)=>(u==null?void 0:u[f])===s)||!l.value)return;let i=w(n);i&&ce(()=>{var s,f;if(!r.value)return;let y=w(t),h=(s=e.value)==null?void 0:s.activeElement;if(y){if(y===h){a.value=h;return}}else if(i.contains(h)){a.value=h;return}y?k(y):G(i,D.First|D.NoScroll)===De.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),a.value=(f=e.value)==null?void 0:f.activeElement})},{immediate:!0,flush:"post"})}),a}function Ye({ownerDocument:e,container:n,containers:t,previousActiveElement:l},a){var r;de((r=e.value)==null?void 0:r.defaultView,"focus",o=>{if(!a.value)return;let u=fe(t);w(n)instanceof HTMLElement&&u.add(w(n));let i=l.value;if(!i)return;let s=o.target;s&&s instanceof HTMLElement?ve(u,s)?(l.value=s,k(s)):(o.preventDefault(),o.stopPropagation(),k(i)):k(l.value)},!0)}function ve(e,n){for(let t of e)if(t.contains(n))return!0;return!1}let X=new Map,x=new Map;function ue(e,n=v(!0)){j(t=>{var l;if(!n.value)return;let a=w(e);if(!a)return;t(function(){var o;if(!a)return;let u=(o=x.get(a))!=null?o:1;if(u===1?x.delete(a):x.set(a,u-1),u!==1)return;let i=X.get(a);i&&(i["aria-hidden"]===null?a.removeAttribute("aria-hidden"):a.setAttribute("aria-hidden",i["aria-hidden"]),a.inert=i.inert,X.delete(a))});let r=(l=x.get(a))!=null?l:0;x.set(a,r+1),r===0&&(X.set(a,{"aria-hidden":a.getAttribute("aria-hidden"),inert:a.inert}),a.setAttribute("aria-hidden","true"),a.inert=!0)})}let me=Symbol("ForcePortalRootContext");function Ge(){return B(me,!1)}let ne=$({name:"ForcePortalRoot",props:{as:{type:[Object,String],default:"template"},force:{type:Boolean,default:!1}},setup(e,{slots:n,attrs:t}){return q(me,e.force),()=>{let{force:l,...a}=e;return T({theirProps:a,ourProps:{},slot:{},slots:n,attrs:t,name:"ForcePortalRoot"})}}});function _e(e){let n=J(e);if(!n){if(e===null)return null;throw new Error(`[Headless UI]: Cannot find ownerDocument for contextElement: ${e}`)}let t=n.getElementById("headlessui-portal-root");if(t)return t;let l=n.createElement("div");return l.setAttribute("id","headlessui-portal-root"),n.body.appendChild(l)}let ge=$({name:"Portal",props:{as:{type:[Object,String],default:"div"}},setup(e,{slots:n,attrs:t}){let l=v(null),a=c(()=>J(l)),r=Ge(),o=B(he,null),u=v(r===!0||o==null?_e(l.value):o.resolveTarget());return j(()=>{r||o!=null&&(u.value=o.resolveTarget())}),F(()=>{var i,s;let f=(i=a.value)==null?void 0:i.getElementById("headlessui-portal-root");f&&u.value===f&&u.value.children.length<=0&&((s=u.value.parentElement)==null||s.removeChild(u.value))}),()=>{if(u.value===null)return null;let i={ref:l,"data-headlessui-portal":""};return P(Ce,{to:u.value},T({ourProps:i,theirProps:e,slot:{},attrs:t,slots:n,name:"Portal"}))}}}),he=Symbol("PortalGroupContext"),Je=$({name:"PortalGroup",props:{as:{type:[Object,String],default:"template"},target:{type:Object,default:null}},setup(e,{attrs:n,slots:t}){let l=je({resolveTarget(){return e.target}});return q(he,l),()=>{let{target:a,...r}=e;return T({theirProps:r,ourProps:{},slot:{},attrs:n,slots:t,name:"PortalGroup"})}}}),we=Symbol("StackContext");var le=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))(le||{});function Ke(){return B(we,()=>{})}function ze({type:e,enabled:n,element:t,onUpdate:l}){let a=Ke();function r(...o){l==null||l(...o),a(...o)}L(()=>{K(n,(o,u)=>{o?r(0,e,t):u===!0&&r(1,e,t)},{immediate:!0,flush:"sync"})}),F(()=>{n.value&&r(1,e,t)}),q(we,r)}let ye=Symbol("DescriptionContext");function Qe(){let e=B(ye,null);if(e===null)throw new Error("Missing parent");return e}function Ze({slot:e=v({}),name:n="Description",props:t={}}={}){let l=v([]);function a(r){return l.value.push(r),()=>{let o=l.value.indexOf(r);o!==-1&&l.value.splice(o,1)}}return q(ye,{register:a,slot:e,name:n,props:t}),c(()=>l.value.length>0?l.value.join(" "):void 0)}$({name:"Description",props:{as:{type:[Object,String],default:"p"},id:{type:String,default:()=>`headlessui-description-${C()}`}},setup(e,{attrs:n,slots:t}){let l=Qe();return L(()=>F(l.register(e.id))),()=>{let{name:a="Description",slot:r=v({}),props:o={}}=l,{id:u,...i}=e,s={...Object.entries(o).reduce((f,[y,h])=>Object.assign(f,{[y]:Be(h)}),{}),id:u};return T({ourProps:s,theirProps:i,slot:r.value,attrs:n,slots:t,name:a})}}});function Xe(e){let n=He(e.getSnapshot());return F(e.subscribe(()=>{n.value=e.getSnapshot()})),n}function be(){let e=[],n={addEventListener(t,l,a,r){return t.addEventListener(l,a,r),n.add(()=>t.removeEventListener(l,a,r))},requestAnimationFrame(...t){let l=requestAnimationFrame(...t);n.add(()=>cancelAnimationFrame(l))},nextFrame(...t){n.requestAnimationFrame(()=>{n.requestAnimationFrame(...t)})},setTimeout(...t){let l=setTimeout(...t);n.add(()=>clearTimeout(l))},style(t,l,a){let r=t.style.getPropertyValue(l);return Object.assign(t.style,{[l]:a}),this.add(()=>{Object.assign(t.style,{[l]:r})})},group(t){let l=be();return t(l),this.add(()=>l.dispose())},add(t){return e.push(t),()=>{let l=e.indexOf(t);if(l>=0)for(let a of e.splice(l,1))a()}},dispose(){for(let t of e.splice(0))t()}};return n}function et(e,n){let t=e(),l=new Set;return{getSnapshot(){return t},subscribe(a){return l.add(a),()=>l.delete(a)},dispatch(a,...r){let o=n[a].call(t,...r);o&&(t=o,l.forEach(u=>u()))}}}function tt(){let e;return{before({doc:n}){var t;let l=n.documentElement;e=((t=n.defaultView)!=null?t:window).innerWidth-l.clientWidth},after({doc:n,d:t}){let l=n.documentElement,a=l.clientWidth-l.offsetWidth,r=e-a;t.style(l,"paddingRight",`${r}px`)}}}function nt(){if(!se())return{};let e;return{before(){e=window.pageYOffset},after({doc:n,d:t,meta:l}){function a(o){return l.containers.flatMap(u=>u()).some(u=>u.contains(o))}t.style(n.body,"marginTop",`-${e}px`),window.scrollTo(0,0);let r=null;t.addEventListener(n,"click",o=>{if(o.target instanceof HTMLElement)try{let u=o.target.closest("a");if(!u)return;let{hash:i}=new URL(u.href),s=n.querySelector(i);s&&!a(s)&&(r=s)}catch{}},!0),t.addEventListener(n,"touchmove",o=>{o.target instanceof HTMLElement&&!a(o.target)&&o.preventDefault()},{passive:!1}),t.add(()=>{window.scrollTo(0,window.pageYOffset+e),r&&r.isConnected&&(r.scrollIntoView({block:"nearest"}),r=null)})}}}function lt(){return{before({doc:e,d:n}){n.style(e.documentElement,"overflow","hidden")}}}function at(e){let n={};for(let t of e)Object.assign(n,t(n));return n}let R=et(()=>new Map,{PUSH(e,n){var t;let l=(t=this.get(e))!=null?t:{doc:e,count:0,d:be(),meta:new Set};return l.count++,l.meta.add(n),this.set(e,l),this},POP(e,n){let t=this.get(e);return t&&(t.count--,t.meta.delete(n)),this},SCROLL_PREVENT({doc:e,d:n,meta:t}){let l={doc:e,d:n,meta:at(t)},a=[nt(),tt(),lt()];a.forEach(({before:r})=>r==null?void 0:r(l)),a.forEach(({after:r})=>r==null?void 0:r(l))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});R.subscribe(()=>{let e=R.getSnapshot(),n=new Map;for(let[t]of e)n.set(t,t.documentElement.style.overflow);for(let t of e.values()){let l=n.get(t.doc)==="hidden",a=t.count!==0;(a&&!l||!a&&l)&&R.dispatch(t.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",t),t.count===0&&R.dispatch("TEARDOWN",t)}});function rt(e,n,t){let l=Xe(R),a=c(()=>{let r=e.value?l.value.get(e.value):void 0;return r?r.count>0:!1});return K([e,n],([r,o],[u],i)=>{if(!r||!o)return;R.dispatch("PUSH",r,t);let s=!1;i(()=>{s||(R.dispatch("POP",u??r,t),s=!0)})},{immediate:!0}),a}var ot=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(ot||{});let ae=Symbol("DialogContext");function I(e){let n=B(ae,null);if(n===null){let t=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,I),t}return n}let Y="DC8F892D-2EBD-447C-A4C8-A03058436FF4",dt=$({name:"Dialog",inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},open:{type:[Boolean,String],default:Y},initialFocus:{type:Object,default:null},id:{type:String,default:()=>`headlessui-dialog-${C()}`}},emits:{close:e=>!0},setup(e,{emit:n,attrs:t,slots:l,expose:a}){var r;let o=v(!1);L(()=>{o.value=!0});let u=v(0),i=Ae(),s=c(()=>e.open===Y&&i!==null?(i.value&V.Open)===V.Open:e.open),f=v(null),y=v(null),h=c(()=>J(f));if(a({el:f,$el:f}),!(e.open!==Y||i!==null))throw new Error("You forgot to provide an `open` prop to the `Dialog`.");if(typeof s.value!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${s.value===Y?void 0:e.open}`);let p=c(()=>o.value&&s.value?0:1),b=c(()=>p.value===0),E=c(()=>u.value>1),z=B(ae,null)!==null,re=c(()=>E.value?"parent":"leaf"),W=c(()=>i!==null?(i.value&V.Closing)===V.Closing:!1),Ee=c(()=>z||W.value?!1:b.value),Se=c(()=>{var d,m,S;return(S=Array.from((m=(d=h.value)==null?void 0:d.querySelectorAll("body > *"))!=null?m:[]).find(g=>g.id==="headlessui-portal-root"?!1:g.contains(w(y))&&g instanceof HTMLElement))!=null?S:null});ue(Se,Ee);let Pe=c(()=>E.value?!0:b.value),Te=c(()=>{var d,m,S;return(S=Array.from((m=(d=h.value)==null?void 0:d.querySelectorAll("[data-headlessui-portal]"))!=null?m:[]).find(g=>g.contains(w(y))&&g instanceof HTMLElement))!=null?S:null});ue(Te,Pe),ze({type:"Dialog",enabled:c(()=>p.value===0),element:f,onUpdate:(d,m)=>{if(m==="Dialog")return _(d,{[le.Add]:()=>u.value+=1,[le.Remove]:()=>u.value-=1})}});let $e=Ze({name:"DialogDescription",slot:c(()=>({open:s.value}))}),U=v(null),O={titleId:U,panelRef:v(null),dialogState:p,setTitleId(d){U.value!==d&&(U.value=d)},close(){n("close",!1)}};q(ae,O);function Q(){var d,m,S;return[...Array.from((m=(d=h.value)==null?void 0:d.querySelectorAll("html > *, body > *, [data-headlessui-portal]"))!=null?m:[]).filter(g=>!(g===document.body||g===document.head||!(g instanceof HTMLElement)||g.contains(w(y))||O.panelRef.value&&g.contains(O.panelRef.value))),(S=O.panelRef.value)!=null?S:f.value]}let Le=c(()=>!(!b.value||E.value));Re(()=>Q(),(d,m)=>{O.close(),xe(()=>m==null?void 0:m.focus())},Le);let Fe=c(()=>!(E.value||p.value!==0));de((r=h.value)==null?void 0:r.defaultView,"keydown",d=>{Fe.value&&(d.defaultPrevented||d.key===ke.Escape&&(d.preventDefault(),d.stopPropagation(),O.close()))});let Oe=c(()=>!(W.value||p.value!==0||z));return rt(h,Oe,d=>{var m;return{containers:[...(m=d.containers)!=null?m:[],Q]}}),j(d=>{if(p.value!==0)return;let m=w(f);if(!m)return;let S=new ResizeObserver(g=>{for(let Z of g){let M=Z.target.getBoundingClientRect();M.x===0&&M.y===0&&M.width===0&&M.height===0&&O.close()}});S.observe(m),d(()=>S.disconnect())}),()=>{let{id:d,open:m,initialFocus:S,...g}=e,Z={...t,ref:f,id:d,role:"dialog","aria-modal":p.value===0?!0:void 0,"aria-labelledby":U.value,"aria-describedby":$e.value},M={open:p.value===0};return P(ne,{force:!0},()=>[P(ge,()=>P(Je,{target:f.value},()=>P(ne,{force:!1},()=>P(H,{initialFocus:S,containers:Q,features:b.value?_(re.value,{parent:H.features.RestoreFocus,leaf:H.features.All&~H.features.FocusLock}):H.features.None},()=>T({ourProps:Z,theirProps:g,slot:M,attrs:t,slots:l,visible:p.value===0,features:oe.RenderStrategy|oe.Static,name:"Dialog"}))))),P(ee,{features:te.Hidden,ref:y})])}}});$({name:"DialogOverlay",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:()=>`headlessui-dialog-overlay-${C()}`}},setup(e,{attrs:n,slots:t}){let l=I("DialogOverlay");function a(r){r.target===r.currentTarget&&(r.preventDefault(),r.stopPropagation(),l.close())}return()=>{let{id:r,...o}=e;return T({ourProps:{id:r,"aria-hidden":!0,onClick:a},theirProps:o,slot:{open:l.dialogState.value===0},attrs:n,slots:t,name:"DialogOverlay"})}}});$({name:"DialogBackdrop",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:()=>`headlessui-dialog-backdrop-${C()}`}},inheritAttrs:!1,setup(e,{attrs:n,slots:t,expose:l}){let a=I("DialogBackdrop"),r=v(null);return l({el:r,$el:r}),L(()=>{if(a.panelRef.value===null)throw new Error("A <DialogBackdrop /> component is being used, but a <DialogPanel /> component is missing.")}),()=>{let{id:o,...u}=e,i={id:o,ref:r,"aria-hidden":!0};return P(ne,{force:!0},()=>P(ge,()=>T({ourProps:i,theirProps:{...n,...u},slot:{open:a.dialogState.value===0},attrs:n,slots:t,name:"DialogBackdrop"})))}}});let ct=$({name:"DialogPanel",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:()=>`headlessui-dialog-panel-${C()}`}},setup(e,{attrs:n,slots:t,expose:l}){let a=I("DialogPanel");l({el:a.panelRef,$el:a.panelRef});function r(o){o.stopPropagation()}return()=>{let{id:o,...u}=e,i={id:o,ref:a.panelRef,onClick:r};return T({ourProps:i,theirProps:u,slot:{open:a.dialogState.value===0},attrs:n,slots:t,name:"DialogPanel"})}}}),ft=$({name:"DialogTitle",props:{as:{type:[Object,String],default:"h2"},id:{type:String,default:()=>`headlessui-dialog-title-${C()}`}},setup(e,{attrs:n,slots:t}){let l=I("DialogTitle");return L(()=>{l.setTitleId(e.id),F(()=>l.setTitleId(null))}),()=>{let{id:a,...r}=e;return T({ourProps:{id:a},theirProps:r,slot:{open:l.dialogState.value===0},attrs:n,slots:t,name:"DialogTitle"})}}});export{Ze as M,ct as U,ft as Y,st as n,dt as q,be as r};
