import{a as i,j as A,B as H,o as t,c as V,w as r,e as a,h as J,d as e,u as o,C,k as z,q as U,b as s,t as y,T as D,f as E,n as F,F as j,O as W,r as X,v as $}from"./index-3de9bee7.js";import{_ as ee}from"./AppTopbar-27ebc835.js";import{l as R,k as B,a2 as te,m as se,$ as oe}from"./index-ddf5f523.js";import{_ as ae}from"./pagination-041319ed.js";import{c as v}from"./checkPermission.service-9981644d.js";import{_ as K}from"./basicModal-d721522e.js";import{S as Q}from"./transition-97ad7178.js";import{g as G,S as T,b as Z,M as q}from"./menu-bce0729d.js";import"./listbox-3a5ebdfc.js";import"./hidden-7b234e84.js";import"./use-tracked-pointer-ed292bdb.js";import"./use-resolve-button-type-8b834d9a.js";import"./use-controllable-4f60503e.js";import"./dialog-ddee7d0d.js";import"./use-tree-walker-b9bc1be5.js";const ne=e("div",{class:"p-6 gap-4"},[e("p",null,"Opravdu si přejete inventuru smazat?")],-1),le={class:"border-t p-5"},re={class:"text-right space-x-3"},ie={__name:"deleteInventoryModal",props:{inventory:{type:Object,required:!0}},emits:["reloadInventories"],setup(L,{expose:I,emit:S}){const b=L,c=i(!1);A("debugModeGlobalVar"),i(null),i("");const _=i(!1);H(()=>{});function p(){c.value=!1}function x(){c.value=!0}async function k(){v.check("inventories.delete")||v.check("property.master")?await z.post("api/inventories/"+b.inventory.id+"/delete").then(h=>{U.success(h.data.message)}).catch(h=>{U.error(h.message)}):_.value=!1,p(),S("reloadInventories",!0)}return I({openModal:x}),(h,m)=>(t(),V(o(Q),{appear:"",show:c.value,as:"template",onClose:m[3]||(m[3]=f=>p())},{default:r(()=>[a(K,{size:"sm"},{"modal-title":r(()=>[J("Smazat inventuru")]),"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:m[0]||(m[0]=f=>p())},[a(o(R),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[ne,e("div",le,[e("div",re,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:m[1]||(m[1]=C(f=>p(),["prevent"]))}," Zavřít "),e("button",{onClick:m[2]||(m[2]=C(f=>k(),["prevent"])),class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-red-600",type:"submit"}," Smazat ")])])]),_:1})]),_:1},8,["show"]))}},de={class:"p-6 grid grid-cols-3"},ce=e("label",{class:"block text-sm font-normal leading-6 text-gray-900 pb-1"},"Přiřazený uživatel:",-1),ue={class:"w-44 text-left"},me={key:0,class:"text-gray-900"},pe={key:1,class:"text-gray-400"},ve=["onClick"],_e=e("label",{class:"block text-sm font-normal leading-6 text-gray-900 pb-1"},"Přiřazená místnost",-1),he={class:"w-44 text-left"},ye={key:0,class:"text-gray-900"},fe={key:1,class:"text-gray-400"},xe=["onClick"],ge={class:"border-t p-5"},be={class:"text-right space-x-3"},ke={__name:"createInventoryModal",emits:["reloadProtocols"],setup(L,{expose:I,emit:S}){const b=i(!1);A("debugModeGlobalVar");const c=i({id:""}),_=i({id:""}),p=i(!1),x=i({}),k=i({});function h(u){c.value=u,_.value={id:""}}function m(u){_.value=u,c.value={id:""}}async function f(){p.value=!0,await z.get("/api/users?page=1&search=").then(u=>{x.value=u.data.data}).catch(u=>{console.log(u)})}async function g(){p.value=!0,await z.get("/api/rooms?page=1&search=").then(u=>{k.value=u.data.data}).catch(u=>{console.log(u)})}function w(){b.value=!1}function N(){f(),g(),b.value=!0,c.value={id:""},_.value={id:""}}async function P(){v.check("inventories.create")||v.check("property.master")?await z.post("api/inventories",{user_id:c.value.id,room_id:_.value.id}).then(u=>{U.success(u.data.message)}).catch(u=>{console.log(u)}):p.value=!1,w(),S("reloadInventories",!0)}return I({openModal:N}),(u,d)=>(t(),V(o(Q),{appear:"",show:b.value,as:"template",onClose:d[3]||(d[3]=l=>w())},{default:r(()=>[a(K,{size:"sm"},{"modal-title":r(()=>[J("Vytvoření nové inventury majetku")]),"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:d[0]||(d[0]=l=>w())},[a(o(R),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[e("div",null,[e("div",de,[e("div",null,[a(o(G),{as:"div",class:"relative inline-block text-left"},{default:r(()=>[e("div",null,[ce,a(o(T),{class:"inline-flex w-full justify-center rounded-md bg-white px-4 py-2 text-sm font-medium text-white border border-gray-300"},{default:r(()=>[e("div",ue,[c.value&&c.value.first_name?(t(),s("span",me,y(c.value.first_name+" "+c.value.last_name),1)):(t(),s("span",pe,"Zvolte uživatele..."))]),a(o(B),{class:"ml-2 -mr-1 h-5 w-5 text-violet-200 hover:text-violet-100","aria-hidden":"true"})]),_:1})]),a(D,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:r(()=>[a(o(Z),{class:"absolute right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:r(()=>[(t(!0),s(j,null,E(x.value,l=>(t(),s("div",{key:l.id,class:"px-1 py-1"},[a(o(q),null,{default:r(({active:M})=>[e("button",{onClick:O=>h(l),class:F([M?"bg-main-color-600 text-white":"text-gray-900","group flex w-full rounded-md px-2 py-2 text-sm text-left"])},y(l.first_name+" "+l.last_name),11,ve)]),_:2},1024)]))),128))]),_:1})]),_:1})]),_:1})]),e("div",null,[_e,a(o(G),{as:"div",class:"relative inline-block text-left"},{default:r(()=>[e("div",null,[a(o(T),{class:"inline-flex w-full justify-center rounded-md bg-white px-4 py-2 text-sm font-medium text-white border border-gray-300"},{default:r(()=>[e("div",he,[_.value&&_.value.name?(t(),s("span",ye,y(_.value.name),1)):(t(),s("span",fe,"Zvolte místnost..."))]),a(o(B),{class:"ml-2 -mr-1 h-5 w-5 text-violet-200 hover:text-violet-100","aria-hidden":"true"})]),_:1})]),a(D,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:r(()=>[a(o(Z),{class:"absolute right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:r(()=>[(t(!0),s(j,null,E(k.value,l=>(t(),s("div",{key:l.id,class:"px-1 py-1"},[a(o(q),null,{default:r(({active:M})=>[e("button",{onClick:O=>m(l),class:F([M?"bg-main-color-600 text-white":"text-gray-900","group flex w-full rounded-md px-2 py-2 text-sm text-left"])},y(l.name),11,xe)]),_:2},1024)]))),128))]),_:1})]),_:1})]),_:1})])]),e("div",ge,[e("div",be,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:d[1]||(d[1]=C(l=>w(),["prevent"]))}," Zavřít "),e("button",{onClick:d[2]||(d[2]=C(l=>P(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Vytvořit ")])])])]),_:1})]),_:1},8,["show"]))}},we={class:"space-y-6"},$e={class:"px-0"},Ce={class:"bg-white border border-zinc-200/70 rounded-md p-5"},Ie={class:"sm:flex justify-between items-center gap-4"},Se={class:"grid grid-cols-6 gap-4"},Me={class:"col-span-2"},ze={class:"w-44 text-left"},je={key:0,class:"text-gray-900"},Ve={key:1,class:"text-gray-400"},Ee=["onClick"],Re={class:"flex items-center gap-4"},Le=e("span",null,"Resetovat",-1),Ne=[Le],Pe={class:"flow-root bg-white border border-zinc-200/70 rounded-md"},Oe={class:"sm:-mx-6 lg:-mx-8"},Ue={class:"inline-block min-w-full align-middle sm:px-6 lg:px-8"},De={key:0,class:"min-w-full divide-y divide-gray-200"},Fe=e("th",{scope:"col",class:"pl-5 pr-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Přiřazený uživatel ",-1),Be=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Přiřazená místnost ",-1),Ge=e("th",{scope:"col",class:"py-4 px-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"}," Typ ",-1),Te=e("th",{scope:"col",class:"py-4 px-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"}," Stav ",-1),Ze={key:0,scope:"col",class:"pr-5 pl-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"},qe={key:0,class:"divide-y divide-gray-200"},Ae={class:"whitespace-nowrap pl-5 pr-3 text-sm text-gray-600"},Ye={key:0},Xe={key:0},He={key:1,class:"text-red-600"},Je={key:1},Ke={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},Qe={key:0},We={key:0},et={key:1,class:"text-red-600"},tt={key:1},st={class:"whitespace-nowrap py-4 px-3 text-sm text-gray-600"},ot={key:0},at={key:1},nt={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},lt={key:0},rt={key:0,class:"border border-amber-600 px-2 py-1 rounded-md text-xs text-amber-600 inline-block"},it={class:"flex"},dt=e("span",null,"Čeká na potvrzení",-1),ct={key:1,class:"border border-green-600 px-2 py-1 rounded-md text-xs text-green-600 inline-block"},ut={class:"flex"},mt=e("span",null,"Úspěšně uzavřeno",-1),pt={key:2,class:"border border-red-600 px-2 py-1 rounded-md text-xs text-red-600 inline-block"},vt={class:"flex"},_t=e("span",null,"Neúspěšně uzavřeno",-1),ht={key:1},yt={key:0},ft={class:"flex justify-end gap-2 pr-5 pl-3"},xt=["onClick"],gt={key:1},bt=e("tr",null,[e("td",{colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},"Nebyly nalezeny žádné inventury.")],-1),kt=[bt],Dt={__name:"Inventories",setup(L){const I=i(""),S=i("");i(),W(),A("debugModeGlobalVar");const b=i(["inventories"]),c=i(!1),_=i(""),p=i(1),x=i({}),k=i([{id:"OPEN",name:"Čeká na potvrzení"},{id:"SUCCESSFULLY_CLOSED",name:"Úspěšně uzavřeno"},{id:"FAILED_CLOSED",name:"Neúspěšně uzavřeno"}]),h=i({}),m=i({id:""}),f=i();H(()=>{f.value="",g()});async function g(){c.value=!0,await z.get("/api/inventories?page="+p.value+"&search="+_.value+"&state="+m.value.id).then(d=>{h.value=d.data.data,x.value=d.data.meta}).catch(d=>{console.log(d)}),c.value=!1}function w(d){f.value=d}function N(d){p.value=d,g()}function P(){c.value=!0,p.value=1,_.value="",m.value={id:""},g()}function u(){c.value=!0,p.value=1,g()}return(d,l)=>{const M=X("router-link"),O=X("VueSpinner");return t(),s(j,null,[a(ee,{breadCrumbs:b.value},{topbarButtons:r(()=>[o(v).check("inventories.create")||o(v).check("property.master")?(t(),s("button",{key:0,onClick:l[0]||(l[0]=C(n=>d.$refs.createInventoryRef.openModal(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},"Nová inventura ")):$("",!0)]),_:1},8,["breadCrumbs"]),e("div",we,[e("div",$e,[e("div",Ce,[e("div",Ie,[e("div",Se,[e("div",Me,[a(o(G),{as:"div",class:"relative inline-block text-left"},{default:r(()=>[e("div",null,[a(o(T),{class:"inline-flex w-full justify-center rounded-md bg-white px-4 py-2 text-sm font-medium text-white border border-gray-300"},{default:r(()=>[e("div",ze,[m.value&&m.value.name?(t(),s("span",je,y(m.value.name),1)):(t(),s("span",Ve,"Stav inventury..."))]),a(o(B),{class:"ml-2 -mr-1 h-5 w-5 text-violet-200 hover:text-violet-100","aria-hidden":"true"})]),_:1})]),a(D,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:r(()=>[a(o(Z),{class:"absolute right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:r(()=>[(t(!0),s(j,null,E(k.value,n=>(t(),s("div",{key:n.id,class:"px-1 py-1"},[a(o(q),null,{default:r(({active:Y})=>[e("button",{onClick:wt=>m.value=n,class:F([Y?"bg-main-color-600 text-white":"text-gray-900","group flex w-full rounded-md px-2 py-2 text-sm text-left"])},y(n.name),11,Ee)]),_:2},1024)]))),128))]),_:1})]),_:1})]),_:1})])]),e("div",Re,[e("button",{onClick:l[1]||(l[1]=n=>P()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},Ne),e("button",{onClick:l[2]||(l[2]=n=>u()),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700"}," Filtrovat ")])])])]),e("div",null,[e("div",Pe,[e("div",Oe,[e("div",Ue,[c.value==!1?(t(),s("table",De,[e("thead",null,[e("tr",null,[Fe,Be,Ge,Te,o(v).check("inventories.edit")||o(v).check("items.delete")?(t(),s("th",Ze)):$("",!0)])]),h.value&&h.value.length?(t(),s("tbody",qe,[(t(!0),s(j,null,E(h.value,n=>(t(),s("tr",{key:n.id},[e("td",Ae,[n.user?(t(),s("span",Ye,[n.user.full_name?(t(),s("span",Xe,y(n.user.full_name),1)):(t(),s("span",He,y(n.user.email)+" - Uživateli chybí údaje",1))])):(t(),s("span",Je,"-"))]),e("td",Ke,[n.room?(t(),s("span",Qe,[n.room.name?(t(),s("span",We,y(n.room.name),1)):(t(),s("span",et,"Místnosti chybí jméno"))])):(t(),s("span",tt,"-"))]),e("td",st,[n.type?(t(),s("span",ot,y(n.type),1)):(t(),s("span",at,"-"))]),e("td",nt,[n.state?(t(),s("div",lt,[n.state=="OPEN"?(t(),s("div",rt,[e("div",it,[a(o(te),{class:"h-4 w-4 text-amber-600 mr-1","aria-hidden":"true"}),dt])])):n.state=="SUCCESSFULLY_CLOSED"?(t(),s("div",ct,[e("div",ut,[a(o(se),{class:"h-4 w-4 text-green-600 mr-1","aria-hidden":"true"}),mt])])):n.state=="FAILED_CLOSED"?(t(),s("div",pt,[e("div",vt,[a(o(R),{class:"h-4 w-4 text-red-600 mr-1","aria-hidden":"true"}),_t])])):$("",!0)])):(t(),s("span",ht,"-"))]),o(v).check("inventories.process")||o(v).check("inventories.delete")||o(v).check("property.master")?(t(),s("td",yt,[e("div",ft,[a(M,{to:{name:"inventories-detail",params:{id:n.id}}},{default:r(()=>[a(o(oe),{class:"h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 p-2 rounded-lg","aria-hidden":"true"})]),_:2},1032,["to"]),(o(v).check("inventories.delete")||o(v).check("property.master"))&&n.state=="OPEN"?(t(),s("button",{key:0,onClick:C(Y=>(w(n),d.$refs.deleteInventoryRef.openModal()),["prevent"])},[a(o(R),{class:"h-8 w-8 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-1.5 rounded-lg","aria-hidden":"true"})],8,xt)):$("",!0)])])):$("",!0)]))),128))])):(t(),s("tbody",gt,kt))])):(t(),V(O,{key:1,class:"mx-auto text-spinner-color",size:"40"}))])])]),x.value!==null?(t(),V(ae,{key:0,meta:x.value,onSetPage:N,modelValue:p.value,"onUpdate:modelValue":l[3]||(l[3]=n=>p.value=n)},null,8,["meta","modelValue"])):$("",!0)])]),a(ke,{ref_key:"createInventoryRef",ref:S,onReloadInventories:l[4]||(l[4]=n=>g())},null,512),a(ie,{ref_key:"deleteInventoryRef",ref:I,inventory:f.value,onReloadInventories:l[5]||(l[5]=n=>g())},null,8,["inventory"])],64)}}};export{Dt as default};
