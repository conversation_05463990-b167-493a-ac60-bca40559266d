import{P as j,j as B,a as i,D as $,G as S,m as _,r as D,o as a,b as o,e as y,w as h,d as e,B as M,a5 as N,$ as F,n as b,u as x,F as f,f as E,c as w,l as L,t as k,H as R}from"./index-9d9f1067.js";import{_ as A}from"./AppTopbar-f7fb50ba.js";import{a4 as G}from"./index-0c6a7c95.js";import{_ as I}from"./pagination-ccc83ede.js";import{a as K}from"./switch-b9c71590.js";import"./index-adce43bb.js";import"./listbox-9038424e.js";import"./hidden-b1ebec83.js";import"./use-tracked-pointer-f803765e.js";import"./use-resolve-button-type-13e1cf97.js";import"./use-controllable-3025fab5.js";const T={class:"space-y-6"},H={class:"px-0"},J={class:"bg-white border border-zinc-200/70 rounded-md p-5"},O={class:"sm:flex justify-between items-center gap-4"},Z={class:"flex items-center gap-y-4 gap-x-10"},q={class:"w-80"},Q={class:"flex items-center gap-4"},W={class:"flex items-center gap-4"},X={class:"flow-root bg-white border border-zinc-200/70 rounded-md"},Y={class:"sm:-mx-6 lg:-mx-8"},ee={class:"inline-block min-w-full align-middle sm:px-6 lg:px-8"},te={key:0,class:"min-w-full divide-y divide-gray-200"},se={key:0,class:"divide-y divide-gray-200"},ae={class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600"},oe={key:0},ne={key:1},le={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},re={key:0},ie={key:1},de={class:"text-end pr-5 w-40"},ue=["onClick"],me={key:1},ce={colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},pe={key:0},ve={key:1},Ue={__name:"PropertyUsers",setup(ye){const V=j();B("debugModeGlobalVar");const z=i(["property-users"]),d=i(!1),m=i(""),l=i(1),p=i({}),r=i(0),c=i({});$(()=>{u()}),S(()=>V.perPage,(n,t)=>{d.value=!0,l.value=1,u()});async function u(){d.value=!0,await _.get("/api/users/property?page="+l.value+"&search="+m.value+"&orderby=last_name&order=desc&deleted="+(r.value?1:0)).then(n=>{c.value=n.data.data,p.value=n.data.meta}).catch(n=>{console.log(n)}),d.value=!1}function C(n){_.post("api/users/property/generate-pdf",{user_id:n},{responseType:"blob"}).then(t=>{const v=window.URL.createObjectURL(new Blob([t.data])),s=document.createElement("a");s.href=v,s.setAttribute("download","Protokol.pdf"),document.body.appendChild(s),s.click()}).catch(t=>{console.error("Chyba při získávání souboru:",t)})}function P(n){l.value=n,u()}function U(){d.value=!0,l.value=1,m.value="",r.value=!1,u()}function g(){d.value=!0,l.value=1,u()}return(n,t)=>{const v=D("VueSpinner");return a(),o(f,null,[y(A,{breadCrumbs:z.value},{topbarButtons:h(()=>t[6]||(t[6]=[])),_:1},8,["breadCrumbs"]),e("div",T,[e("div",H,[e("div",J,[e("div",O,[e("div",Z,[e("div",q,[M(e("input",{type:"text",name:"search",id:"search","onUpdate:modelValue":t[0]||(t[0]=s=>m.value=s),onKeyup:t[1]||(t[1]=F(s=>g(),["enter"])),class:"block w-full rounded-md py-1.5 text-gray-900 border border-gray-300 placeholder:text-gray-400 focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte hledaný výraz..."},null,544),[[N,m.value]])]),e("div",Q,[y(x(K),{modelValue:r.value,"onUpdate:modelValue":t[2]||(t[2]=s=>r.value=s),class:b([r.value?"bg-main-color-600":"bg-gray-200","relative inline-flex h-5 w-9 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out"])},{default:h(()=>[e("span",{"aria-hidden":"true",class:b([r.value?"translate-x-4":"translate-x-0","pointer-events-none inline-block h-4 w-4 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"])},null,2)]),_:1},8,["modelValue","class"]),t[7]||(t[7]=e("p",{class:"text-sm"},"Pouze smazaní uživatele s přiřazeným majetkem",-1))])]),e("div",W,[e("button",{onClick:t[3]||(t[3]=s=>U()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},t[8]||(t[8]=[e("span",null,"Resetovat",-1)])),e("button",{onClick:t[4]||(t[4]=s=>g()),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700"}," Filtrovat ")])])])]),e("div",null,[e("div",X,[e("div",Y,[e("div",ee,[d.value==!1?(a(),o("table",te,[t[9]||(t[9]=e("thead",null,[e("tr",null,[e("th",{scope:"col",class:"pl-5 pr-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Jméno uživatele "),e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Email uživatele "),e("th",{scope:"col",class:"pl-5 pr-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"})])],-1)),c.value&&c.value.length?(a(),o("tbody",se,[(a(!0),o(f,null,E(c.value,s=>(a(),o("tr",{key:s.id,class:b(s.deleted_at?"bg-red-100/70":"")},[e("td",ae,[s.first_name||s.last_name?(a(),o("span",oe,k(s.first_name+" "+s.last_name),1)):(a(),o("span",ne,"-"))]),e("td",le,[s.email?(a(),o("span",re,k(s.email),1)):(a(),o("span",ie,"-"))]),e("td",de,[e("button",{onClick:R(be=>C(s.id),["prevent"])},[y(x(G),{class:"h-8 w-8 text-white bg-main-color-600 hover:bg-main-color-700 duration-150 p-2 rounded-lg","aria-hidden":"true"})],8,ue)])],2))),128))])):(a(),o("tbody",me,[e("tr",null,[e("td",ce,[r.value?(a(),o("span",ve,"Nebyl nalezen žádný smazaný uživatel s přiřazeným majetkem a zadaným filtrem.")):(a(),o("span",pe,"Nebyli nalezeni žádní uživatelé."))])])]))])):(a(),w(v,{key:1,class:"mx-auto text-spinner-color",size:"40"}))])])]),p.value!==null?(a(),w(I,{key:0,meta:p.value,onSetPage:P,modelValue:l.value,"onUpdate:modelValue":t[5]||(t[5]=s=>l.value=s)},null,8,["meta","modelValue"])):L("",!0)])])],64)}}};export{Ue as default};
