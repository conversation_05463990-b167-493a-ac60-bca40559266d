import{f as u}from"./index-5b4677b6.js";import{M as p,r as h,o as e,b as t,d as s,F as _,f,e as n,w as c,h as d,t as l,u as v,v as x,W as b}from"./index-bfe6943f.js";const y={class:"px-0 z-10 top-0 sticky pb-6"},g={class:"bg-white border border-zinc-200/70 rounded-md p-5"},k={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-y-3"},w={key:0,class:"flex items-end"},C={key:0,class:"flex items-start"},B={key:1,class:"flex items-end"},N={class:"text-2xl pt-2"},V={class:"flex flex-wrap gap-x-2 gap-y-2"},L={__name:"AppTopbar",props:{breadCrumbs:Array},setup(o){return p(o,()=>{calculateLinks()}),(a,z)=>{const i=h("router-link");return e(),t("div",y,[s("div",g,[s("div",k,[s("div",null,[o.breadCrumbs?(e(),t("div",w,[(e(!0),t(_,null,f(o.breadCrumbs,(r,m)=>(e(),t("div",null,[m==o.breadCrumbs.length-1?(e(),t("div",C,[n(i,{to:{name:r},class:"text-sm hover:underline"},{default:c(()=>[d(l(a.$router.resolve({name:r}).meta.title),1)]),_:2},1032,["to"])])):(e(),t("div",B,[n(i,{to:{name:r},class:"text-sm hover:underline"},{default:c(()=>[d(l(a.$router.resolve({name:r}).meta.title),1)]),_:2},1032,["to"]),n(v(u),{class:"h-5 text-gray-700 px-2"})]))]))),256))])):x("",!0),s("div",N,l(a.$route.meta.title),1)]),s("div",V,[b(a.$slots,"topbarButtons")])])])])}}};export{L as _};
