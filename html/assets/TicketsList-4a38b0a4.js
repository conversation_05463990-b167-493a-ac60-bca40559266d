import{a as u,o,c as S,w as d,e as n,h as le,d as e,u as a,z as ne,y as E,E as q,H as L,m as D,x as R,q as re,P as ie,D as de,G as ce,r as I,b as i,B as ue,a5 as me,$ as pe,F as T,f as P,l as $,t as p,T as G,n as U,a4 as C}from"./index-9d9f1067.js";import{_ as ve}from"./AppTopbar-f7fb50ba.js";import{_ as ge}from"./pagination-ccc83ede.js";import{d as K}from"./index-adce43bb.js";import{X,R as xe,$ as ye}from"./index-0c6a7c95.js";import{S as fe}from"./vue-tailwind-datepicker-18daaf6c.js";import{c as h}from"./checkPermission.service-d7c9bc43.js";import{_ as he}from"./basicModal-f66ed136.js";import{S as be}from"./transition-a42de4b5.js";import{S as ke,b as _e,M as we,g as Me}from"./menu-0b19fe78.js";import{A as Ve,F as $e,B as Ce,E as Se}from"./listbox-9038424e.js";import"./dialog-71363bda.js";import"./hidden-b1ebec83.js";import"./use-tracked-pointer-f803765e.js";import"./use-tree-walker-0792b2cb.js";import"./use-resolve-button-type-13e1cf97.js";import"./use-controllable-3025fab5.js";const Ye={class:"p-6 space-y-6"},Te={class:"mt-2"},De={class:"mt-2"},ze={class:"border-t p-5"},Ne={class:"text-right space-x-3"},je={__name:"createTicketModal",emits:["reloadTickets"],setup(H,{expose:z,emit:N}){const M=u(!1),V=u(""),b=u(""),f=N;function g(){M.value=!1}function c(){V.value="",b.value="",M.value=!0}function j(){D.post("/api/tickets",{subject:V.value,text:b.value}).then(v=>{R.success(v.data.message),f("reloadTickets",!0),g()}).catch(v=>{console.log(v)})}return z({openModal:c}),(v,r)=>(o(),S(a(be),{appear:"",show:M.value,as:"template",onClose:r[4]||(r[4]=x=>g())},{default:d(()=>[n(he,null,{"modal-title":d(()=>r[5]||(r[5]=[le("Založení nového požadavku")])),"modal-close-button":d(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:r[0]||(r[0]=x=>g())},[n(a(X),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":d(()=>[n(a(ne),{onSubmit:j},{default:d(({values:x})=>[e("div",Ye,[e("div",null,[r[6]||(r[6]=e("label",{for:"ticket_name",class:"block text-sm leading-6 text-gray-900"},"Název požadavku:",-1)),e("div",Te,[n(a(E),{rules:"required",type:"text",name:"ticket_name",id:"ticket_name",modelValue:V.value,"onUpdate:modelValue":r[1]||(r[1]=y=>V.value=y),class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte název nového požadavku..."},null,8,["modelValue"]),n(a(q),{name:"ticket_name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[r[7]||(r[7]=e("label",{for:"ticket_desc",class:"block text-sm leading-6 text-gray-900"},"Text požadavku:",-1)),e("div",De,[n(a(E),{as:"textarea",rules:"required",rows:"4",name:"ticket_desc",id:"ticket_desc",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 resize-none",placeholder:"Zadejte text Vašeho požadavku...",modelValue:b.value,"onUpdate:modelValue":r[2]||(r[2]=y=>b.value=y)},null,8,["modelValue"]),n(a(q),{name:"ticket_desc",class:"text-rose-400 text-sm block pt-1"})])])]),e("div",ze,[e("div",Ne,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:r[3]||(r[3]=L(y=>g(),["prevent"]))}," Zavřít "),r[8]||(r[8]=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Vytvořit ",-1))])])]),_:1})]),_:1})]),_:1},8,["show"]))}},Be={class:"space-y-6"},Fe={class:"px-0"},Pe={class:"bg-white border border-zinc-200/70 rounded-md p-5"},Ue={class:"sm:flex justify-between items-center gap-4"},Re={class:"flex items-center gap-4"},Ze={class:"w-72"},Ae={class:"w-44 text-left"},Ee={key:0,class:"text-gray-900"},qe={key:1,class:"text-gray-400"},Ie=["onClick"],Ge={class:"rounded-l-full w-60"},Ke={type:"button",class:"rounded-md border border-gray-300 block font-normal px-2.5 py-1.5 w-full text-sm leading-6 items-center justify-center space-x-2 sm:space-x-4 transition ease-out duration-300"},Le={class:"flex items-center w-full"},Xe={class:"w-6 h-6"},He={class:"flex-1"},Oe={key:0,class:"text-gray-900"},Je={key:1,class:"text-gray-400"},Qe={class:"flex items-center gap-4"},We={class:"flow-root bg-white border border-zinc-200/70 rounded-md"},et={class:"sm:-mx-6 lg:-mx-8"},tt={class:"inline-block min-w-full align-middle sm:px-6 lg:px-8"},st={key:0,class:"min-w-full divide-y divide-gray-200"},at={scope:"col",class:"py-4 pl-5 pr-3 text-center text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"},ot=["checked"],lt={key:0,class:"divide-y divide-gray-200"},nt={class:"whitespace-nowrap py-4 pl-5 pr-3 text-center"},rt=["onClick","value","checked"],it={class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600"},dt={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},ct={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},ut={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},mt={key:0,class:"bg-red-200/75 text-red-700 rounded-lg px-3 py-1 font-semibold"},pt={key:1,class:"bg-amber-200/75 text-amber-700 rounded-lg px-3 py-1 font-semibold"},vt={key:2,class:"space-x-4 space-y-4"},gt={class:"bg-green-200/75 text-green-700 rounded-lg px-3 py-1 font-semibold"},xt={class:"text-green-700"},yt={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},ft={key:1},ht={class:"bg-gray-100/70"},bt={colspan:"7",class:"py-4 pl-5 px-3 text-sm text-gray-900 bg-gray-100/70"},kt={class:"flex items-center gap-2"},_t={class:"relative"},wt={key:0,class:"block truncate"},Mt={key:1,class:"block h-6 text-gray-400"},Vt={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},$t={key:1,class:"rounded-lg bg-main-color-300 px-4 py-2 text-sm text-white shadow-sm cursor-not-allowed"},It={__name:"TicketsList",setup(H){const z=u(),N=re(),M=ie(),V=u(["tickets-parent","tickets-list"]),b=u({}),f=u({id:"",name:""}),g=u([]),c=u([]),j=[{id:1,name:"Vyřešit",permission:"tickets.close"}],v=u(),r=u(""),x=u(1),y=u({}),B=u({}),k=u(!1),m=u([]),O=u({date:"YYYY-MM-DD",month:"MM"});de(()=>{k.value=!0,Q(),_()}),ce(()=>M.perPage,(l,t)=>{k.value=!0,x.value=1,_()});function J(l){f.value=l}function Q(){D.get("/api/ticket-states").then(l=>{b.value=l.data.data}).catch(l=>{console.log(l)})}function _(){if(h.check("tickets.read")||h.check("tickets.master")){if(m.value[0])var l=[m.value[0],m.value[1]];else var l=["",""];if(h.check("tickets.master"))var t="";else var t=N.user.id;D.get("/api/tickets?page="+x.value+"&perpage="+M.perPage+"10&state_id="+f.value.id+"&user_id="+t+"&search="+r.value+"&date_from="+l[0]+"&date_to="+l[1]).then(w=>{y.value=w.data.data,B.value=w.data.meta,k.value=!1,g.value=[],w.data.data.filter((F,s)=>{g.value.push(F.id)})}).catch(w=>{console.log(w)})}else k.value=!1}function W(){m.value=[]}function ee(l){c.value.includes(l)?c.value=c.value.filter(t=>t!==l):c.value.push(l)}function te(){g.value.length!==c.value.length?(c.value=[],y.value.filter((l,t)=>{c.value.push(l.id)})):c.value=[]}function se(l){x.value=l,c.value=[],_()}function ae(){k.value=!0,x.value=1,r.value="",f.value={id:"",name:""},_()}function Z(){k.value=!0,x.value=1,c.value=[],_()}function oe(){c.value.length&&(h.check("tickets.close")||h.check("tickets.master"))?D.post("/api/tickets/close",{tickets:c.value}).then(l=>{R.success(l.data.message),c.value=[],_()}).catch(l=>{console.log(l)}):R.error("Nebyly vybrány žádné tikety")}return(l,t)=>{const w=I("router-link"),F=I("VueSpinner");return o(),i(T,null,[n(ve,{breadCrumbs:V.value},{topbarButtons:d(()=>[a(h).check("tickets.create")||a(h).check("tickets.master")?(o(),i("button",{key:0,onClick:t[0]||(t[0]=L(s=>l.$refs.createTicketRef.openModal(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},"Nový požadavek ")):$("",!0)]),_:1},8,["breadCrumbs"]),e("div",Be,[e("div",Fe,[e("div",Pe,[e("div",Ue,[e("div",Re,[e("div",Ze,[ue(e("input",{type:"text",name:"search",id:"search","onUpdate:modelValue":t[1]||(t[1]=s=>r.value=s),onKeyup:t[2]||(t[2]=pe(s=>Z(),["enter"])),class:"block w-full rounded-md py-1.5 text-gray-900 border border-gray-300 placeholder:text-gray-400 focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte hledaný výraz..."},null,544),[[me,r.value]])]),e("div",null,[n(a(Me),{as:"div",class:"relative inline-block text-left"},{default:d(()=>[e("div",null,[n(a(ke),{class:"inline-flex w-full justify-center rounded-md bg-white px-4 py-2 text-sm font-medium text-white border border-gray-300"},{default:d(()=>[e("div",Ae,[f.value&&f.value.name?(o(),i("span",Ee,p(f.value.name),1)):(o(),i("span",qe,"Stav vyřešení..."))]),n(a(K),{class:"ml-2 -mr-1 h-5 w-5 text-violet-200 hover:text-violet-100","aria-hidden":"true"})]),_:1})]),n(G,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:d(()=>[n(a(_e),{class:"absolute right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:d(()=>[(o(!0),i(T,null,P(b.value,s=>(o(),i("div",{key:s.id,class:"px-1 py-1"},[n(a(we),null,{default:d(({active:Y})=>[e("button",{onClick:A=>J(s),class:U([Y?"bg-main-color-600 text-white":"text-gray-900","group flex w-full rounded-md px-2 py-2 text-sm text-left"])},p(s.name),11,Ie)]),_:2},1024)]))),128))]),_:1})]),_:1})]),_:1})]),e("div",null,[n(a(fe),{i18n:"cs","use-range":"",shortcuts:!1,modelValue:m.value,"onUpdate:modelValue":t[3]||(t[3]=s=>m.value=s),formatter:O.value},{default:d(({clear:s})=>[e("div",null,[e("div",Ge,[e("button",Ke,[e("div",Le,[e("div",Xe,[m.value&&m.value[0]&&m.value[1]?(o(),S(a(X),{key:0,onClick:Y=>W(),class:"h-6 w-6 text-gray-900","aria-hidden":"true"},null,8,["onClick"])):(o(),S(a(xe),{key:1,class:"h-6 w-6 text-gray-900","aria-hidden":"true"}))]),e("div",He,[m.value&&m.value[0]&&m.value[1]?(o(),i("span",Oe,[e("span",null,p(a(C)(m.value[0]).format("DD.MM.YYYY"))+" - "+p(a(C)(m.value[1]).format("DD.MM.YYYY")),1)])):(o(),i("span",Je,[e("span",null,p(a(C)(m.value).format("DD.MM.YYYY")),1)]))])])])])])]),_:1},8,["modelValue","formatter"])])]),e("div",Qe,[e("button",{onClick:t[4]||(t[4]=s=>ae()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},t[11]||(t[11]=[e("span",null,"Resetovat",-1)])),e("button",{onClick:t[5]||(t[5]=s=>Z()),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700"}," Filtrovat ")])])])]),e("div",null,[e("div",We,[e("div",et,[e("div",tt,[k.value==!1?(o(),i("table",st,[e("thead",null,[e("tr",null,[e("th",at,[e("input",{id:"comments","aria-describedby":"comments-description",name:"comments",type:"checkbox",onClick:t[6]||(t[6]=s=>te()),checked:g.value.length&&g.value.length==c.value.length,class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent ring-transparent cursor-pointer"},null,8,ot)]),t[12]||(t[12]=e("th",{scope:"col",class:"py-4 pl-5 pr-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"}," Název požadavku ",-1)),t[13]||(t[13]=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"},"Datum vytvoření ",-1)),t[14]||(t[14]=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Zadavatel ",-1)),t[15]||(t[15]=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"},"Stav ",-1)),t[16]||(t[16]=e("th",{scope:"col",class:"py-4 pl-3 pr-5 text-left text-sm font-semibold text-gray-900 rounded-tr-md bg-gray-100/70"},null,-1))])]),y.value&&y.value.length?(o(),i("tbody",lt,[(o(!0),i(T,null,P(y.value,s=>(o(),i("tr",{key:s.id},[e("td",nt,[e("input",{id:"comments","aria-describedby":"comments-description",name:"comments",type:"checkbox",onClick:()=>{ee(s.id)},value:s.id,class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",checked:c.value.includes(s.id)},null,8,rt)]),e("td",it,p(s.subject),1),e("td",dt,p(a(C)(s.created_at).format("DD.MM.YYYY")),1),e("td",ct,p(s.user.full_name),1),e("td",ut,[s.state.id==1?(o(),i("span",mt,p(s.state.name),1)):$("",!0),s.state.id==2||s.state.id==3?(o(),i("span",pt,p(s.state.name),1)):$("",!0),s.state.id==4?(o(),i("span",vt,[e("span",gt,p(s.state.name),1),e("span",xt,p(a(C)(s.updated_at).format("DD.MM.YYYY"))+" - "+p(s.user.full_name),1)])):$("",!0)]),e("td",yt,[n(w,{to:{name:"ticket-detail",params:{id:s.id}}},{default:d(()=>[n(a(ye),{class:"ml-2 -mr-1 h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 p-2 rounded-lg","aria-hidden":"true"})]),_:2},1032,["to"])])]))),128))])):(o(),i("tbody",ft,t[17]||(t[17]=[e("tr",null,[e("td",{colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},"Nebyly nalezeny žádné požadavky.")],-1)]))),e("tfoot",ht,[e("tr",null,[e("td",bt,[e("div",kt,[t[18]||(t[18]=e("span",null,"Označené:",-1)),n(a(Se),{as:"div",modelValue:v.value,"onUpdate:modelValue":t[7]||(t[7]=s=>v.value=s)},{default:d(()=>[e("div",_t,[n(a(Ve),{class:"relative cursor-pointer rounded-lg bg-white w-48 py-1.5 pl-3 pr-10 text-left text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-main-color-600 sm:text-sm sm:leading-6"},{default:d(()=>[v.value&&v.value.name?(o(),i("span",wt,p(v.value.name),1)):(o(),i("span",Mt,"vyberte akci...")),e("span",Vt,[n(a(K),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]),_:1}),n(G,{"leave-active-class":"transition ease-in duration-100","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:d(()=>[n(a($e),{class:"absolute bottom-11 z-10 max-h-60 w-48 overflow-auto rounded-md bg-white divide-y divide-gray-100 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:d(()=>[(o(),i(T,null,P(j,s=>n(a(Ce),{as:"template",key:s.id,value:s},{default:d(({active:Y,selectedAction:A})=>[e("li",null,[a(h).check(s.permission)?(o(),i("span",{key:0,class:U([Y?"bg-main-color-600 text-white":"text-gray-900","relative cursor-pointer select-none p-2 m-1 rounded-md block"])},[e("span",{class:U([A?"font-semibold":"font-normal","block truncate"])},p(s.name),3)],2)):$("",!0)])]),_:2},1032,["value"])),64))]),_:1})]),_:1})])]),_:1},8,["modelValue"]),v.value?(o(),i("button",{key:0,onClick:t[8]||(t[8]=s=>oe()),class:"rounded-lg bg-main-color-600 px-4 py-2 text-sm text-white shadow-sm hover:bg-main-color-700"}," Potvrdit ")):(o(),i("button",$t," Potvrdit "))])])])])])):(o(),S(F,{key:1,class:"mx-auto text-spinner-color",size:"40"}))])])]),B.value!==null?(o(),S(ge,{key:0,meta:B.value,onSetPage:se,modelValue:x.value,"onUpdate:modelValue":t[9]||(t[9]=s=>x.value=s)},null,8,["meta","modelValue"])):$("",!0)])]),n(je,{ref_key:"createTicketRef",ref:z,onReloadTickets:t[10]||(t[10]=s=>_())},null,512)],64)}}};export{It as default};
