import{a as i,j as G,o as s,c as M,w as c,e as u,h as L,d as t,u as p,b as r,v as k,D as S,k as N,q as A,I as Q,C as H,r as D,F as P,f as E,n as B,t as V,y as J,L as W,B as Y,z as T,a2 as ee,X as te,a6 as se}from"./index-ad469968.js";import{_ as oe}from"./AppTopbar-c961872c.js";import{t as X}from"./index-15865cd0.js";import{_ as ae}from"./pagination-85416e79.js";import{_ as O}from"./basicModal-5d7ba3ca.js";import{X as q,e as le,C as ne}from"./index-f6faa48e.js";import{S as F}from"./transition-335d453f.js";import{d as re}from"./debounce-5db41500.js";import{N as ie,$ as de,K as ue,U as me,_ as pe}from"./combobox-846ab255.js";import"./listbox-3e44478a.js";import"./hidden-f039557c.js";import"./use-tracked-pointer-2ea74235.js";import"./use-resolve-button-type-15920ae7.js";import"./use-controllable-b2bd8058.js";import"./dialog-8577fd56.js";import"./use-tree-walker-370a00c7.js";const ce={key:0,class:"p-6"},ve={class:"border-t p-5"},ge={class:"text-right space-x-3"},fe={__name:"removeUserModal",emits:["reloadUsers"],setup(Z,{expose:b,emit:U}){const C=U,h=i(!1);G("debugModeGlobalVar");const n=i(""),v=i("");i(!1);function m(){h.value=!1}async function f(g,a){h.value=!0,v.value=g,n.value=a}async function _(){await N.post("api/groups/"+v.value+"/delete-users",{users:n.value}).then(g=>{A.success(g.data.message)}).catch(g=>{console.log(g)}),m(),C("reloadUsers",!0)}return b({openModal:f}),(g,a)=>(s(),M(p(F),{appear:"",show:h.value,as:"template",onClose:a[3]||(a[3]=y=>m())},{default:c(()=>[u(O,{size:"sm"},{"modal-title":c(()=>a[4]||(a[4]=[L("Smazat uživatele")])),"modal-close-button":c(()=>[t("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:a[0]||(a[0]=y=>m())},[u(p(q),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":c(()=>[n.value?(s(),r("div",ce,a[5]||(a[5]=[t("span",null,"Opravdu si přejete uživatele ze skupiny smazat?",-1)]))):k("",!0),t("div",ve,[t("div",ge,[t("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:a[1]||(a[1]=S(y=>m(),["prevent"]))}," Zavřít "),t("button",{onClick:a[2]||(a[2]=S(y=>_(),["prevent"])),class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-red-600",type:"submit"}," Smazat ")])])]),_:1})]),_:1},8,["show"]))}},xe={class:"p-6"},ye={class:"w-full"},be={key:0},he={class:"relative mt-1"},_e={class:"relative w-full cursor-default overflow-hidden rounded-md bg-white border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 text-sm"},we={key:0},ke={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},$e={key:1,class:"h-20 max-h-20 overflow-hidden"},Ue={key:2},Ce={class:"border-t p-5"},ze={class:"text-right space-x-3"},Ve={__name:"createUserModal",emits:["reloadProtocols"],setup(Z,{expose:b,emit:U}){const C=U,h=i(!1);G("debugModeGlobalVar");const n=i(null);i(!1);const v=i([]),m=i(""),f=i(!1),_=i(""),g=Q(()=>m.value===""?v.value:v.value.filter(x=>`${x.first_name} ${x.last_name}`.toLowerCase().replace(/\s+/g,"").includes(m.value.toLowerCase().replace(/\s+/g,"")))),a=re(async()=>{try{const x=await N.get("/api/users?page=1&perpage=50&search="+m.value);v.value=x.data.data}catch(x){console.error(x)}finally{f.value=!1}},300);H(m,()=>{f.value=!0,a()});function y(){h.value=!1}async function I(x){await a(),h.value=!0,n.value=null,_.value=x}async function w(){const x={user_id:n.value.id};await N.post("api/groups/"+_.value+"/users",x).then(l=>{A.success(l.data.message)}).catch(l=>{console.log(l)}),y(),C("reloadUsers",!0)}return b({openModal:I}),(x,l)=>{const R=D("VueSpinner"),d=D("divk");return s(),M(p(F),{appear:"",show:h.value,as:"template",onClose:l[6]||(l[6]=e=>y())},{default:c(()=>[u(O,{size:"sm"},{"modal-title":c(()=>l[7]||(l[7]=[L("Přidání uživatele do skupiny")])),"modal-close-button":c(()=>[t("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:l[0]||(l[0]=e=>y())},[u(p(q),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":c(()=>[t("div",null,[t("div",xe,[u(d,null,{default:c(()=>[t("div",ye,[v.value?(s(),r("div",be,[l[8]||(l[8]=t("label",{class:"block text-sm font-normal leading-6 text-gray-900"},"Uživatel:",-1)),u(p(ie),{modelValue:n.value,"onUpdate:modelValue":l[3]||(l[3]=e=>n.value=e)},{default:c(()=>[t("div",he,[t("div",_e,[u(p(de),{class:"w-full border-none py-2 pl-4 pr-10 text-sm leading-5 text-gray-900 focus:ring-0 font-medium",placeholder:"Zvolte uživatele",displayValue:e=>e?`${e.first_name||""} ${e.middle_name?e.middle_name+" ":""}${e.last_name||""}`.trim():"",onChange:l[1]||(l[1]=e=>m.value=e.target.value)},null,8,["displayValue"]),u(p(ue),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:c(()=>[u(p(le),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),u(p(F),{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:l[2]||(l[2]=e=>m.value="")},{default:c(()=>[u(p(me),{class:"absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},{default:c(()=>[f.value?k("",!0):(s(),r("div",we,[g.value.length===0&&m.value!==""?(s(),r("div",ke," Žádný uživatel nenalezen. ")):k("",!0)])),f.value?(s(),r("div",$e,[u(R,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(s(),r("div",Ue,[(s(!0),r(P,null,E(g.value,e=>(s(),M(p(pe),{as:"template",key:e.id,value:e},{default:c(({active:$})=>{var j,z,o;return[t("li",{class:B(["relative cursor-default select-none py-2 pl-4 pr-10",{"bg-main-color-600 text-white":$,"text-gray-900":!$}])},[t("span",{class:B(["block truncate",{"font-medium":((j=n.value)==null?void 0:j.id)==e.id,"font-normal":((z=n.value)==null?void 0:z.id)!=e.id}])},V((e==null?void 0:e.first_name)+" "+(e!=null&&e.middle_name?(e==null?void 0:e.middle_name)+" ":"")+(e==null?void 0:e.last_name)),3),((o=n.value)==null?void 0:o.id)==e.id?(s(),r("span",{key:0,class:B(["absolute inset-y-0 right-0 flex items-center pr-3",{"text-white":$,"text-main-color-600":!$}])},[u(p(ne),{class:"h-5 w-5","aria-hidden":"true"})],2)):k("",!0)],2)]}),_:2},1032,["value"]))),128))]))]),_:1})]),_:1})])]),_:1},8,["modelValue"])])):k("",!0)])]),_:1})]),t("div",Ce,[t("div",ze,[t("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:l[4]||(l[4]=S(e=>y(),["prevent"]))}," Zavřít "),t("button",{onClick:l[5]||(l[5]=S(e=>w(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Přidat ")])])])]),_:1})]),_:1},8,["show"])}}},Me={key:0},Se={class:"space-y-6"},Ne={class:"px-0"},Re={class:"bg-white border border-zinc-200/70 rounded-md p-5"},je={class:"sm:flex justify-between items-center gap-4"},De={class:"w-80"},Ie={class:"flex items-center gap-4"},Be={class:"flow-root bg-white border border-zinc-200/70 rounded-md"},Pe={class:"sm:-mx-6 lg:-mx-8"},Fe={class:"inline-block min-w-full align-middle sm:px-6 lg:px-8"},Ge={key:0,class:"min-w-full divide-y divide-gray-200"},Le={scope:"col",class:"pl-5 pr-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"},Ze=["checked"],Ke={key:0,class:"divide-y divide-gray-200"},Te={class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600"},Xe=["value"],Ae={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},Ee={key:0},Oe={key:1},qe={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},Qe={key:0},He={key:1},Je={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},We={key:0},Ye={key:1},et={class:"text-end pr-5 w-40"},tt={key:1},st={key:2,class:"bg-gray-100/70"},ot={colspan:"10",class:"py-4 pl-5 px-3 text-sm text-gray-900 bg-gray-100/70"},at={class:"flex items-center gap-2"},_t={__name:"GroupUsers",setup(Z){const b=J();W();const U=i(""),C=i("");G("debugModeGlobalVar");const h=i(["group-users"]),n=i([]),v=i(!1),m=i(""),f=i(1),_=i({}),g=i({}),a=i({});Y(()=>{v.value=!0,I(),w()});function y(){n.value.length==a.value.length?n.value=[]:n.value=a.value.map(d=>d.id)}async function I(){await N.get("/api/groups/"+b.params.id).then(d=>{g.value=d.data.data}).catch(d=>{console.log(d)})}async function w(){v.value=!0,await N.get("/api/groups/"+b.params.id+"/users?page="+f.value+"&search="+m.value).then(d=>{a.value=d.data.data,_.value=d.data.meta}).catch(d=>{console.log(d)}),v.value=!1}function x(d){f.value=d,w()}function l(){v.value=!0,f.value=1,m.value="",w()}function R(){v.value=!0,f.value=1,w()}return(d,e)=>{var z;const $=D("spa"),j=D("VueSpinner");return s(),r(P,null,[g.value.name?(s(),r("div",Me,[u(oe,{breadCrumbs:h.value,customName:g.value.name||d.$route.meta.title,customDesc:g.value.description||null},{topbarButtons:c(()=>[t("button",{onClick:e[0]||(e[0]=o=>d.$refs.createUserRef.openModal(p(b).params.id)),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600"},e[11]||(e[11]=[t("span",null,"Přidat uživatele",-1)]))]),_:1},8,["breadCrumbs","customName","customDesc"])])):k("",!0),t("div",Se,[t("div",Ne,[t("div",Re,[t("div",je,[t("div",De,[T(t("input",{type:"text",name:"search",id:"search","onUpdate:modelValue":e[1]||(e[1]=o=>m.value=o),onKeyup:e[2]||(e[2]=te(o=>R(),["enter"])),class:"block w-full rounded-md py-1.5 text-gray-900 border border-gray-300 placeholder:text-gray-400 focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte hledaný výraz..."},null,544),[[ee,m.value]])]),t("div",Ie,[t("button",{onClick:e[3]||(e[3]=o=>l()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},e[12]||(e[12]=[t("span",null,"Resetovat",-1)])),t("button",{onClick:e[4]||(e[4]=o=>R()),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700"}," Filtrovat ")])])])]),t("div",null,[t("div",Be,[t("div",Pe,[t("div",Fe,[v.value==!1?(s(),r("table",Ge,[t("thead",null,[t("tr",null,[t("th",Le,[t("input",{checked:n.value.length==((z=a.value)==null?void 0:z.length),onClick:y,"aria-describedby":"comments-description",type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-indigo-600 ring-transparent focus:ring-transparent cursor-pointer"},null,8,Ze)]),e[13]||(e[13]=t("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Uživatel ",-1)),e[14]||(e[14]=t("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Email ",-1)),e[15]||(e[15]=t("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Organizační jednotka ",-1)),e[16]||(e[16]=t("th",{scope:"col",class:"pl-5 pr-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"},null,-1))])]),a.value&&a.value.length?(s(),r("tbody",Ke,[(s(!0),r(P,null,E(a.value,o=>(s(),r("tr",{key:o.id},[t("td",Te,[T(t("input",{value:o.id,"onUpdate:modelValue":e[5]||(e[5]=K=>n.value=K),id:"comments","aria-describedby":"comments-description",name:"comments",type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-transparent cursor-pointer"},null,8,Xe),[[se,n.value]])]),t("td",Ae,[o.full_name&&o.full_name.length>1?(s(),r("span",Ee,V(o.full_name),1)):(s(),r("span",Oe,V(o.account_name),1))]),t("td",qe,[o.email?(s(),r("span",Qe,V(o.email),1)):(s(),r("span",He,"-"))]),t("td",Je,[o.organization_unit?(s(),r("span",We,V(o.organization_unit.name),1)):(s(),r("span",Ye,"-"))]),t("td",et,[t("button",null,[u(p(X),{class:"h-9 w-9 text-red-500 bg-red-100 hover:bg-red-200/75 duration-150 p-2 rounded-md","aria-hidden":"true",onClick:K=>d.$refs.removeUserRef.openModal(p(b).params.id,[o.id])},null,8,["onClick"])])])]))),128))])):(s(),r("tbody",tt,e[17]||(e[17]=[t("tr",null,[t("td",{colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},"Nebyli nalezeni žádní uživatelé.")],-1)]))),n.value&&n.value.length?(s(),r("tfoot",st,[t("tr",null,[t("td",ot,[t("div",at,[t("button",{onClick:e[6]||(e[6]=o=>d.$refs.removeUserRef.openModal(p(b).params.id,n.value)),class:"rounded-md text-red-500 bg-red-100 hover:bg-red-200/75 duration-150 px-2.5 h-10 flex gap-1 justify-center items-center"},[u(p(X),{class:"h-5 w-5","aria-hidden":"true"}),u($,{class:"pl-2"},{default:c(()=>e[18]||(e[18]=[L("Smazat hromadně")])),_:1})]),t("button",{onClick:e[7]||(e[7]=S(o=>n.value=[],["prevent"])),class:"rounded-md bg-main-color-200/75 px-4 h-10 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},e[19]||(e[19]=[t("span",null,"Zrušit výběr",-1)]))])])])])):k("",!0)])):(s(),M(j,{key:1,class:"mx-auto text-spinner-color",size:"40"}))])])]),_.value!==null?(s(),M(ae,{key:0,meta:_.value,onSetPage:x,modelValue:f.value,"onUpdate:modelValue":e[8]||(e[8]=o=>f.value=o)},null,8,["meta","modelValue"])):k("",!0)])]),u(fe,{ref_key:"removeUserRef",ref:U,size:"sm",onReloadUsers:e[9]||(e[9]=o=>w())},null,512),u(Ve,{ref_key:"createUserRef",ref:C,size:"sm",onReloadUsers:e[10]||(e[10]=o=>w())},null,512)],64)}}};export{_t as default};
