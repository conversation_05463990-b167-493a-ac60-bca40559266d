import{y as te,a as d,o as n,c as b,w as u,d as e,e as a,u as s,s as p,E as x,x as se,h as E,k as w,q as Y,j as ge,B as he,r as ye,b as i,t as c,v as m,F as q,P as T,T as N,f as R,n as z}from"./index-3de9bee7.js";import{_ as fe}from"./AppTopbar-27ebc835.js";import{i as ae,_ as xe,h as be,B as J,k as K,C as A,U as X,l as ke,J as we,K as Ve}from"./index-ddf5f523.js";import{S as Ce}from"./vue-tailwind-datepicker-96876598.js";import{s as Ue}from"./default.css_vue_type_style_index_1_src_true_lang-cb98c754.js";import{c as B}from"./checkPermission.service-9981644d.js";import{E as Q,A as W,F as ee,B as O}from"./listbox-3a5ebdfc.js";import"./hidden-7b234e84.js";import"./use-tracked-pointer-ed292bdb.js";import"./use-resolve-button-type-8b834d9a.js";import"./use-controllable-4f60503e.js";const je={class:"space-y-4 bg-white border border-zinc-200/70 rounded-md p-5"},ze={class:"flex justify-between items-center"},$e={class:"flex items-center"},Pe=e("p",{class:"ml-4 text-lg text-gray-900"},"Změna hesla",-1),Ze=e("button",{type:"submit",class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},"Uložit ",-1),Se=e("label",{for:"userPassword",class:"block text-sm font-normal leading-6 text-gray-900"},"Heslo:",-1),Ee={class:"mt-2"},De={class:"grid grid-cols-12 items-end gap-4 border-b pb-8 mb-6"},Me={class:"col-span-12 sm:col-span-8"},Be=e("label",{for:"userPasswordConfirmation",class:"block text-sm font-normal leading-6 text-gray-900"},"Opakovat heslo:",-1),Ie={class:"mt-2"},Le={class:"col-span-12 sm:col-span-4 mb-2"},qe={class:"flex h-6 md:justify-end md:items-center"},Ae=["checked"],Oe=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"sendSms",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Zaslat do SMS")],-1),Ye={class:"col-span-12 sm:col-span-12 mb-2"},Ge={class:"flex h-6 justify-start items-center"},He=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"mustChangePassword",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Vyžádat změnu hesla po přihlášení")],-1),Fe=e("div",null,[e("span",{class:"text-sm font-semibold inline-block pb-4"},"Požadavky na nové heslo:"),e("ul",{class:"list-disc ml-6 text-sm space-y-1"},[e("li",null,[E("Heslo musí mít alespoň "),e("span",null,"8 znaků")]),e("li",null,"Doporučujeme využít kombinaci malých a velkých písmen"),e("li",null,"Doporučujeme využít v hesle i číslici")])],-1),Te={__name:"usersEditChangeAdPassword",setup(oe){const I=te();d({});const V=d(null),$=d(null),h=d(0),t=d(0);function y(){h.value=!h.value}function L(){w.post("/api/users/"+I.params.id+"/ad-password-update",{password:V.value,password_confirmation:$.value,must_change_password:t.value,send_sms:h.value,show_password:1}).then(k=>{Y.success(k.data.message),getTableData(),closeCreateUserModal()}).catch(k=>{console.log(k)})}const D=d([{id:1,name:"A"},{id:2,name:"B"},{id:3,name:"C"},{id:4,name:"D"},{id:5,name:"E"}]);return d(D.value[0]),(k,g)=>(n(),b(s(se),{onSubmit:L,class:"col-span-8 md:col-span-4 order-2 2xl:order-3 2xl:col-span-3"},{default:u(({values:P})=>[e("div",je,[e("div",ze,[e("div",$e,[a(s(ae),{class:"w-7"}),Pe]),Ze]),e("div",null,[Se,e("div",Ee,[a(s(p),{rules:"required|password",modelValue:V.value,"onUpdate:modelValue":g[0]||(g[0]=f=>V.value=f),id:"userPassword",name:"userPassword",type:"password",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte heslo..."},null,8,["modelValue"]),a(s(x),{name:"userPassword",class:"text-rose-400 text-sm block pt-1"})])]),e("div",De,[e("div",Me,[Be,e("div",Ie,[a(s(p),{rules:"required|password|isEqual:"+V.value,modelValue:$.value,"onUpdate:modelValue":g[1]||(g[1]=f=>$.value=f),id:"userPasswordConfirmation",name:"userPasswordConfirmation",type:"password",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Opakujte heslo..."},null,8,["rules","modelValue"]),a(s(x),{name:"userPasswordConfirmation",class:"text-rose-400 text-sm block pt-1"})])]),e("div",Le,[e("div",qe,[e("input",{id:"sendSms","aria-describedby":"sendSms",name:"sendSms",onClick:g[2]||(g[2]=f=>y()),type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",checked:h.value},null,8,Ae),Oe])]),e("div",Ye,[e("div",Ge,[a(s(p),{id:"mustChangePassword","aria-describedby":"mustChangePassword",name:"mustChangePassword",modelValue:t.value,"onUpdate:modelValue":g[3]||(g[3]=f=>t.value=f),value:!t.value,type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"]),He])])]),Fe])]),_:1}))}};const Ne={key:0,type:"submit",class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},Re={class:"md:flex justify-between items-center"},Je={class:"pb-6 pl-2 flex items-center gap-6"},Ke={class:"text-xl"},Xe={key:0},Qe={key:1},We={key:2},et={key:0,class:"flex gap-2 items-center"},tt={class:"bg-main-color-100 rounded-full p-1"},st=e("span",{class:"text-xs text-main-color-600"},"Lokální účet",-1),at={class:"flex"},ot={class:"mb-6 sm:border sm:border-zinc-200/70 rounded-md sm:flex gap-x-0.5 sm:bg-zinc-200/70 space-y-2 sm:space-y-0"},lt=e("span",{class:"flex items-center"},"Základní informace ",-1),nt={class:"flex items-center"},rt=e("p",{class:"ml-2"},"Role a oprávnění",-1),it={class:"grid grid-cols-8 gap-6"},dt={class:"col-span-8 md:col-span-4 order-1 2xl:order-1 2xl:col-span-3"},ct={class:"space-y-4 bg-white border border-zinc-200/70 rounded-md p-5"},ut={class:"flex items-center"},mt=e("p",{class:"ml-4 text-lg text-gray-900"},"Základní informace",-1),pt={class:"grid grid-cols-1 sm:grid-cols-2 gap-6 gap-y-4"},_t=e("label",{for:"first-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Jméno:",-1),vt={class:"mt-2"},gt=e("label",{for:"last-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Příjmení:",-1),ht={class:"mt-2"},yt=e("label",{for:"last-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Telefonní číslo:",-1),ft={class:"mt-2"},xt=e("label",{for:"new-email",class:"block text-sm font-normal leading-6 text-gray-900"},"Emailová adresa:",-1),bt={class:"mt-2"},kt={class:"grid grid-cols-12 items-end gap-4"},wt={class:"col-span-12 sm:col-span-8"},Vt=e("label",{for:"new-email-confirmation",class:"block text-sm font-normal leading-6 text-gray-900"},"Opakovat email:",-1),Ct={class:"mt-2"},Ut={class:"col-span-12 sm:col-span-4 mb-2"},jt={class:"flex h-6 md:justify-end md:items-center"},zt=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"verifiedEmail",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Ověřený email")],-1),$t={key:0,class:"col-span-8 md:col-span-4 order-3 2xl:order-2 2xl:col-span-2"},Pt={class:"bg-white border border-zinc-200/70 rounded-md p-5"},Zt={key:0,class:"border-b pb-6"},St={class:"flex items-center my-4"},Et=e("p",{class:"ml-4 text-lg text-gray-900"},"Zařazení do OU",-1),Dt={class:"relative mt-1"},Mt={key:0,class:"block truncate"},Bt={key:1,class:"block truncate text-gray-400"},It={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},Lt={key:0,class:"absolute inset-y-0 left-0 flex items-center pl-3 text-main-color-600"},qt={key:1},At={class:"flex items-center my-4"},Ot=e("p",{class:"ml-4 text-lg text-gray-900"},"Zařazení do skupin",-1),Yt={key:2,class:"space-y-4 border-t pt-6 mt-6"},Gt={class:"flex items-center"},Ht=e("p",{class:"ml-4 text-lg text-gray-900"},"Host / expirace účtu",-1),Ft={class:"flex h-6 justify-start items-center"},Tt=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"visitor",class:"text-gray-900 cursor-pointer text-sm"},[E("Uživatelský účet je "),e("strong",null,"účet hosta")])],-1),Nt={class:"rounded-l-full w-full"},Rt={type:"button",class:"rounded-md border border-gray-300 block font-normal px-2.5 py-1.5 w-full text-sm leading-6 items-center justify-center space-x-2 sm:space-x-4 transition ease-out duration-300"},Jt={class:"flex items-center w-full"},Kt={class:"flex-1 text-left"},Xt={key:0,class:"text-gray-900"},Qt={key:1,class:"text-gray-400 text-sm"},Wt={class:"w-6 h-6"},es={class:"flex gap-2"},ts=e("span",{class:"font-light text-sm"},"V případě nenastavení data, bude účet neomezeně platný.",-1),ss={key:0,class:"bg-white border border-zinc-200/70 rounded-md p-5 mt-6"},as={class:"flex items-center my-4"},os=e("p",{class:"ml-4 text-lg text-gray-900"},"Doplňující informace",-1),ls={class:"grid grid-cols-3 gap-4"},ns={class:"col-span-3 lg:col-span-1"},rs=e("label",{for:"profile_path",class:"block text-sm font-normal leading-6 text-gray-900"},"Cesta k profilu:",-1),is={class:"mt-2"},ds={class:"col-span-3 lg:col-span-1"},cs=e("label",{for:"script_path",class:"block text-sm font-normal leading-6 text-gray-900"},"Logon script:",-1),us={class:"mt-2"},ms={class:"col-span-3 lg:col-span-1 flex items-end gap-4"},ps={class:"grow"},_s=e("label",{for:"home_directory",class:"block text-sm font-normal leading-6 text-gray-900"},"Domovská složka:",-1),vs={class:"mt-2"},gs={class:"flex items-center"},hs={class:"relative mt-1"},ys={key:0,class:"block truncate"},fs={key:1,class:"block truncate text-gray-400"},xs={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},bs={key:0,class:"absolute inset-y-0 right-0 flex items-center pl-3 text-main-color-600"},ks={key:0,class:"absolute inset-y-0 left-0 flex items-center pl-3 text-main-color-600"},ws={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block mt-6"},Vs=e("span",null,"New password styles",-1),Cs=e("br",null,null,-1),Us=e("br",null,null,-1),js=e("br",null,null,-1),zs=e("br",null,null,-1),$s=e("br",null,null,-1),Ps=e("br",null,null,-1),Zs=e("br",null,null,-1),Ss=e("br",null,null,-1),Es=e("br",null,null,-1),Ds=e("br",null,null,-1),Ms=e("br",null,null,-1),Bs=e("br",null,null,-1),Rs={__name:"UsersEdit",setup(oe){const I=ge("debugModeGlobalVar"),V=d(["skolasys-root","users-edit"]),$=d(),h=te(),t=d({}),y=d(),L=d({}),D=d({}),k=d({}),g=d(L.value.id),P=d("email"),f=d([]),le=d([]),ne=d(null),re=d(null),ie=d(0);he(()=>{de(),ce(),me(),ue()});function de(){w.get("/api/users/"+h.params.id).then(r=>{t.value=r.data.data,t.value.email_confirmation=r.data.data.email,t.value.email_verified_at?y.value=!0:y.value=!1,t.value.active_directory==1?(ve(),P.value="email"):P.value="required|email"}).catch(r=>{console.log(r)})}function ce(){w.get("/api/organization-units?perpage=9999").then(r=>{D.value=r.data.data,g.value=r.data.data[0]}).catch(r=>{console.log(r)})}function ue(){w.get("/api/account-control-codes?listing=1").then(r=>{k.value=r.data.data}).catch(r=>{console.log(r)})}function me(){w.get("/api/groups?perpage=9999").then(r=>{f.value=r.data.data}).catch(r=>{console.log(r)})}function pe(){var r,l,U,M,o,_,v,Z,H,F;if(t.value.active_directory==1){let j={first_name:t.value.first_name,last_name:t.value.last_name,email:t.value.email,email_verified:y.value,phone:t.value.phone,organization_unit:((U=(l=(r=t.value)==null?void 0:r.active_directory_data)==null?void 0:l.organization_unit)==null?void 0:U.id)||null,visitor:t.value.active_directory_data.visitors,groups:t.value.active_directory_data.groups.map(S=>S.id),visitor:t.value.active_directory_data.visitor,profile_path:((o=(M=t.value)==null?void 0:M.active_directory_data)==null?void 0:o.profile_path)||null,script_path:((v=(_=t.value)==null?void 0:_.active_directory_data)==null?void 0:v.script_path)||null,home_directory:((H=(Z=t.value)==null?void 0:Z.active_directory_data)==null?void 0:H.home_directory)||null,home_drive:((F=C.value)==null?void 0:F.name)||null};t.value.active_directory_data.expire_date&&t.value.active_directory_data.expire_date[0]&&t.value.active_directory_data.expire_date[0].length&&(j.expire=T(t.value.active_directory_data.expire_date[0]).format("YYYY-MM-DD HH:MM")),w.post("/api/users/"+h.params.id+"/ad-update",j).then(S=>{Y.success(S.data.message)}).catch(S=>{console.log(S)})}else w.post("/api/users/"+h.params.id+"/update",{first_name:t.value.first_name,last_name:t.value.last_name,email:t.value.email,email_verified:y.value,phone:t.value.phone}).then(j=>{Y.success(j.data.message)}).catch(j=>{console.log(j)})}function _e(){t.value.active_directory_data.visitor=!t.value.active_directory_data.visitor}const G=d(Array.from({length:26},(r,l)=>({id:l+1,name:String.fromCharCode(65+l)+":"}))),C=d(null);function ve(){t.value.active_directory_data.home_drive&&(C.value=G.value.find(r=>r.name==t.value.active_directory_data.home_drive))}return(r,l)=>{const U=ye("router-link");return n(),i(q,null,[a(s(se),{onSubmit:l[16]||(l[16]=M=>pe())},{default:u(({values:M})=>[a(fe,{breadCrumbs:V.value},{topbarButtons:u(()=>[a(U,{to:{name:"users"},class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200 flex items-center"},{default:u(()=>[E(" Zpět ")]),_:1}),s(B).check("users.edit")?(n(),i("button",Ne,"Uložit ")):m("",!0)]),_:1},8,["breadCrumbs"]),e("div",Re,[e("div",Je,[e("h2",Ke,[t.value.first_name?(n(),i("span",Xe,c(t.value.first_name+" "),1)):m("",!0),t.value.midle_name?(n(),i("span",Qe,c(t.value.midle_name+" "),1)):m("",!0),t.value.last_name?(n(),i("span",We,c(t.value.last_name),1)):m("",!0)]),t.value.active_directory==0?(n(),i("div",et,[e("span",tt,[a(s(xe),{class:"h-4 w-4 text-main-color-600","aria-hidden":"true"})]),st])):m("",!0)]),e("div",at,[e("div",ot,[a(U,{class:"flex rounded-md sm:rounded-r-none sm:rounded-t-none sm:rounded-b-none sm:rounded-l-md px-8 h-10 text-center text-sm font-medium text-gray-900 bg-white border-b-2 border-zinc-200 sm:border-white hover:bg-gray-50 hover:border-gray-50","exact-active-class":"!border-main-color-600",to:{name:"users-edit",params:{id:s(h).params.id}}},{default:u(()=>[lt]),_:1},8,["to"]),s(B).check("users.read_roles_permissions")&&s(B).check("roles.read")&&s(B).check("permissions.read")?(n(),b(U,{key:0,class:"flex rounded-md sm:rounded-l-none sm:rounded-t-none sm:rounded-b-none sm:rounded-r-md px-8 h-10 text-center text-sm font-medium text-gray-900 bg-white border-b-2 border-zinc-200 sm:border-white hover:bg-gray-50 hover:border-gray-50","exact-active-class":"!border-main-color-600",to:{name:"users-roles-permissions",params:{id:s(h).params.id}}},{default:u(()=>[e("span",nt,[a(s(ae),{class:"h-4"}),rt])]),_:1},8,["to"])):m("",!0)])])]),e("div",it,[e("div",dt,[e("div",ct,[e("div",ut,[a(s(be),{class:"w-7"}),mt]),e("div",pt,[e("div",null,[_t,e("div",vt,[a(s(p),{rules:"required|minLength:2|maxLength:50",modelValue:t.value.first_name,"onUpdate:modelValue":l[0]||(l[0]=o=>t.value.first_name=o),type:"text",name:"first-name",id:"first-name",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte jméno..."},null,8,["modelValue"]),a(s(x),{name:"first-name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[gt,e("div",ht,[a(s(p),{rules:"required|minLength:2|maxLength:50",modelValue:t.value.last_name,"onUpdate:modelValue":l[1]||(l[1]=o=>t.value.last_name=o),type:"text",name:"last-name",id:"last-name",autocomplete:"family-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte Příjmení..."},null,8,["modelValue"]),a(s(x),{name:"last-name",class:"text-rose-400 text-sm block pt-1"})])])]),e("div",null,[yt,e("div",ft,[a(s(p),{rules:"phone",modelValue:t.value.phone,"onUpdate:modelValue":l[2]||(l[2]=o=>t.value.phone=o),type:"tel",name:"phone",id:"phone",autocomplete:"phone",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte tel. číslo..."},null,8,["modelValue"]),a(s(x),{name:"phone",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[xt,e("div",bt,[a(s(p),{rules:P.value,modelValue:t.value.email,"onUpdate:modelValue":l[3]||(l[3]=o=>t.value.email=o),id:"new-email",name:"new-email",type:"email",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte emailovou adresu..."},null,8,["rules","modelValue"]),a(s(x),{name:"new-email",class:"text-rose-400 text-sm block pt-1"})])]),e("div",kt,[e("div",wt,[Vt,e("div",Ct,[a(s(p),{rules:P.value+"|isEqual:"+t.value.email,modelValue:t.value.email_confirmation,"onUpdate:modelValue":l[4]||(l[4]=o=>t.value.email_confirmation=o),id:"new-email-confirmation",name:"new-email-confirmation",type:"email",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Opakujte emailovou adresu..."},null,8,["rules","modelValue"]),a(s(x),{name:"new-email-confirmation",class:"text-rose-400 text-sm block pt-1"})])]),e("div",Ut,[e("div",jt,[a(s(p),{id:"verifiedEmail","aria-describedby":"verifiedEmail",name:"verifiedEmail",modelValue:y.value,"onUpdate:modelValue":l[5]||(l[5]=o=>y.value=o),value:!y.value,type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"]),zt])])])])]),t.value.active_directory==1?(n(),i("div",$t,[e("div",Pt,[t.value.active_directory==1?(n(),i("div",Zt,[e("div",St,[a(s(J),{class:"w-7"}),Et]),a(s(p),{rules:"required",modelValue:t.value.active_directory_data.organization_unit,"onUpdate:modelValue":l[6]||(l[6]=o=>t.value.active_directory_data.organization_unit=o),type:"text",name:"selected_ou",id:"selected_ou",class:"hidden"},null,8,["modelValue"]),a(s(Q),{modelValue:t.value.active_directory_data.organization_unit,"onUpdate:modelValue":l[7]||(l[7]=o=>t.value.active_directory_data.organization_unit=o)},{default:u(()=>[e("div",Dt,[a(s(W),{class:"relative w-full text-left cursor-pointer block rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},{default:u(()=>{var o,_,v,Z;return[(_=(o=t.value.active_directory_data)==null?void 0:o.organization_unit)!=null&&_.name?(n(),i("span",Mt,c((Z=(v=t.value.active_directory_data)==null?void 0:v.organization_unit)==null?void 0:Z.name),1)):(n(),i("span",Bt,"Vyberte organizační jednotku...")),e("span",It,[a(s(K),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]}),_:1}),a(N,{"leave-active-class":"transition duration-100 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:u(()=>[a(s(ee),{class:"absolute z-20 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:u(()=>[(n(!0),i(q,null,R(D.value,o=>(n(),b(s(O),{key:o.id,value:o,as:"template"},{default:u(({active:_,selected:v})=>[e("li",{class:z([_?"bg-main-color-100 text-main-color-900":"text-gray-900","relative cursor-pointer select-none py-2 pl-10 pr-4"])},[e("span",{class:z([v?"font-medium":"font-normal","block truncate"])},c(o.name),3),v?(n(),i("span",Lt,[a(s(A),{class:"h-5 w-5","aria-hidden":"true"})])):m("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})]),_:1})])]),_:1},8,["modelValue"]),a(s(x),{name:"selected_ou",class:"text-rose-400 text-sm block pt-1"})])):m("",!0),t.value.active_directory==1?(n(),i("div",qt,[e("div",At,[a(s(X),{class:"w-7"}),Ot]),a(s(p),{name:"selectedGroups",value:t.value.active_directory_data.groups},{default:u(({handleChange:o,value:_})=>[a(s(Ue),{name:"selectedGroups",modelValue:t.value.active_directory_data.groups,"onUpdate:modelValue":[l[8]||(l[8]=v=>t.value.active_directory_data.groups=v),o],mode:"tags",label:"name","value-prop":"id",options:f.value,object:!0,placeholder:"Zvolte si skupiny",class:"!rounded-lg"},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1},8,["value"]),a(s(x),{name:"selectedGroups",class:"text-rose-400 text-sm block pt-1"})])):m("",!0),t.value.active_directory==1?(n(),i("div",Yt,[e("div",Gt,[a(s(X),{class:"w-7"}),Ht]),e("div",Ft,[a(s(p),{id:"visitor","aria-describedby":"visitor",name:"visitor",onClick:l[9]||(l[9]=o=>_e()),type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",modelValue:t.value.active_directory_data.visitor,"onUpdate:modelValue":l[10]||(l[10]=o=>t.value.active_directory_data.visitor=o),value:!t.value.active_directory_data.visitor},null,8,["modelValue","value"]),Tt]),e("div",null,[a(s(Ce),{i18n:"cs","as-single":"",shortcuts:!1,modelValue:t.value.active_directory_data.expire_date,"onUpdate:modelValue":l[11]||(l[11]=o=>t.value.active_directory_data.expire_date=o),placeholder:"Zvolte datum, do kdy je účet aktivní"},{default:u(({clear:o})=>[e("div",null,[e("div",Nt,[e("button",Rt,[e("div",Jt,[e("div",Kt,[t.value.active_directory_data.expire_date&&t.value.active_directory_data.expire_date.length?(n(),i("span",Xt,[e("span",null,c(s(T)(t.value.active_directory_data.expire_date[0]).format("DD.MM.YYYY")),1)])):(n(),i("span",Qt,"Zvolte datum, do kdy je účet aktivní"))]),e("div",Wt,[t.value.active_directory_data.expire_date&&t.value.active_directory_data.expire_date.length?(n(),b(s(ke),{key:0,onClick:o,class:"h-6 w-6 text-gray-900","aria-hidden":"true"},null,8,["onClick"])):(n(),b(s(we),{key:1,class:"h-6 w-6 text-gray-900","aria-hidden":"true"}))])])])])])]),_:1},8,["modelValue"])]),e("div",es,[e("div",null,[a(s(Ve),{class:"h-5 w-5 text-main-color-600","aria-hidden":"true"})]),ts])])):m("",!0)])])):m("",!0),t.value.active_directory==1?(n(),b(Te,{key:1})):m("",!0)]),t.value.active_directory==1?(n(),i("div",ss,[e("div",as,[a(s(J),{class:"w-7"}),os]),e("div",ls,[e("div",ns,[rs,e("div",is,[a(s(p),{modelValue:t.value.active_directory_data.profile_path,"onUpdate:modelValue":l[12]||(l[12]=o=>t.value.active_directory_data.profile_path=o),type:"text",name:"profile_path",id:"profile_path",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte cestu k profilu..."},null,8,["modelValue"])])]),e("div",ds,[cs,e("div",us,[a(s(p),{modelValue:t.value.active_directory_data.script_path,"onUpdate:modelValue":l[13]||(l[13]=o=>t.value.active_directory_data.script_path=o),type:"text",name:"script_path",id:"script_path",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte logon script..."},null,8,["modelValue"])])]),e("div",ms,[e("div",ps,[_s,e("div",vs,[a(s(p),{modelValue:t.value.active_directory_data.home_directory,"onUpdate:modelValue":l[14]||(l[14]=o=>t.value.active_directory_data.home_directory=o),type:"text",name:"home_directory",id:"home_directory",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte cestu domovské s..."},null,8,["modelValue"])])]),e("div",gs,[a(s(Q),{modelValue:C.value,"onUpdate:modelValue":l[15]||(l[15]=o=>C.value=o)},{default:u(()=>[e("div",hs,[a(s(W),{class:"relative w-16 text-left cursor-pointer block rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},{default:u(()=>[C.value?(n(),i("span",ys,c(C.value.name),1)):(n(),i("span",fs,"Zvolte disk...")),e("span",xs,[a(s(K),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]),_:1}),a(N,{"leave-active-class":"transition duration-100 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:u(()=>[a(s(ee),{class:"absolute right-0 mt-1 max-h-60 w-46 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:u(()=>[(n(),b(s(O),{key:"uncheck",value:null,as:"template"},{default:u(({active:o,selected:_})=>[e("li",{class:z([o?"bg-main-color-100 text-main-color-900":"text-gray-900","relative select-none py-2 pl-4 pr-10 cursor-pointer"])},[e("span",{class:z([_?"font-medium":"font-normal","block truncate"])},"Žádný",2),_?(n(),i("span",bs,[a(s(A),{class:"h-5 w-5","aria-hidden":"true"})])):m("",!0)],2)]),_:1})),(n(!0),i(q,null,R(G.value,o=>(n(),b(s(O),{key:o.name,value:o,as:"template"},{default:u(({active:_,selected:v})=>[e("li",{class:z([_?"bg-main-color-100 text-main-color-900":"text-gray-900","relative select-none py-2 pr-10 pl-4 cursor-pointer"])},[e("span",{class:z([v?"font-medium":"font-normal","block truncate"])},c(o.name),3),v?(n(),i("span",ks,[a(s(A),{class:"h-5 w-5","aria-hidden":"true"})])):m("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})]),_:1})])]),_:1},8,["modelValue"])])])])])):m("",!0)]),_:1}),s(I)?(n(),i("div",ws,[Vs,Cs,e("span",null,c(ne.value),1),Us,e("span",null,c(re.value),1),js,e("span",null,c(ie.value),1),zs,$s,e("span",null,[E("user: "),e("pre",null,c(t.value),1)]),Ps,e("span",null,"selected account type: "+c(r.selectedAccountType),1),Zs,e("span",null,"verified email: "+c(y.value),1),Ss,e("span",null,"send sms: "+c(r.sendSms),1),Es,e("span",null,"selected ou: "+c(g.value),1),Ds,e("span",null,[E("groups: "),e("pre",null,c(le.value),1)]),Ms,e("span",null,"account control codes: "+c(k.value),1),Bs,e("span",null,"selected account control code: "+c($.value),1)])):m("",!0)],64)}}};export{Rs as default};
