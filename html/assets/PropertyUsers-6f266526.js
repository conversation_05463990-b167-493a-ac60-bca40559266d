import{L as j,j as B,a as i,I as S,B as $,k as _,r as M,o as a,b as o,e as y,w as h,d as e,z as N,a2 as D,X as F,n as g,u as x,F as f,f as L,c as w,v as E,t as k,C as I}from"./index-68b8ac03.js";import{_ as R}from"./AppTopbar-8e1085f0.js";import{a0 as A}from"./index-67bf9347.js";import{_ as K}from"./pagination-e4e83c1d.js";import{a as T}from"./switch-4b45f5b5.js";import"./index-034b8efa.js";import"./listbox-b79c8b7a.js";import"./hidden-de3fbc3d.js";import"./use-tracked-pointer-c58e5566.js";import"./use-resolve-button-type-ab764b96.js";import"./use-controllable-d8e20660.js";const G={class:"space-y-6"},J={class:"px-0"},O={class:"bg-white border border-zinc-200/70 rounded-md p-5"},X={class:"sm:flex justify-between items-center gap-4"},Z={class:"flex items-center gap-y-4 gap-x-10"},q={class:"w-80"},H={class:"flex items-center gap-4"},Q={class:"flex items-center gap-4"},W={class:"flow-root bg-white border border-zinc-200/70 rounded-md"},Y={class:"sm:-mx-6 lg:-mx-8"},ee={class:"inline-block min-w-full align-middle sm:px-6 lg:px-8"},te={key:0,class:"min-w-full divide-y divide-gray-200"},se={key:0,class:"divide-y divide-gray-200"},ae={class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600"},oe={key:0},ne={key:1},le={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},re={key:0},ie={key:1},de={class:"text-end pr-5 w-40"},ue=["onClick"],ce={key:1},me={colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},pe={key:0},ve={key:1},Ue={__name:"PropertyUsers",setup(ye){const z=j();B("debugModeGlobalVar");const V=i(["property-users"]),d=i(!1),c=i(""),l=i(1),p=i({}),r=i(0),m=i({});S(()=>{u()}),$(()=>z.perPage,(n,t)=>{d.value=!0,l.value=1,u()});async function u(){d.value=!0,await _.get("/api/users/property?page="+l.value+"&search="+c.value+"&orderby=last_name&order=desc&deleted="+(r.value?1:0)).then(n=>{m.value=n.data.data,p.value=n.data.meta}).catch(n=>{console.log(n)}),d.value=!1}function C(n){_.post("api/users/property/generate-pdf",{user_id:n},{responseType:"blob"}).then(t=>{const v=window.URL.createObjectURL(new Blob([t.data])),s=document.createElement("a");s.href=v,s.setAttribute("download","Protokol.pdf"),document.body.appendChild(s),s.click()}).catch(t=>{console.error("Chyba při získávání souboru:",t)})}function P(n){l.value=n,u()}function U(){d.value=!0,l.value=1,c.value="",r.value=!1,u()}function b(){d.value=!0,l.value=1,u()}return(n,t)=>{const v=M("VueSpinner");return a(),o(f,null,[y(R,{breadCrumbs:V.value},{topbarButtons:h(()=>t[6]||(t[6]=[])),_:1},8,["breadCrumbs"]),e("div",G,[e("div",J,[e("div",O,[e("div",X,[e("div",Z,[e("div",q,[N(e("input",{type:"text",name:"search",id:"search","onUpdate:modelValue":t[0]||(t[0]=s=>c.value=s),onKeyup:t[1]||(t[1]=F(s=>b(),["enter"])),class:"block w-full rounded-md py-1.5 text-gray-900 border border-gray-300 placeholder:text-gray-400 focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte hledaný výraz..."},null,544),[[D,c.value]])]),e("div",H,[y(x(T),{modelValue:r.value,"onUpdate:modelValue":t[2]||(t[2]=s=>r.value=s),class:g([r.value?"bg-indigo-600":"bg-gray-200","relative inline-flex h-5 w-9 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out"])},{default:h(()=>[e("span",{"aria-hidden":"true",class:g([r.value?"translate-x-4":"translate-x-0","pointer-events-none inline-block h-4 w-4 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"])},null,2)]),_:1},8,["modelValue","class"]),t[7]||(t[7]=e("p",{class:"text-sm"},"Pouze smazaní uživatele s přiřazeným majetkem",-1))])]),e("div",Q,[e("button",{onClick:t[3]||(t[3]=s=>U()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},t[8]||(t[8]=[e("span",null,"Resetovat",-1)])),e("button",{onClick:t[4]||(t[4]=s=>b()),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700"}," Filtrovat ")])])])]),e("div",null,[e("div",W,[e("div",Y,[e("div",ee,[d.value==!1?(a(),o("table",te,[t[9]||(t[9]=e("thead",null,[e("tr",null,[e("th",{scope:"col",class:"pl-5 pr-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Jméno uživatele "),e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Email uživatele "),e("th",{scope:"col",class:"pl-5 pr-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"})])],-1)),m.value&&m.value.length?(a(),o("tbody",se,[(a(!0),o(f,null,L(m.value,s=>(a(),o("tr",{key:s.id,class:g(s.deleted_at?"bg-red-100/70":"")},[e("td",ae,[s.first_name||s.last_name?(a(),o("span",oe,k(s.first_name+" "+s.last_name),1)):(a(),o("span",ne,"-"))]),e("td",le,[s.email?(a(),o("span",re,k(s.email),1)):(a(),o("span",ie,"-"))]),e("td",de,[e("button",{onClick:I(ge=>C(s.id),["prevent"])},[y(x(A),{class:"h-8 w-8 text-white bg-main-color-600 hover:bg-main-color-700 duration-150 p-2 rounded-lg","aria-hidden":"true"})],8,ue)])],2))),128))])):(a(),o("tbody",ce,[e("tr",null,[e("td",me,[r.value?(a(),o("span",ve,"Nebyl nalezen žádný smazaný uživatel s přiřazeným majetkem a zadaným filtrem.")):(a(),o("span",pe,"Nebyli nalezeni žádní uživatelé."))])])]))])):(a(),w(v,{key:1,class:"mx-auto text-spinner-color",size:"40"}))])])]),p.value!==null?(a(),w(K,{key:0,meta:p.value,onSetPage:P,modelValue:l.value,"onUpdate:modelValue":t[5]||(t[5]=s=>l.value=s)},null,8,["meta","modelValue"])):E("",!0)])])],64)}}};export{Ue as default};
