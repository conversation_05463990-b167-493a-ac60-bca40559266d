import{t as V,o as D,f as E,K,b as $,H as j,T as B,a as p}from"./hidden-f039557c.js";import{b as L}from"./use-resolve-button-type-15920ae7.js";import{d as F,p as G}from"./use-controllable-b2bd8058.js";import{G as H,j as I,I as r,a as O,B as P,C as R,a9 as c,F as T}from"./index-ad469968.js";let A=Symbol("GroupContext"),N=H({name:"Switch",emits:{"update:modelValue":a=>!0},props:{as:{type:[Object,String],default:"button"},modelValue:{type:Boolean,default:void 0},defaultChecked:{type:Boolean,optional:!0},name:{type:String,optional:!0},value:{type:String,optional:!0},id:{type:String,default:()=>`headlessui-switch-${V()}`}},inheritAttrs:!1,setup(a,{emit:f,attrs:d,slots:m,expose:v}){let l=I(A,null),[n,i]=F(r(()=>a.modelValue),e=>f("update:modelValue",e),r(()=>a.defaultChecked));function s(){i(!n.value)}let h=O(null),u=l===null?h:l.switchRef,y=L(r(()=>({as:a.as,type:d.type})),u);v({el:u,$el:u});function b(e){e.preventDefault(),s()}function k(e){e.key===p.Space?(e.preventDefault(),s()):e.key===p.Enter&&G(e.currentTarget)}function C(e){e.preventDefault()}let o=r(()=>{var e,t;return(t=(e=D(u))==null?void 0:e.closest)==null?void 0:t.call(e,"form")});return P(()=>{R([o],()=>{if(!o.value||a.defaultChecked===void 0)return;function e(){i(a.defaultChecked)}return o.value.addEventListener("reset",e),()=>{var t;(t=o.value)==null||t.removeEventListener("reset",e)}},{immediate:!0})}),()=>{let{id:e,name:t,value:S,...g}=a,w={checked:n.value},x={id:e,ref:u,role:"switch",type:y.value,tabIndex:0,"aria-checked":n.value,"aria-labelledby":l==null?void 0:l.labelledby.value,"aria-describedby":l==null?void 0:l.describedby.value,onClick:b,onKeyup:k,onKeypress:C};return c(T,[t!=null&&n.value!=null?c(E,K({features:$.Hidden,as:"input",type:"checkbox",hidden:!0,readOnly:!0,checked:n.value,name:t,value:S})):null,j({ourProps:x,theirProps:{...d,...B(g,["modelValue","defaultChecked"])},slot:w,attrs:d,slots:m,name:"Switch"})])}}});export{N as a};
