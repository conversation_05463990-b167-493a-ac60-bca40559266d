import{H as Se,a as oe,I as pe,K as xe,N as Te,F as De,L as ce,$ as Pt,j as ve,B as jt,a0 as Lt,u as N,Z as Ce,M as At,a1 as Tt,o as G,b as J,d as T,c as Be,v as he,n as we,e as re,z as ie,A as de,t as le,w as Le,W as $t,V as St,R as Mt,T as st,f as He,a2 as It,C as Ve,a3 as l}from"./index-bfe6943f.js";const Rt={class:"flex justify-between items-center px-2 py-1.5"},Yt={class:"flex-shrink-0"},Ct={class:"inline-flex rounded-full"},Bt={class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Ft=["d"],Nt={class:"px-1.5 space-x-1.5 flex flex-1"},Ht={class:"flex-1 flex rounded-md"},Ut=["textContent"],zt={class:"flex-1 flex rounded-md"},Wt=["textContent"],Gt={class:"flex-shrink-0"},Zt={class:"inline-flex rounded-full"},Qt={class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Kt=["d"],ze={__name:"Header",props:{asPrevOrNext:Boolean,panel:Object,calendar:Object},setup(t){return(n,a)=>(G(),J("div",Rt,[T("div",Yt,[ie(T("span",Ct,[T("button",{type:"button",class:"p-1.5 rounded-full bg-white text-vtd-secondary-600 transition-colors border border-transparent hover:bg-vtd-secondary-100 hover:text-vtd-secondary-900 focus:bg-vtd-primary-50 focus:text-vtd-secondary-900 focus:border-vtd-primary-300 focus:ring focus:ring-vtd-primary-500 focus:ring-opacity-10 focus:outline-none dark:bg-vtd-secondary-800 dark:text-vtd-secondary-300 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-secondary-300 dark:focus:bg-vtd-secondary-600 dark:focus:text-vtd-secondary-100 dark:focus:border-vtd-primary-500 dark:focus:ring-opacity-25 dark:focus:bg-opacity-50",onClick:a[0]||(a[0]=e=>t.panel.calendar?t.calendar.onPrevious():t.calendar.onPreviousYear())},[(G(),J("svg",Bt,[T("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:t.panel.calendar?"M15 19l-7-7 7-7":"M11 19l-7-7 7-7m8 14l-7-7 7-7"},null,8,Ft)]))])],512),[[de,t.panel.calendar||t.panel.year]])]),T("div",Nt,[T("span",Ht,[T("button",{type:"button",class:"px-3 py-1.5 block w-full leading-relaxed rounded-md bg-white text-xs 2xl:text-sm tracking-wide text-vtd-secondary-600 font-semibold sm:font-medium transition-colors border border-transparent hover:bg-vtd-secondary-100 hover:text-vtd-secondary-900 focus:bg-vtd-primary-50 focus:text-vtd-secondary-900 focus:border-vtd-primary-300 focus:ring focus:ring-vtd-primary-500 focus:ring-opacity-10 focus:outline-none uppercase dark:bg-vtd-secondary-800 dark:text-vtd-secondary-300 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-secondary-300 dark:focus:bg-vtd-secondary-600 dark:focus:text-vtd-secondary-100 dark:focus:border-vtd-primary-500 dark:focus:ring-opacity-25 dark:focus:bg-opacity-50",textContent:le(t.calendar.month),onClick:a[1]||(a[1]=e=>t.calendar.openMonth())},null,8,Ut)]),T("span",zt,[T("button",{type:"button",class:"px-3 py-1.5 block w-full leading-relaxed rounded-md bg-white text-xs 2xl:text-sm tracking-wide text-vtd-secondary-600 font-semibold sm:font-medium transition-colors border border-transparent hover:bg-vtd-secondary-100 hover:text-vtd-secondary-900 focus:bg-vtd-primary-50 focus:text-vtd-secondary-900 focus:border-vtd-primary-300 focus:ring focus:ring-vtd-primary-500 focus:ring-opacity-10 focus:outline-none uppercase dark:bg-vtd-secondary-800 dark:text-vtd-secondary-300 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-secondary-300 dark:focus:bg-vtd-secondary-600 dark:focus:text-vtd-secondary-100 dark:focus:border-vtd-primary-500 dark:focus:ring-opacity-25 dark:focus:bg-opacity-50",textContent:le(t.calendar.year),onClick:a[2]||(a[2]=e=>t.calendar.openYear())},null,8,Wt)])]),T("div",Gt,[ie(T("span",Zt,[T("button",{type:"button",class:"p-1.5 rounded-full bg-white text-vtd-secondary-600 transition-colors border border-transparent hover:bg-vtd-secondary-100 hover:text-vtd-secondary-900 focus:bg-vtd-primary-50 focus:text-vtd-secondary-900 focus:border-vtd-primary-300 focus:ring focus:ring-vtd-primary-500 focus:ring-opacity-10 focus:outline-none dark:bg-vtd-secondary-800 dark:text-vtd-secondary-300 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-secondary-300 dark:focus:bg-vtd-secondary-600 dark:focus:text-vtd-secondary-100 dark:focus:border-vtd-primary-500 dark:focus:ring-opacity-25 dark:focus:bg-opacity-50",onClick:a[3]||(a[3]=e=>t.panel.calendar?t.calendar.onNext():t.calendar.onNextYear())},[(G(),J("svg",Qt,[T("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:t.panel.calendar?"M9 5l7 7-7 7":"M13 5l7 7-7 7M5 5l7 7-7 7"},null,8,Kt)]))])],512),[[de,t.panel.calendar||t.panel.year]])])]))}},Jt={class:"flex flex-wrap mt-1.5"},Xt={class:"flex rounded-md mt-1.5"},qt=["textContent","onClick"],We={__name:"Month",props:{months:Array},emits:["update:month"],setup(t,{emit:n}){return(a,e)=>(G(),J("div",Jt,[(G(!0),J(De,null,He(t.months,(i,d)=>(G(),J("div",{key:d,class:"w-1/2 px-0.5"},[T("span",Xt,[T("button",{type:"button",class:"px-3 py-2 block w-full leading-6 rounded-md bg-white text-xs 2xl:text-sm tracking-wide text-vtd-secondary-600 font-medium transition-colors border border-transparent hover:bg-vtd-secondary-100 hover:text-vtd-secondary-900 focus:bg-vtd-primary-50 focus:text-vtd-secondary-900 focus:border-vtd-primary-300 focus:ring focus:ring-vtd-primary-500 focus:ring-opacity-10 focus:outline-none uppercase dark:bg-vtd-secondary-800 dark:hover:bg-vtd-secondary-700 dark:text-vtd-secondary-300 dark:hover:text-vtd-secondary-100 dark:focus:bg-vtd-secondary-700",textContent:le(i),onClick:A=>n("update:month",d)},null,8,qt)])]))),128))]))}},er={class:"grid grid-cols-7 py-2 mt-0.5"},tr=["textContent"],Ge={__name:"Week",props:{weeks:Array},setup(t){return(n,a)=>(G(),J("div",er,[(G(!0),J(De,null,He(t.weeks,(e,i)=>(G(),J("div",{key:i,class:"text-vtd-secondary-500 text-xs 2xl:text-sm tracking-wide font-medium text-center cursor-default dark:text-vtd-secondary-400"},[T("span",{textContent:le(e)},null,8,tr)]))),128))]))}},rr={class:"flex flex-wrap"},or={class:"flex rounded-md mt-1.5"},ar=["textContent","onClick"],Ze={__name:"Year",props:{asPrevOrNext:Boolean,years:Array},emits:["update:year"],setup(t,{emit:n}){return(a,e)=>(G(),J("div",rr,[(G(!0),J(De,null,He(t.years,(i,d)=>(G(),J("div",{key:d,class:"w-1/2 px-0.5"},[T("span",or,[T("button",{type:"button",class:"px-3 py-2 block w-full leading-6 rounded-md bg-white text-xs 2xl:text-sm tracking-wide text-vtd-secondary-600 font-medium transition-colors border border-transparent hover:bg-vtd-secondary-100 hover:text-vtd-secondary-900 focus:bg-vtd-primary-50 focus:text-vtd-secondary-900 focus:border-vtd-primary-300 focus:ring focus:ring-vtd-primary-500 focus:ring-opacity-10 focus:outline-none uppercase dark:bg-vtd-secondary-800 dark:hover:bg-vtd-secondary-700 dark:text-vtd-secondary-300 dark:hover:text-vtd-secondary-100 dark:focus:bg-vtd-secondary-700",textContent:le(i),onClick:A=>n("update:year",i,t.asPrevOrNext)},null,8,ar)])]))),128))]))}},nr={class:"grid grid-cols-7 gap-y-0.5 my-1"},sr=["data-tooltip"],lr=["disabled","onClick","onMouseenter","onFocusin","textContent","data-date"],Qe={__name:"Calendar",props:{asPrevOrNext:Boolean,calendar:Object,weeks:Array,asRange:Boolean},emits:["update:date"],setup(t,{emit:n}){const a=ve("isBetweenRange"),e=ve("betweenRangeClasses"),i=ve("datepickerClasses"),d=ve("atMouseOver");return(A,g)=>(G(),J("div",nr,[re(It,{"enter-from-class":"opacity-0","enter-to-class":"opacity-100","enter-active-class":"transition-opacity ease-out duration-300","leave-active-class":"transition-opacity ease-in duration-200","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:Le(()=>[(G(!0),J(De,null,He(t.calendar.date(),(E,x)=>(G(),J("div",{key:x,class:we(["relative",{"vtd-tooltip":t.asRange&&E.duration()}]),"data-tooltip":`${E.duration()}`},[re(st,{"enter-from-class":"opacity-0","enter-to-class":"opacity-100","enter-active-class":"transition-opacity ease-out duration-200","leave-active-class":"transition-opacity ease-in duration-150","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:Le(()=>[N(a)(E)||E.hovered()?(G(),J("span",{key:0,class:we(["absolute bg-vtd-primary-100 bg-opacity-60 dark:bg-vtd-secondary-700 dark:bg-opacity-50",N(e)(E)])},null,2)):he("",!0)]),_:2},1024),T("button",{type:"button",class:we(["vtd-datepicker-date relative w-[2.7rem] h-[2.7rem] lg:w-10 lg:h-10 flex justify-center items-center text-xs 2xl:text-sm",[N(i)(E),t.asRange?"transition-all":"transition-colors"]]),disabled:E.disabled||E.inRange(),onClick:j=>n("update:date",E,t.asPrevOrNext),onMouseenter:j=>N(d)(E),onFocusin:j=>N(d)(E),textContent:le(E.date()),"data-date":E.toDate()},null,42,lr)],10,sr))),128))]),_:1})]))}},ir={key:0,class:"relative w-full border-t border-b-0 sm:border-t-0 sm:border-b lg:border-b-0 lg:border-r border-black/[.1] order-last sm:order-none dark:border-vtd-secondary-700/[1] sm:mt-1 lg:mr-1 sm:mb-1 lg:mb-0 sm:mx-1 lg:mx-0"},ur={key:0,class:"grid grid-cols-2 sm:grid-cols-3 gap-1 lg:block w-full pr-5 sm:pr-6 mt-1.5 sm:mt-0 sm:mb-1.5 lg:mb-0"},dr=["onClick","textContent"],cr={key:1,class:"grid grid-cols-2 sm:grid-cols-3 gap-1 lg:block w-full pr-5 sm:pr-6 mt-1.5 sm:mt-0 sm:mb-1.5 lg:mb-0"},at={__name:"Shortcut",props:{shortcuts:[Boolean,Function],close:Function,asRange:Boolean,asSingle:Boolean,i18n:Object},setup(t){const n=t,a=ve("setToToday"),e=ve("setToYesterday"),i=ve("setToLastDay"),d=ve("setToThisMonth"),A=ve("setToLastMonth"),g=ve("setToCustomShortcut"),E=()=>typeof n.shortcuts=="function"?n.shortcuts():!1;return(x,j)=>n.asRange&&n.asSingle||n.asRange&&!n.asSingle?(G(),J("div",ir,[E()?(G(),J("ol",ur,[(G(!0),J(De,null,He(E(),(_,b)=>(G(),J("li",{key:b},[T("a",{href:"#",class:"vtd-shortcuts block text-sm lg:text-xs px-2 py-2 sm:leading-4 whitespace-nowrap font-medium rounded text-vtd-primary-600 hover:text-vtd-primary-700 transition-colors hover:bg-vtd-secondary-100 focus:bg-vtd-secondary-100 focus:text-vtd-primary-600 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-primary-300 dark:text-vtd-primary-400 dark:focus:bg-vtd-secondary-700 dark:focus:text-vtd-primary-300",onClick:Ve(B=>N(g)(_,t.close),["prevent"]),textContent:le(_.label)},null,8,dr)]))),128))])):(G(),J("ol",cr,[T("li",null,[T("a",{href:"#",class:"vtd-shortcuts block text-sm lg:text-xs px-2 py-2 sm:leading-4 whitespace-nowrap font-medium rounded text-vtd-primary-600 hover:text-vtd-primary-700 transition-colors hover:bg-vtd-secondary-100 focus:bg-vtd-secondary-100 focus:text-vtd-primary-600 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-primary-300 dark:text-vtd-primary-400 dark:focus:bg-vtd-secondary-700 dark:focus:text-vtd-primary-300",onClick:j[0]||(j[0]=Ve(_=>N(a)(t.close),["prevent"]))},le(n.i18n.today),1)]),T("li",null,[T("a",{href:"#",class:"vtd-shortcuts block text-sm lg:text-xs px-2 py-2 sm:leading-4 whitespace-nowrap font-medium rounded text-vtd-primary-600 hover:text-vtd-primary-700 transition-colors hover:bg-vtd-secondary-100 focus:bg-vtd-secondary-100 focus:text-vtd-primary-600 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-primary-300 dark:text-vtd-primary-400 dark:focus:bg-vtd-secondary-700 dark:focus:text-vtd-primary-300",onClick:j[1]||(j[1]=Ve(_=>N(e)(t.close),["prevent"]))},le(n.i18n.yesterday),1)]),T("li",null,[T("a",{href:"#",class:"vtd-shortcuts block text-sm lg:text-xs px-2 py-2 sm:leading-4 whitespace-nowrap font-medium rounded text-vtd-primary-600 hover:text-vtd-primary-700 transition-colors hover:bg-vtd-secondary-100 focus:bg-vtd-secondary-100 focus:text-vtd-primary-600 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-primary-300 dark:text-vtd-primary-400 dark:focus:bg-vtd-secondary-700 dark:focus:text-vtd-primary-300",onClick:j[2]||(j[2]=Ve(_=>N(i)(7,t.close),["prevent"]))},le(n.i18n.past(7)),1)]),T("li",null,[T("a",{href:"#",class:"vtd-shortcuts block text-sm lg:text-xs px-2 py-2 sm:leading-4 whitespace-nowrap font-medium rounded text-vtd-primary-600 hover:text-vtd-primary-700 transition-colors hover:bg-vtd-secondary-100 focus:bg-vtd-secondary-100 focus:text-vtd-primary-600 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-primary-300 dark:text-vtd-primary-400 dark:focus:bg-vtd-secondary-700 dark:focus:text-vtd-primary-300",onClick:j[3]||(j[3]=Ve(_=>N(i)(30,t.close),["prevent"]))},le(n.i18n.past(30)),1)]),T("li",null,[T("a",{href:"#",class:"vtd-shortcuts block text-sm lg:text-xs px-2 py-2 sm:leading-4 whitespace-nowrap font-medium rounded text-vtd-primary-600 hover:text-vtd-primary-700 transition-colors hover:bg-vtd-secondary-100 focus:bg-vtd-secondary-100 focus:text-vtd-primary-600 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-primary-300 dark:text-vtd-primary-400 dark:focus:bg-vtd-secondary-700 dark:focus:text-vtd-primary-300",onClick:j[4]||(j[4]=Ve(_=>N(d)(t.close),["prevent"]))},le(n.i18n.currentMonth),1)]),T("li",null,[T("a",{href:"#",class:"vtd-shortcuts block text-sm lg:text-xs px-2 py-2 sm:leading-4 whitespace-nowrap font-medium rounded text-vtd-primary-600 hover:text-vtd-primary-700 transition-colors hover:bg-vtd-secondary-100 focus:bg-vtd-secondary-100 focus:text-vtd-primary-600 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-primary-300 dark:text-vtd-primary-400 dark:focus:bg-vtd-secondary-700 dark:focus:text-vtd-primary-300",onClick:j[5]||(j[5]=Ve(_=>N(A)(t.close),["prevent"]))},le(n.i18n.pastMonth),1)])]))])):he("",!0)}};function Ee(t,n,...a){if(t in n){let i=n[t];return typeof i=="function"?i(...a):i}let e=new Error(`Tried to handle "${t}" but there is no handler defined. Only defined handlers are: ${Object.keys(n).map(i=>`"${i}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,Ee),e}var Fe=(t=>(t[t.None=0]="None",t[t.RenderStrategy=1]="RenderStrategy",t[t.Static=2]="Static",t))(Fe||{}),vr=(t=>(t[t.Unmount=0]="Unmount",t[t.Hidden=1]="Hidden",t))(vr||{});function Me({visible:t=!0,features:n=0,ourProps:a,theirProps:e,...i}){var d;let A=pr(e,a),g=Object.assign(i,{props:A});if(t||n&2&&A.static)return qe(g);if(n&1){let E=(d=A.unmount)==null||d?0:1;return Ee(E,{[0](){return null},[1](){return qe({...i,props:{...A,hidden:!0,style:{display:"none"}}})}})}return qe(g)}function qe({props:t,attrs:n,slots:a,slot:e,name:i}){var d;let{as:A,...g}=it(t,["unmount","static"]),E=(d=a.default)==null?void 0:d.call(a,e),x={};if(e){let j=!1,_=[];for(let[b,B]of Object.entries(e))typeof B=="boolean"&&(j=!0),B===!0&&_.push(b);j&&(x["data-headlessui-state"]=_.join(" "))}if(A==="template"){if(E=lt(E),Object.keys(g).length>0||Object.keys(n).length>0){let[j,..._]=E??[];if(!fr(j)||_.length>0)throw new Error(['Passing props on "template"!',"",`The current component <${i} /> is rendering a "template".`,"However we need to passthrough the following props:",Object.keys(g).concat(Object.keys(n)).sort((b,B)=>b.localeCompare(B)).map(b=>`  - ${b}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "template".',"Render a single element as the child so that we can forward the props onto that element."].map(b=>`  - ${b}`).join(`
`)].join(`
`));return Pt(j,Object.assign({},g,x))}return Array.isArray(E)&&E.length===1?E[0]:E}return Te(A,Object.assign({},g,x),E)}function lt(t){return t.flatMap(n=>n.type===De?lt(n.children):[n])}function pr(...t){if(t.length===0)return{};if(t.length===1)return t[0];let n={},a={};for(let e of t)for(let i in e)i.startsWith("on")&&typeof e[i]=="function"?(a[i]!=null||(a[i]=[]),a[i].push(e[i])):n[i]=e[i];if(n.disabled||n["aria-disabled"])return Object.assign(n,Object.fromEntries(Object.keys(a).map(e=>[e,void 0])));for(let e in a)Object.assign(n,{[e](i,...d){let A=a[e];for(let g of A){if(i instanceof Event&&i.defaultPrevented)return;g(i,...d)}}});return n}function it(t,n=[]){let a=Object.assign({},t);for(let e of n)e in a&&delete a[e];return a}function fr(t){return t==null?!1:typeof t.type=="string"||typeof t.type=="object"||typeof t.type=="function"}let mr=0;function hr(){return++mr}function $e(){return hr()}var ke=(t=>(t.Space=" ",t.Enter="Enter",t.Escape="Escape",t.Backspace="Backspace",t.Delete="Delete",t.ArrowLeft="ArrowLeft",t.ArrowUp="ArrowUp",t.ArrowRight="ArrowRight",t.ArrowDown="ArrowDown",t.Home="Home",t.End="End",t.PageUp="PageUp",t.PageDown="PageDown",t.Tab="Tab",t))(ke||{});function z(t){var n;return t==null||t.value==null?null:(n=t.value.$el)!=null?n:t.value}let ut=Symbol("Context");var Ne=(t=>(t[t.Open=0]="Open",t[t.Closed=1]="Closed",t))(Ne||{});function dt(){return ve(ut,null)}function yr(t){ce(ut,t)}function nt(t,n){if(t)return t;let a=n??"button";if(typeof a=="string"&&a.toLowerCase()==="button")return"button"}function _r(t,n){let a=oe(nt(t.value.type,t.value.as));return jt(()=>{a.value=nt(t.value.type,t.value.as)}),xe(()=>{var e;a.value||!z(n)||z(n)instanceof HTMLButtonElement&&!((e=z(n))!=null&&e.hasAttribute("type"))&&(a.value="button")}),a}const Je=typeof window>"u"||typeof document>"u";function Ue(t){if(Je)return null;if(t instanceof Node)return t.ownerDocument;if(t!=null&&t.hasOwnProperty("value")){let n=z(t);if(n)return n.ownerDocument}return document}let tt=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(t=>`${t}:not([tabindex='-1'])`).join(",");var Oe=(t=>(t[t.First=1]="First",t[t.Previous=2]="Previous",t[t.Next=4]="Next",t[t.Last=8]="Last",t[t.WrapAround=16]="WrapAround",t[t.NoScroll=32]="NoScroll",t))(Oe||{}),br=(t=>(t[t.Error=0]="Error",t[t.Overflow=1]="Overflow",t[t.Success=2]="Success",t[t.Underflow=3]="Underflow",t))(br||{}),gr=(t=>(t[t.Previous=-1]="Previous",t[t.Next=1]="Next",t))(gr||{});function ct(t=document.body){return t==null?[]:Array.from(t.querySelectorAll(tt))}var ot=(t=>(t[t.Strict=0]="Strict",t[t.Loose=1]="Loose",t))(ot||{});function vt(t,n=0){var a;return t===((a=Ue(t))==null?void 0:a.body)?!1:Ee(n,{[0](){return t.matches(tt)},[1](){let e=t;for(;e!==null;){if(e.matches(tt))return!0;e=e.parentElement}return!1}})}let xr=["textarea","input"].join(",");function kr(t){var n,a;return(a=(n=t==null?void 0:t.matches)==null?void 0:n.call(t,xr))!=null?a:!1}function wr(t,n=a=>a){return t.slice().sort((a,e)=>{let i=n(a),d=n(e);if(i===null||d===null)return 0;let A=i.compareDocumentPosition(d);return A&Node.DOCUMENT_POSITION_FOLLOWING?-1:A&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function Ae(t,n,a=!0,e=null){var i;let d=(i=Array.isArray(t)?t.length>0?t[0].ownerDocument:document:t==null?void 0:t.ownerDocument)!=null?i:document,A=Array.isArray(t)?a?wr(t):t:ct(t);e=e??d.activeElement;let g=(()=>{if(n&5)return 1;if(n&10)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),E=(()=>{if(n&1)return 0;if(n&2)return Math.max(0,A.indexOf(e))-1;if(n&4)return Math.max(0,A.indexOf(e))+1;if(n&8)return A.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),x=n&32?{preventScroll:!0}:{},j=0,_=A.length,b;do{if(j>=_||j+_<=0)return 0;let B=E+j;if(n&16)B=(B+_)%_;else{if(B<0)return 3;if(B>=_)return 1}b=A[B],b==null||b.focus(x),j+=g}while(b!==d.activeElement);return n&6&&kr(b)&&b.select(),b.hasAttribute("tabindex")||b.setAttribute("tabindex","0"),2}function et(t,n,a){Je||xe(e=>{document.addEventListener(t,n,a),e(()=>document.removeEventListener(t,n,a))})}function Er(t,n,a=pe(()=>!0)){function e(d,A){if(!a.value||d.defaultPrevented)return;let g=A(d);if(g===null||!g.ownerDocument.documentElement.contains(g))return;let E=function x(j){return typeof j=="function"?x(j()):Array.isArray(j)||j instanceof Set?j:[j]}(t);for(let x of E){if(x===null)continue;let j=x instanceof HTMLElement?x:z(x);if(j!=null&&j.contains(g))return}return!vt(g,ot.Loose)&&g.tabIndex!==-1&&d.preventDefault(),n(d,g)}let i=oe(null);et("mousedown",d=>{a.value&&(i.value=d.target)},!0),et("click",d=>{!i.value||(e(d,()=>i.value),i.value=null)},!0),et("blur",d=>e(d,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}var Ke=(t=>(t[t.None=1]="None",t[t.Focusable=2]="Focusable",t[t.Hidden=4]="Hidden",t))(Ke||{});let rt=Se({name:"Hidden",props:{as:{type:[Object,String],default:"div"},features:{type:Number,default:1}},setup(t,{slots:n,attrs:a}){return()=>{let{features:e,...i}=t,d={"aria-hidden":(e&2)===2?!0:void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(e&4)===4&&(e&2)!==2&&{display:"none"}}};return Me({ourProps:d,theirProps:i,slot:{},attrs:a,slots:n,name:"Hidden"})}}});function Dr(t,n,a){Je||xe(e=>{window.addEventListener(t,n,a),e(()=>window.removeEventListener(t,n,a))})}var Pe=(t=>(t[t.Forwards=0]="Forwards",t[t.Backwards=1]="Backwards",t))(Pe||{});function pt(){let t=oe(0);return Dr("keydown",n=>{n.key==="Tab"&&(t.value=n.shiftKey?1:0)}),t}function Vr(t,n,a,e){Je||xe(i=>{t=t??window,t.addEventListener(n,a,e),i(()=>t.removeEventListener(n,a,e))})}var Or=(t=>(t[t.Open=0]="Open",t[t.Closed=1]="Closed",t))(Or||{});let ft=Symbol("PopoverContext");function Xe(t){let n=ve(ft,null);if(n===null){let a=new Error(`<${t} /> is missing a parent <${_t.name} /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(a,Xe),a}return n}let mt=Symbol("PopoverGroupContext");function ht(){return ve(mt,null)}let yt=Symbol("PopoverPanelContext");function Pr(){return ve(yt,null)}let _t=Se({name:"Popover",props:{as:{type:[Object,String],default:"div"}},setup(t,{slots:n,attrs:a,expose:e}){var i;let d=`headlessui-popover-button-${$e()}`,A=`headlessui-popover-panel-${$e()}`,g=oe(null);e({el:g,$el:g});let E=oe(1),x=oe(null),j=oe(null),_=oe(null),b=oe(null),B=pe(()=>Ue(g)),Q=pe(()=>{if(!z(x)||!z(b))return!1;for(let $ of document.querySelectorAll("body > *"))if(Number($==null?void 0:$.contains(z(x)))^Number($==null?void 0:$.contains(z(b))))return!0;return!1}),D={popoverState:E,buttonId:d,panelId:A,panel:b,button:x,isPortalled:Q,beforePanelSentinel:j,afterPanelSentinel:_,togglePopover(){E.value=Ee(E.value,{[0]:1,[1]:0})},closePopover(){E.value!==1&&(E.value=1)},close($){D.closePopover();let S=(()=>$?$ instanceof HTMLElement?$:$.value instanceof HTMLElement?z($):z(D.button):z(D.button))();S==null||S.focus()}};ce(ft,D),yr(pe(()=>Ee(E.value,{[0]:Ne.Open,[1]:Ne.Closed})));let L={buttonId:d,panelId:A,close(){D.closePopover()}},v=ht(),k=v==null?void 0:v.registerPopover;function O(){var $,S,s,r;return(r=v==null?void 0:v.isFocusWithinPopoverGroup())!=null?r:(($=B.value)==null?void 0:$.activeElement)&&(((S=z(x))==null?void 0:S.contains(B.value.activeElement))||((s=z(b))==null?void 0:s.contains(B.value.activeElement)))}return xe(()=>k==null?void 0:k(L)),Vr((i=B.value)==null?void 0:i.defaultView,"focus",$=>{var S,s;E.value===0&&(O()||!x||!b||(S=z(D.beforePanelSentinel))!=null&&S.contains($.target)||(s=z(D.afterPanelSentinel))!=null&&s.contains($.target)||D.closePopover())},!0),Er([x,b],($,S)=>{var s;D.closePopover(),vt(S,ot.Loose)||($.preventDefault(),(s=z(x))==null||s.focus())},pe(()=>E.value===0)),()=>{let $={open:E.value===0,close:D.close};return Me({theirProps:t,ourProps:{ref:g},slot:$,slots:n,attrs:a,name:"Popover"})}}}),jr=Se({name:"PopoverButton",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(t,{attrs:n,slots:a,expose:e}){let i=Xe("PopoverButton"),d=pe(()=>Ue(i.button));e({el:i.button,$el:i.button});let A=ht(),g=A==null?void 0:A.closeOthers,E=Pr(),x=E===null?!1:E===i.panelId,j=oe(null),_=`headlessui-focus-sentinel-${$e()}`;x||xe(()=>{i.button.value=j.value});let b=_r(pe(()=>({as:t.as,type:n.type})),j);function B(v){var k,O,$,S,s;if(x){if(i.popoverState.value===1)return;switch(v.key){case ke.Space:case ke.Enter:v.preventDefault(),(O=(k=v.target).click)==null||O.call(k),i.closePopover(),($=z(i.button))==null||$.focus();break}}else switch(v.key){case ke.Space:case ke.Enter:v.preventDefault(),v.stopPropagation(),i.popoverState.value===1&&(g==null||g(i.buttonId)),i.togglePopover();break;case ke.Escape:if(i.popoverState.value!==0)return g==null?void 0:g(i.buttonId);if(!z(i.button)||(S=d.value)!=null&&S.activeElement&&!((s=z(i.button))!=null&&s.contains(d.value.activeElement)))return;v.preventDefault(),v.stopPropagation(),i.closePopover();break}}function Q(v){x||v.key===ke.Space&&v.preventDefault()}function D(v){var k,O;t.disabled||(x?(i.closePopover(),(k=z(i.button))==null||k.focus()):(v.preventDefault(),v.stopPropagation(),i.popoverState.value===1&&(g==null||g(i.buttonId)),i.togglePopover(),(O=z(i.button))==null||O.focus()))}function L(v){v.preventDefault(),v.stopPropagation()}return()=>{let v=i.popoverState.value===0,k={open:v},O=x?{ref:j,type:b.value,onKeydown:B,onClick:D}:{ref:j,id:i.buttonId,type:b.value,"aria-expanded":t.disabled?void 0:i.popoverState.value===0,"aria-controls":z(i.panel)?i.panelId:void 0,disabled:t.disabled?!0:void 0,onKeydown:B,onKeyup:Q,onClick:D,onMousedown:L},$=pt();function S(){let s=z(i.panel);if(!s)return;function r(){Ee($.value,{[Pe.Forwards]:()=>Ae(s,Oe.First),[Pe.Backwards]:()=>Ae(s,Oe.Last)})}r()}return Te(De,[Me({ourProps:O,theirProps:{...n,...t},slot:k,attrs:n,slots:a,name:"PopoverButton"}),v&&!x&&i.isPortalled.value&&Te(rt,{id:_,features:Ke.Focusable,as:"button",type:"button",onFocus:S})])}}}),Lr=Se({name:"PopoverOverlay",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0}},setup(t,{attrs:n,slots:a}){let e=Xe("PopoverOverlay"),i=`headlessui-popover-overlay-${$e()}`,d=dt(),A=pe(()=>d!==null?d.value===Ne.Open:e.popoverState.value===0);function g(){e.closePopover()}return()=>{let E={open:e.popoverState.value===0};return Me({ourProps:{id:i,"aria-hidden":!0,onClick:g},theirProps:t,slot:E,attrs:n,slots:a,features:Fe.RenderStrategy|Fe.Static,visible:A.value,name:"PopoverOverlay"})}}}),Ar=Se({name:"PopoverPanel",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},focus:{type:Boolean,default:!1}},inheritAttrs:!1,setup(t,{attrs:n,slots:a,expose:e}){let{focus:i}=t,d=Xe("PopoverPanel"),A=pe(()=>Ue(d.panel)),g=`headlessui-focus-sentinel-before-${$e()}`,E=`headlessui-focus-sentinel-after-${$e()}`;e({el:d.panel,$el:d.panel}),ce(yt,d.panelId),xe(()=>{var L,v;if(!i||d.popoverState.value!==0||!d.panel)return;let k=(L=A.value)==null?void 0:L.activeElement;(v=z(d.panel))!=null&&v.contains(k)||Ae(z(d.panel),Oe.First)});let x=dt(),j=pe(()=>x!==null?x.value===Ne.Open:d.popoverState.value===0);function _(L){var v,k;switch(L.key){case ke.Escape:if(d.popoverState.value!==0||!z(d.panel)||A.value&&!((v=z(d.panel))!=null&&v.contains(A.value.activeElement)))return;L.preventDefault(),L.stopPropagation(),d.closePopover(),(k=z(d.button))==null||k.focus();break}}function b(L){var v,k,O,$,S;let s=L.relatedTarget;!s||!z(d.panel)||(v=z(d.panel))!=null&&v.contains(s)||(d.closePopover(),((O=(k=z(d.beforePanelSentinel))==null?void 0:k.contains)!=null&&O.call(k,s)||(S=($=z(d.afterPanelSentinel))==null?void 0:$.contains)!=null&&S.call($,s))&&s.focus({preventScroll:!0}))}let B=pt();function Q(){let L=z(d.panel);if(!L)return;function v(){Ee(B.value,{[Pe.Forwards]:()=>{Ae(L,Oe.Next)},[Pe.Backwards]:()=>{var k;(k=z(d.button))==null||k.focus({preventScroll:!0})}})}v()}function D(){let L=z(d.panel);if(!L)return;function v(){Ee(B.value,{[Pe.Forwards]:()=>{var k,O;let $=z(d.button),S=z(d.panel);if(!$)return;let s=ct(),r=s.indexOf($),F=s.slice(0,r+1),R=[...s.slice(r+1),...F];for(let w of R.slice())if((O=(k=w==null?void 0:w.id)==null?void 0:k.startsWith)!=null&&O.call(k,"headlessui-focus-sentinel-")||S!=null&&S.contains(w)){let Z=R.indexOf(w);Z!==-1&&R.splice(Z,1)}Ae(R,Oe.First,!1)},[Pe.Backwards]:()=>Ae(L,Oe.Previous)})}v()}return()=>{let L={open:d.popoverState.value===0,close:d.close},v={ref:d.panel,id:d.panelId,onKeydown:_,onFocusout:i&&d.popoverState.value===0?b:void 0,tabIndex:-1};return Me({ourProps:v,theirProps:{...n,...it(t,["focus"])},attrs:n,slot:L,slots:{...a,default:(...k)=>{var O;return[Te(De,[j.value&&d.isPortalled.value&&Te(rt,{id:g,ref:d.beforePanelSentinel,features:Ke.Focusable,as:"button",type:"button",onFocus:Q}),(O=a.default)==null?void 0:O.call(a,...k),j.value&&d.isPortalled.value&&Te(rt,{id:E,ref:d.afterPanelSentinel,features:Ke.Focusable,as:"button",type:"button",onFocus:D})])]}},features:Fe.RenderStrategy|Fe.Static,visible:j.value,name:"PopoverPanel"})}}});Se({name:"PopoverGroup",props:{as:{type:[Object,String],default:"div"}},setup(t,{attrs:n,slots:a,expose:e}){let i=oe(null),d=oe([]),A=pe(()=>Ue(i));e({el:i,$el:i});function g(_){let b=d.value.indexOf(_);b!==-1&&d.value.splice(b,1)}function E(_){return d.value.push(_),()=>{g(_)}}function x(){var _;let b=A.value;if(!b)return!1;let B=b.activeElement;return(_=z(i))!=null&&_.contains(B)?!0:d.value.some(Q=>{var D,L;return((D=b.getElementById(Q.buttonId))==null?void 0:D.contains(B))||((L=b.getElementById(Q.panelId))==null?void 0:L.contains(B))})}function j(_){for(let b of d.value)b.buttonId!==_&&b.close()}return ce(mt,{registerPopover:E,unregisterPopover:g,isFocusWithinPopoverGroup:x,closeOthers:j}),()=>Me({ourProps:{ref:i},theirProps:t,slot:{},attrs:n,slots:a,name:"PopoverGroup"})}});var je=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},bt={exports:{}};(function(t,n){(function(a,e){t.exports=e()})(je,function(){var a=1e3,e=6e4,i=36e5,d="millisecond",A="second",g="minute",E="hour",x="day",j="week",_="month",b="quarter",B="year",Q="date",D="Invalid Date",L=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,v=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,k={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(M){var h=["th","st","nd","rd"],m=M%100;return"["+M+(h[(m-20)%10]||h[m]||h[0])+"]"}},O=function(M,h,m){var P=String(M);return!P||P.length>=h?M:""+Array(h+1-P.length).join(m)+M},$={s:O,z:function(M){var h=-M.utcOffset(),m=Math.abs(h),P=Math.floor(m/60),V=m%60;return(h<=0?"+":"-")+O(P,2,"0")+":"+O(V,2,"0")},m:function M(h,m){if(h.date()<m.date())return-M(m,h);var P=12*(m.year()-h.year())+(m.month()-h.month()),V=h.clone().add(P,_),Y=m-V<0,I=h.clone().add(P+(Y?-1:1),_);return+(-(P+(m-V)/(Y?V-I:I-V))||0)},a:function(M){return M<0?Math.ceil(M)||0:Math.floor(M)},p:function(M){return{M:_,y:B,w:j,d:x,D:Q,h:E,m:g,s:A,ms:d,Q:b}[M]||String(M||"").toLowerCase().replace(/s$/,"")},u:function(M){return M===void 0}},S="en",s={};s[S]=k;var r=function(M){return M instanceof Z},F=function M(h,m,P){var V;if(!h)return S;if(typeof h=="string"){var Y=h.toLowerCase();s[Y]&&(V=Y),m&&(s[Y]=m,V=Y);var I=h.split("-");if(!V&&I.length>1)return M(I[0])}else{var W=h.name;s[W]=h,V=W}return!P&&V&&(S=V),V||!P&&S},R=function(M,h){if(r(M))return M.clone();var m=typeof h=="object"?h:{};return m.date=M,m.args=arguments,new Z(m)},w=$;w.l=F,w.i=r,w.w=function(M,h){return R(M,{locale:h.$L,utc:h.$u,x:h.$x,$offset:h.$offset})};var Z=function(){function M(m){this.$L=F(m.locale,null,!0),this.parse(m)}var h=M.prototype;return h.parse=function(m){this.$d=function(P){var V=P.date,Y=P.utc;if(V===null)return new Date(NaN);if(w.u(V))return new Date;if(V instanceof Date)return new Date(V);if(typeof V=="string"&&!/Z$/i.test(V)){var I=V.match(L);if(I){var W=I[2]-1||0,K=(I[7]||"0").substring(0,3);return Y?new Date(Date.UTC(I[1],W,I[3]||1,I[4]||0,I[5]||0,I[6]||0,K)):new Date(I[1],W,I[3]||1,I[4]||0,I[5]||0,I[6]||0,K)}}return new Date(V)}(m),this.$x=m.x||{},this.init()},h.init=function(){var m=this.$d;this.$y=m.getFullYear(),this.$M=m.getMonth(),this.$D=m.getDate(),this.$W=m.getDay(),this.$H=m.getHours(),this.$m=m.getMinutes(),this.$s=m.getSeconds(),this.$ms=m.getMilliseconds()},h.$utils=function(){return w},h.isValid=function(){return this.$d.toString()!==D},h.isSame=function(m,P){var V=R(m);return this.startOf(P)<=V&&V<=this.endOf(P)},h.isAfter=function(m,P){return R(m)<this.startOf(P)},h.isBefore=function(m,P){return this.endOf(P)<R(m)},h.$g=function(m,P,V){return w.u(m)?this[P]:this.set(V,m)},h.unix=function(){return Math.floor(this.valueOf()/1e3)},h.valueOf=function(){return this.$d.getTime()},h.startOf=function(m,P){var V=this,Y=!!w.u(P)||P,I=w.p(m),W=function(se,ne){var ue=w.w(V.$u?Date.UTC(V.$y,ne,se):new Date(V.$y,ne,se),V);return Y?ue:ue.endOf(x)},K=function(se,ne){return w.w(V.toDate()[se].apply(V.toDate("s"),(Y?[0,0,0,0]:[23,59,59,999]).slice(ne)),V)},q=this.$W,ae=this.$M,me=this.$D,fe="set"+(this.$u?"UTC":"");switch(I){case B:return Y?W(1,0):W(31,11);case _:return Y?W(1,ae):W(0,ae+1);case j:var ge=this.$locale().weekStart||0,ye=(q<ge?q+7:q)-ge;return W(Y?me-ye:me+(6-ye),ae);case x:case Q:return K(fe+"Hours",0);case E:return K(fe+"Minutes",1);case g:return K(fe+"Seconds",2);case A:return K(fe+"Milliseconds",3);default:return this.clone()}},h.endOf=function(m){return this.startOf(m,!1)},h.$set=function(m,P){var V,Y=w.p(m),I="set"+(this.$u?"UTC":""),W=(V={},V[x]=I+"Date",V[Q]=I+"Date",V[_]=I+"Month",V[B]=I+"FullYear",V[E]=I+"Hours",V[g]=I+"Minutes",V[A]=I+"Seconds",V[d]=I+"Milliseconds",V)[Y],K=Y===x?this.$D+(P-this.$W):P;if(Y===_||Y===B){var q=this.clone().set(Q,1);q.$d[W](K),q.init(),this.$d=q.set(Q,Math.min(this.$D,q.daysInMonth())).$d}else W&&this.$d[W](K);return this.init(),this},h.set=function(m,P){return this.clone().$set(m,P)},h.get=function(m){return this[w.p(m)]()},h.add=function(m,P){var V,Y=this;m=Number(m);var I=w.p(P),W=function(ae){var me=R(Y);return w.w(me.date(me.date()+Math.round(ae*m)),Y)};if(I===_)return this.set(_,this.$M+m);if(I===B)return this.set(B,this.$y+m);if(I===x)return W(1);if(I===j)return W(7);var K=(V={},V[g]=e,V[E]=i,V[A]=a,V)[I]||1,q=this.$d.getTime()+m*K;return w.w(q,this)},h.subtract=function(m,P){return this.add(-1*m,P)},h.format=function(m){var P=this,V=this.$locale();if(!this.isValid())return V.invalidDate||D;var Y=m||"YYYY-MM-DDTHH:mm:ssZ",I=w.z(this),W=this.$H,K=this.$m,q=this.$M,ae=V.weekdays,me=V.months,fe=function(ne,ue,_e,be){return ne&&(ne[ue]||ne(P,Y))||_e[ue].slice(0,be)},ge=function(ne){return w.s(W%12||12,ne,"0")},ye=V.meridiem||function(ne,ue,_e){var be=ne<12?"AM":"PM";return _e?be.toLowerCase():be},se={YY:String(this.$y).slice(-2),YYYY:this.$y,M:q+1,MM:w.s(q+1,2,"0"),MMM:fe(V.monthsShort,q,me,3),MMMM:fe(me,q),D:this.$D,DD:w.s(this.$D,2,"0"),d:String(this.$W),dd:fe(V.weekdaysMin,this.$W,ae,2),ddd:fe(V.weekdaysShort,this.$W,ae,3),dddd:ae[this.$W],H:String(W),HH:w.s(W,2,"0"),h:ge(1),hh:ge(2),a:ye(W,K,!0),A:ye(W,K,!1),m:String(K),mm:w.s(K,2,"0"),s:String(this.$s),ss:w.s(this.$s,2,"0"),SSS:w.s(this.$ms,3,"0"),Z:I};return Y.replace(v,function(ne,ue){return ue||se[ne]||I.replace(":","")})},h.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},h.diff=function(m,P,V){var Y,I=w.p(P),W=R(m),K=(W.utcOffset()-this.utcOffset())*e,q=this-W,ae=w.m(this,W);return ae=(Y={},Y[B]=ae/12,Y[_]=ae,Y[b]=ae/3,Y[j]=(q-K)/6048e5,Y[x]=(q-K)/864e5,Y[E]=q/i,Y[g]=q/e,Y[A]=q/a,Y)[I]||q,V?ae:w.a(ae)},h.daysInMonth=function(){return this.endOf(_).$D},h.$locale=function(){return s[this.$L]},h.locale=function(m,P){if(!m)return this.$L;var V=this.clone(),Y=F(m,P,!0);return Y&&(V.$L=Y),V},h.clone=function(){return w.w(this.$d,this)},h.toDate=function(){return new Date(this.valueOf())},h.toJSON=function(){return this.isValid()?this.toISOString():null},h.toISOString=function(){return this.$d.toISOString()},h.toString=function(){return this.$d.toUTCString()},M}(),X=Z.prototype;return R.prototype=X,[["$ms",d],["$s",A],["$m",g],["$H",E],["$W",x],["$M",_],["$y",B],["$D",Q]].forEach(function(M){X[M[1]]=function(h){return this.$g(h,M[0],M[1])}}),R.extend=function(M,h){return M.$i||(M(h,Z,R),M.$i=!0),R},R.locale=F,R.isDayjs=r,R.unix=function(M){return R(1e3*M)},R.en=s[S],R.Ls=s,R.p={},R})})(bt);const u=bt.exports;var gt={exports:{}};(function(t,n){(function(a,e){t.exports=e()})(je,function(){return function(a,e,i){var d=e.prototype,A=function(_){return _&&(_.indexOf?_:_.s)},g=function(_,b,B,Q,D){var L=_.name?_:_.$locale(),v=A(L[b]),k=A(L[B]),O=v||k.map(function(S){return S.slice(0,Q)});if(!D)return O;var $=L.weekStart;return O.map(function(S,s){return O[(s+($||0))%7]})},E=function(){return i.Ls[i.locale()]},x=function(_,b){return _.formats[b]||function(B){return B.replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(Q,D,L){return D||L.slice(1)})}(_.formats[b.toUpperCase()])},j=function(){var _=this;return{months:function(b){return b?b.format("MMMM"):g(_,"months")},monthsShort:function(b){return b?b.format("MMM"):g(_,"monthsShort","months",3)},firstDayOfWeek:function(){return _.$locale().weekStart||0},weekdays:function(b){return b?b.format("dddd"):g(_,"weekdays")},weekdaysMin:function(b){return b?b.format("dd"):g(_,"weekdaysMin","weekdays",2)},weekdaysShort:function(b){return b?b.format("ddd"):g(_,"weekdaysShort","weekdays",3)},longDateFormat:function(b){return x(_.$locale(),b)},meridiem:this.$locale().meridiem,ordinal:this.$locale().ordinal}};d.localeData=function(){return j.bind(this)()},i.localeData=function(){var _=E();return{firstDayOfWeek:function(){return _.weekStart||0},weekdays:function(){return i.weekdays()},weekdaysShort:function(){return i.weekdaysShort()},weekdaysMin:function(){return i.weekdaysMin()},months:function(){return i.months()},monthsShort:function(){return i.monthsShort()},longDateFormat:function(b){return x(_,b)},meridiem:_.meridiem,ordinal:_.ordinal}},i.months=function(){return g(E(),"months")},i.monthsShort=function(){return g(E(),"monthsShort","months",3)},i.weekdays=function(_){return g(E(),"weekdays",null,null,_)},i.weekdaysShort=function(_){return g(E(),"weekdaysShort","weekdays",3,_)},i.weekdaysMin=function(_){return g(E(),"weekdaysMin","weekdays",2,_)}}})})(gt);const Tr=gt.exports;var xt={exports:{}};(function(t,n){(function(a,e){t.exports=e()})(je,function(){var a={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"};return function(e,i,d){var A=i.prototype,g=A.format;d.en.formats=a,A.format=function(E){E===void 0&&(E="YYYY-MM-DDTHH:mm:ssZ");var x=this.$locale().formats,j=function(_,b){return _.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,function(B,Q,D){var L=D&&D.toUpperCase();return Q||b[D]||a[D]||b[L].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(v,k,O){return k||O.slice(1)})})}(E,x===void 0?{}:x);return g.call(this,j)}}})})(xt);const $r=xt.exports;var kt={exports:{}};(function(t,n){(function(a,e){t.exports=e()})(je,function(){var a={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},e=/(\[[^[]*\])|([-_:/.,()\s]+)|(A|a|YYYY|YY?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,i=/\d\d/,d=/\d\d?/,A=/\d*[^-_:/,()\s\d]+/,g={},E=function(D){return(D=+D)+(D>68?1900:2e3)},x=function(D){return function(L){this[D]=+L}},j=[/[+-]\d\d:?(\d\d)?|Z/,function(D){(this.zone||(this.zone={})).offset=function(L){if(!L||L==="Z")return 0;var v=L.match(/([+-]|\d\d)/g),k=60*v[1]+(+v[2]||0);return k===0?0:v[0]==="+"?-k:k}(D)}],_=function(D){var L=g[D];return L&&(L.indexOf?L:L.s.concat(L.f))},b=function(D,L){var v,k=g.meridiem;if(k){for(var O=1;O<=24;O+=1)if(D.indexOf(k(O,0,L))>-1){v=O>12;break}}else v=D===(L?"pm":"PM");return v},B={A:[A,function(D){this.afternoon=b(D,!1)}],a:[A,function(D){this.afternoon=b(D,!0)}],S:[/\d/,function(D){this.milliseconds=100*+D}],SS:[i,function(D){this.milliseconds=10*+D}],SSS:[/\d{3}/,function(D){this.milliseconds=+D}],s:[d,x("seconds")],ss:[d,x("seconds")],m:[d,x("minutes")],mm:[d,x("minutes")],H:[d,x("hours")],h:[d,x("hours")],HH:[d,x("hours")],hh:[d,x("hours")],D:[d,x("day")],DD:[i,x("day")],Do:[A,function(D){var L=g.ordinal,v=D.match(/\d+/);if(this.day=v[0],L)for(var k=1;k<=31;k+=1)L(k).replace(/\[|\]/g,"")===D&&(this.day=k)}],M:[d,x("month")],MM:[i,x("month")],MMM:[A,function(D){var L=_("months"),v=(_("monthsShort")||L.map(function(k){return k.slice(0,3)})).indexOf(D)+1;if(v<1)throw new Error;this.month=v%12||v}],MMMM:[A,function(D){var L=_("months").indexOf(D)+1;if(L<1)throw new Error;this.month=L%12||L}],Y:[/[+-]?\d+/,x("year")],YY:[i,function(D){this.year=E(D)}],YYYY:[/\d{4}/,x("year")],Z:j,ZZ:j};function Q(D){var L,v;L=D,v=g&&g.formats;for(var k=(D=L.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,function(R,w,Z){var X=Z&&Z.toUpperCase();return w||v[Z]||a[Z]||v[X].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(M,h,m){return h||m.slice(1)})})).match(e),O=k.length,$=0;$<O;$+=1){var S=k[$],s=B[S],r=s&&s[0],F=s&&s[1];k[$]=F?{regex:r,parser:F}:S.replace(/^\[|\]$/g,"")}return function(R){for(var w={},Z=0,X=0;Z<O;Z+=1){var M=k[Z];if(typeof M=="string")X+=M.length;else{var h=M.regex,m=M.parser,P=R.slice(X),V=h.exec(P)[0];m.call(w,V),R=R.replace(V,"")}}return function(Y){var I=Y.afternoon;if(I!==void 0){var W=Y.hours;I?W<12&&(Y.hours+=12):W===12&&(Y.hours=0),delete Y.afternoon}}(w),w}}return function(D,L,v){v.p.customParseFormat=!0,D&&D.parseTwoDigitYear&&(E=D.parseTwoDigitYear);var k=L.prototype,O=k.parse;k.parse=function($){var S=$.date,s=$.utc,r=$.args;this.$u=s;var F=r[1];if(typeof F=="string"){var R=r[2]===!0,w=r[3]===!0,Z=R||w,X=r[2];w&&(X=r[2]),g=this.$locale(),!R&&X&&(g=v.Ls[X]),this.$d=function(P,V,Y){try{if(["x","X"].indexOf(V)>-1)return new Date((V==="X"?1e3:1)*P);var I=Q(V)(P),W=I.year,K=I.month,q=I.day,ae=I.hours,me=I.minutes,fe=I.seconds,ge=I.milliseconds,ye=I.zone,se=new Date,ne=q||(W||K?1:se.getDate()),ue=W||se.getFullYear(),_e=0;W&&!K||(_e=K>0?K-1:se.getMonth());var be=ae||0,Ie=me||0,Re=fe||0,Ye=ge||0;return ye?new Date(Date.UTC(ue,_e,ne,be,Ie,Re,Ye+60*ye.offset*1e3)):Y?new Date(Date.UTC(ue,_e,ne,be,Ie,Re,Ye)):new Date(ue,_e,ne,be,Ie,Re,Ye)}catch{return new Date("")}}(S,F,s),this.init(),X&&X!==!0&&(this.$L=this.locale(X).$L),Z&&S!=this.format(F)&&(this.$d=new Date("")),g={}}else if(F instanceof Array)for(var M=F.length,h=1;h<=M;h+=1){r[1]=F[h-1];var m=v.apply(this,r);if(m.isValid()){this.$d=m.$d,this.$L=m.$L,this.init();break}h===M&&(this.$d=new Date(""))}else O.call(this,$)}}})})(kt);const Sr=kt.exports;var wt={exports:{}};(function(t,n){(function(a,e){t.exports=e()})(je,function(){return function(a,e,i){e.prototype.isToday=function(){var d="YYYY-MM-DD",A=i();return this.format(d)===A.format(d)}}})})(wt);const Mr=wt.exports;var Et={exports:{}};(function(t,n){(function(a,e){t.exports=e()})(je,function(){return function(a,e,i){e.prototype.isBetween=function(d,A,g,E){var x=i(d),j=i(A),_=(E=E||"()")[0]==="(",b=E[1]===")";return(_?this.isAfter(x,g):!this.isBefore(x,g))&&(b?this.isBefore(j,g):!this.isAfter(j,g))||(_?this.isBefore(x,g):!this.isAfter(x,g))&&(b?this.isAfter(j,g):!this.isBefore(j,g))}}})})(Et);const Ir=Et.exports;var Dt={exports:{}};(function(t,n){(function(a,e){t.exports=e()})(je,function(){var a,e,i=1e3,d=6e4,A=36e5,g=864e5,E=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,x=31536e6,j=2592e6,_=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/,b={years:x,months:j,days:g,hours:A,minutes:d,seconds:i,milliseconds:1,weeks:6048e5},B=function(S){return S instanceof $},Q=function(S,s,r){return new $(S,r,s.$l)},D=function(S){return e.p(S)+"s"},L=function(S){return S<0},v=function(S){return L(S)?Math.ceil(S):Math.floor(S)},k=function(S){return Math.abs(S)},O=function(S,s){return S?L(S)?{negative:!0,format:""+k(S)+s}:{negative:!1,format:""+S+s}:{negative:!1,format:""}},$=function(){function S(r,F,R){var w=this;if(this.$d={},this.$l=R,r===void 0&&(this.$ms=0,this.parseFromMilliseconds()),F)return Q(r*b[D(F)],this);if(typeof r=="number")return this.$ms=r,this.parseFromMilliseconds(),this;if(typeof r=="object")return Object.keys(r).forEach(function(M){w.$d[D(M)]=r[M]}),this.calMilliseconds(),this;if(typeof r=="string"){var Z=r.match(_);if(Z){var X=Z.slice(2).map(function(M){return M!=null?Number(M):0});return this.$d.years=X[0],this.$d.months=X[1],this.$d.weeks=X[2],this.$d.days=X[3],this.$d.hours=X[4],this.$d.minutes=X[5],this.$d.seconds=X[6],this.calMilliseconds(),this}}return this}var s=S.prototype;return s.calMilliseconds=function(){var r=this;this.$ms=Object.keys(this.$d).reduce(function(F,R){return F+(r.$d[R]||0)*b[R]},0)},s.parseFromMilliseconds=function(){var r=this.$ms;this.$d.years=v(r/x),r%=x,this.$d.months=v(r/j),r%=j,this.$d.days=v(r/g),r%=g,this.$d.hours=v(r/A),r%=A,this.$d.minutes=v(r/d),r%=d,this.$d.seconds=v(r/i),r%=i,this.$d.milliseconds=r},s.toISOString=function(){var r=O(this.$d.years,"Y"),F=O(this.$d.months,"M"),R=+this.$d.days||0;this.$d.weeks&&(R+=7*this.$d.weeks);var w=O(R,"D"),Z=O(this.$d.hours,"H"),X=O(this.$d.minutes,"M"),M=this.$d.seconds||0;this.$d.milliseconds&&(M+=this.$d.milliseconds/1e3);var h=O(M,"S"),m=r.negative||F.negative||w.negative||Z.negative||X.negative||h.negative,P=Z.format||X.format||h.format?"T":"",V=(m?"-":"")+"P"+r.format+F.format+w.format+P+Z.format+X.format+h.format;return V==="P"||V==="-P"?"P0D":V},s.toJSON=function(){return this.toISOString()},s.format=function(r){var F=r||"YYYY-MM-DDTHH:mm:ss",R={Y:this.$d.years,YY:e.s(this.$d.years,2,"0"),YYYY:e.s(this.$d.years,4,"0"),M:this.$d.months,MM:e.s(this.$d.months,2,"0"),D:this.$d.days,DD:e.s(this.$d.days,2,"0"),H:this.$d.hours,HH:e.s(this.$d.hours,2,"0"),m:this.$d.minutes,mm:e.s(this.$d.minutes,2,"0"),s:this.$d.seconds,ss:e.s(this.$d.seconds,2,"0"),SSS:e.s(this.$d.milliseconds,3,"0")};return F.replace(E,function(w,Z){return Z||String(R[w])})},s.as=function(r){return this.$ms/b[D(r)]},s.get=function(r){var F=this.$ms,R=D(r);return R==="milliseconds"?F%=1e3:F=R==="weeks"?v(F/b[R]):this.$d[R],F===0?0:F},s.add=function(r,F,R){var w;return w=F?r*b[D(F)]:B(r)?r.$ms:Q(r,this).$ms,Q(this.$ms+w*(R?-1:1),this)},s.subtract=function(r,F){return this.add(r,F,!0)},s.locale=function(r){var F=this.clone();return F.$l=r,F},s.clone=function(){return Q(this.$ms,this)},s.humanize=function(r){return a().add(this.$ms,"ms").locale(this.$l).fromNow(!r)},s.milliseconds=function(){return this.get("milliseconds")},s.asMilliseconds=function(){return this.as("milliseconds")},s.seconds=function(){return this.get("seconds")},s.asSeconds=function(){return this.as("seconds")},s.minutes=function(){return this.get("minutes")},s.asMinutes=function(){return this.as("minutes")},s.hours=function(){return this.get("hours")},s.asHours=function(){return this.as("hours")},s.days=function(){return this.get("days")},s.asDays=function(){return this.as("days")},s.weeks=function(){return this.get("weeks")},s.asWeeks=function(){return this.as("weeks")},s.months=function(){return this.get("months")},s.asMonths=function(){return this.as("months")},s.years=function(){return this.get("years")},s.asYears=function(){return this.as("years")},S}();return function(S,s,r){a=r,e=r().$utils(),r.duration=function(w,Z){var X=r.locale();return Q(w,{$l:X},Z)},r.isDuration=B;var F=s.prototype.add,R=s.prototype.subtract;s.prototype.add=function(w,Z){return B(w)&&(w=w.asMilliseconds()),F.bind(this)(w,Z)},s.prototype.subtract=function(w,Z){return B(w)&&(w=w.asMilliseconds()),R.bind(this)(w,Z)}}})})(Dt);const Rr=Dt.exports;function Yr(){const t=n=>{const a=[],e=n.localeData().firstDayOfWeek();for(let i=0;i<=n.date(0-e).day();i++)a.push(n.date(0).subtract(i,"day"));return a.sort((i,d)=>i.date()-d.date())};return{usePreviousDate:t,useCurrentDate:n=>Array.from({length:n.daysInMonth()},(a,e)=>n.date(e+1)),useNextDate:n=>{const a=[];for(let e=1;e<=42-(t(n).length+n.daysInMonth());e++)a.push(n.date(e).month(n.month()).add(1,"month"));return a},useDisableDate:(n,{disableDate:a})=>typeof a=="function"?a(n.toDate()):!1,useBetweenRange:(n,{previous:a,next:e})=>{let i;return a.isAfter(e,"date")?i="(]":i="[)",!!(n.isBetween(a,e,"date",i)&&!n.off)},useToValueFromString:(n,{formatter:a})=>n.format(a.date),useToValueFromArray:({previous:n,next:a},{formatter:e,separator:i})=>`${n.format(e.date)}${i}${a.format(e.date)}`}}function Cr(){return{useVisibleViewport:t=>{if(t){const{right:n}=t.getBoundingClientRect(),a=window.innerWidth||document.documentElement.clientWidth;return n>a}else return null}}}const Br=["disabled","placeholder"],Fr={class:"absolute inset-y-0 right-0 inline-flex items-center rounded-md overflow-hidden"},Nr=["disabled"],Hr={class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Ur={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M6 18L18 6M6 6l12 12"},zr={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"},Wr={class:"flex flex-wrap lg:flex-nowrap"},Gr={class:"relative flex flex-wrap sm:flex-nowrap p-1 w-full"},Zr={key:0,class:"hidden h-full absolute inset-0 sm:flex justify-center items-center"},Qr=T("div",{class:"h-full border-r border-black/[.1] dark:border-vtd-secondary-700/[1]"},null,-1),Kr=[Qr],Jr={class:"px-0.5 sm:px-2"},Xr={key:1,class:"relative w-full md:w-1/2 lg:w-80 overflow-hidden mt-3 sm:mt-0 sm:ml-2"},qr={class:"px-0.5 sm:px-2"},eo={key:0},to={class:"mt-2 mx-2 py-1.5 border-t border-black/[.1] dark:border-vtd-secondary-700/[1]"},ro={class:"mt-1.5 sm:flex sm:flex-row-reverse"},oo=["disabled","onClick","textContent"],ao=["onClick","textContent"],no={key:1,class:"sm:hidden"},so={class:"mt-2 mx-2 py-1.5 border-t border-black/[.1] dark:border-vtd-secondary-700/[1]"},lo={class:"mt-1.5 sm:flex sm:flex-row-reverse"},io=["onClick","textContent"],uo={key:1,class:"flex"},co={class:"bg-white rounded-lg shadow-sm border border-black/[.1] px-3 py-3 sm:px-4 sm:py-4 dark:bg-vtd-secondary-800 dark:border-vtd-secondary-700/[1]"},vo={class:"flex flex-wrap lg:flex-nowrap"},po={class:"relative flex flex-wrap sm:flex-nowrap p-1 w-full"},fo={key:0,class:"hidden h-full absolute inset-0 sm:flex justify-center items-center"},mo=T("div",{class:"h-full border-r border-black/[.1] dark:border-vtd-secondary-700/[1]"},null,-1),ho=[mo],yo={class:"px-0.5 sm:px-2"},_o={key:1,class:"relative w-full md:w-1/2 lg:w-80 overflow-hidden mt-3 sm:mt-0 sm:ml-2"},bo={class:"px-0.5 sm:px-2"},go={key:0},xo={class:"mt-2 mx-2 py-1.5 border-t border-black/[.1] dark:border-vtd-secondary-700/[1]"},ko={class:"mt-1.5 sm:flex sm:flex-row-reverse"},wo=["disabled","textContent"],Eo={__name:"VueTailwindDatePicker",props:{noInput:Boolean,overlay:Boolean,asSingle:Boolean,useRange:Boolean,placeholder:{type:[Boolean,String],default:!1},i18n:{type:String,default:"en"},inputClasses:{type:String,default:""},disabled:{type:Boolean,default:!1},disableInRange:{type:Boolean,default:!0},disableDate:{type:[Boolean,Array,Function],default:!1},autoApply:{type:Boolean,default:!0},shortcuts:{type:[Boolean,Function],default:!0},separator:{type:String,default:" ~ "},formatter:{type:Object,default:()=>({date:"YYYY-MM-DD HH:mm:ss",month:"MMM"})},modelValue:{type:[Array,Object,String],default:()=>[]},startFrom:{type:[Object,String],default:()=>new Date},weekdaysSize:{type:String,default:"short"},options:{type:Object,default:()=>({shortcuts:{today:"Today",yesterday:"Yesterday",past:t=>`Last ${t} Days`,currentMonth:"This Month",pastMonth:"Last Month"},footer:{apply:"Apply",cancel:"Cancel"}})}},emits:["update:modelValue","select:month","select:year","select:right:month","select:right:year","click:prev","click:next","click:right:prev","click:right:next"],setup(t,{expose:n,emit:a}){const e=t,{useCurrentDate:i,useDisableDate:d,useBetweenRange:A,useNextDate:g,usePreviousDate:E,useToValueFromArray:x,useToValueFromString:j}=Yr(),{useVisibleViewport:_}=Cr();u.extend(Tr),u.extend($r),u.extend(Sr),u.extend(Mr),u.extend(Ir),u.extend(Rr);const b=oe(null),B=oe(null),Q=oe(null),D=oe(""),L=oe(null),v=oe(""),k=oe([]),O=oe([]),$=oe(null),S=oe(null),s=Lt({previous:{calendar:!0,month:!1,year:!1},next:{calendar:!0,month:!1,year:!1}}),r=oe({previous:u(),next:u().add(1,"month"),year:{previous:u().year(),next:u().year()},weeks:e.weekdaysSize==="min"?u.weekdaysMin():u.weekdaysShort(),months:e.formatter.month==="MMM"?u.monthsShort():u.months()}),F=pe(()=>r.value.weeks),R=pe(()=>r.value.months),w=pe(()=>{const{previous:c,next:p,year:y}=N(r);return{previous:{date:()=>E(c).concat(i(c)).concat(g(c)).map(o=>(o.today=o.isToday(),o.active=c.month()===o.month(),o.off=c.month()!==o.month(),o.sunday=o.day()===0,o.disabled=d(o,e)&&!V(o),o.inRange=()=>{if(e.asSingle&&!e.useRange)return c.month()!==o.month()},o.hovered=()=>P()&&k.value.length>1?(o.isBetween(k.value[0],k.value[1],"date","()")||o.isBetween(k.value[1],k.value[0],"date","(]"))&&c.month()===o.month():!1,o.duration=()=>!1,o)),month:c&&c.format(e.formatter.month),year:c&&c.year(),years:()=>Array.from({length:12},(o,f)=>y.previous+f),onPrevious:()=>{r.value.previous=c.subtract(1,"month"),a("click:prev",r.value.previous)},onNext:()=>{r.value.previous=c.add(1,"month"),c.diff(p,"month")===-1&&(r.value.next=p.add(1,"month")),a("click:next",r.value.previous)},onPreviousYear:()=>{r.value.year.previous=r.value.year.previous-12},onNextYear:()=>{r.value.year.previous=r.value.year.previous+12},openMonth:()=>{s.previous.month=!s.previous.month,s.previous.year=!1,s.previous.calendar=!s.previous.month},setMount:o=>{r.value.previous=c.month(o),s.previous.month=!s.previous.month,s.previous.year=!1,s.previous.calendar=!s.previous.month,a("select:month",r.value.previous),Ce(()=>{(r.value.next.isSame(r.value.previous,"month")||r.value.next.isBefore(r.value.previous))&&(r.value.next=r.value.previous.add(1,"month")),r.value.year.next=r.value.next.year()})},openYear:()=>{s.previous.year=!s.previous.year,s.previous.month=!1,s.previous.calendar=!s.previous.year},setYear:(o,f)=>{f||(r.value.previous=c.year(o),s.previous.year=!s.previous.year,s.previous.calendar=!s.previous.year,a("select:year",r.value.previous),Ce(()=>{(r.value.next.isSame(r.value.previous,"month")||r.value.next.isBefore(r.value.previous))&&(r.value.next=r.value.previous.add(1,"month")),r.value.year.previous=r.value.previous.year(),r.value.year.next=r.value.next.year()}))}},next:{date:()=>E(p).concat(i(p)).concat(g(p)).map(o=>(o.today=o.isToday(),o.active=p.month()===o.month(),o.off=p.month()!==o.month(),o.sunday=o.day()===0,o.disabled=d(o,e)&&!V(o),o.inRange=()=>{if(e.asSingle&&!e.useRange)return p.month()!==o.month()},o.hovered=()=>k.value.length>1?(o.isBetween(k.value[0],k.value[1],"date","()")||o.isBetween(k.value[1],k.value[0],"date","(]"))&&p.month()===o.month():!1,o.duration=()=>!1,o)),month:p&&p.format(e.formatter.month),year:p&&p.year(),years:()=>Array.from({length:12},(o,f)=>y.next+f),onPrevious:()=>{r.value.next=p.subtract(1,"month"),p.diff(c,"month")===1&&(r.value.previous=c.subtract(1,"month")),a("click:right:prev",r.value.next)},onNext:()=>{r.value.next=p.add(1,"month"),a("click:right:next",r.value.next)},onPreviousYear:()=>{r.value.year.next=r.value.year.next-12},onNextYear:()=>{r.value.year.next=r.value.year.next+12},openMonth:()=>{s.next.month=!s.next.month,s.next.year=!1,s.next.calendar=!s.next.month},setMount:o=>{r.value.next=p.month(o),s.next.month=!s.next.month,s.next.year=!1,s.next.calendar=!s.next.month,a("select:right:month",r.value.next),Ce(()=>{(r.value.previous.isSame(r.value.next,"month")||r.value.previous.isAfter(r.value.next))&&(r.value.previous=r.value.next.subtract(1,"month")),r.value.year.previous=r.value.previous.year()})},openYear:()=>{s.next.year=!s.next.year,s.next.month=!1,s.next.calendar=!s.next.year},setYear:(o,f)=>{f&&(r.value.next=p.year(o),s.next.year=!s.next.year,s.next.month=!1,s.next.calendar=!s.next.year,a("select:right:year",r.value.next),Ce(()=>{(r.value.previous.isSame(r.value.next,"month")||r.value.previous.isAfter(r.value.next))&&(r.value.previous=r.value.next.subtract(1,"month")),r.value.year.previous=r.value.previous.year(),r.value.year.next=r.value.next.year()}))}}}}),Z=oe(!1);setTimeout(()=>{Z.value=!0},250);const X=()=>u().localeData().firstDayOfWeek(),M=c=>{const p=[...c],y=p.shift();return[...p,y]},h=()=>Array.isArray(e.modelValue),m=()=>typeof e.modelValue=="object",P=()=>!e.useRange&&!e.asSingle?!0:!e.useRange&&e.asSingle?!1:e.useRange&&!e.asSingle?!0:!!(e.useRange&&e.asSingle),V=c=>{if(e.disableInRange||v.value==="")return!1;let p,y;if(h()){const[o,f]=e.modelValue;p=o,y=f}else if(m()){if(e.modelValue){const[o,f]=Object.values(e.modelValue);p=o,y=f}}else{const[o,f]=e.modelValue.split(e.separator);p=o,y=f}return c.isBetween(u(p,e.formatter.date,!0),u(y,e.formatter.date,!0),"date","[]")},Y=()=>{$.value=null,S.value=null,k.value=[],L.value=null},I=()=>{if(v.value="",h())a("update:modelValue",[]);else if(m()){const c={},[p,y]=Object.keys(e.modelValue);c[p]="",c[y]="",a("update:modelValue",c)}else a("update:modelValue","");O.value=[],B.value&&B.value.focus()};n({clearPicker:I});const W=()=>{if(P()){const[c,p]=v.value.split(e.separator),[y,o]=[u(c,e.formatter.date,!0),u(p,e.formatter.date,!0)];if(y.isValid()&&o.isValid())if(K(y),K(o),h())a("update:modelValue",[c,p]);else if(m()){const f={},[C,H]=Object.keys(e.modelValue);f[C]=c,f[H]=p,a("update:modelValue",f)}else a("update:modelValue",x({previous:y,next:o},e))}else{const c=u(v.value,e.formatter.date,!0);if(c.isValid())if(K(c),h())a("update:modelValue",[v.value]);else if(m()){const p={},[y]=Object.keys(e.modelValue);p[y]=v.value,a("update:modelValue",p)}else a("update:modelValue",v.value)}},K=(c,p,y)=>{if(P())if($.value)if(S.value=c,e.autoApply){c.isBefore($.value)?v.value=x({previous:c,next:$.value},e):v.value=x({previous:$.value,next:c},e);const[o,f]=v.value.split(e.separator);if(h())a("update:modelValue",[u(o,e.formatter.date,!0).format(e.formatter.date),u(f,e.formatter.date,!0).format(e.formatter.date)]);else if(m()){const C={},[H,te]=Object.keys(e.modelValue);C[H]=o,C[te]=f,a("update:modelValue",C)}else a("update:modelValue",x({previous:u(o,e.formatter.date,!0),next:u(f,e.formatter.date,!0)},e));y&&y(),O.value=[],u(o,e.formatter.date,!0).isSame(u(f,e.formatter.date,!0),"month")||(r.value.previous=u(o,e.formatter.date,!0),r.value.next=u(f,e.formatter.date,!0)),Y()}else{$.value.isAfter(c,"month")?O.value=[c,$.value]:O.value=[$.value,c];const[o,f]=O.value;o.isSame(f,"month")||(r.value.previous=o,r.value.next=f),Y()}else O.value=[],$.value=c,L.value=c,k.value.push(c),O.value.push(c),p?(r.value.next=c,r.value.previous.isSame(c,"month")&&(r.value.next=c.add(1,"month"))):(r.value.previous=c,r.value.next.isSame(c,"month")&&(r.value.previous=r.value.next,r.value.next=c.add(1,"month")));else if(e.autoApply){if(v.value=j(c,e),h())a("update:modelValue",[v.value]);else if(m()){const o={},[f]=Object.keys(e.modelValue);o[f]=v.value,a("update:modelValue",o)}else a("update:modelValue",v.value);y&&y(),O.value=[],Y()}else O.value=[c],Y()},q=c=>{if(O.value.length<1)return!1;let p;if(P()){const[y,o]=O.value;o.isBefore(y)?p=x({previous:o,next:y},e):p=x({previous:y,next:o},e)}else{const[y]=O.value;p=y}if(P()){const[y,o]=p.split(e.separator);if(h())a("update:modelValue",[u(y,e.formatter.date,!0).format(e.formatter.date),u(o,e.formatter.date,!0).format(e.formatter.date)]);else if(m()){const f={},[C,H]=Object.keys(e.modelValue);f[C]=y,f[H]=o,a("update:modelValue",f)}else a("update:modelValue",x({previous:u(y,e.formatter.date,!0),next:u(o,e.formatter.date,!0)},e));v.value=p}else if(v.value=p.format(e.formatter.date),h())a("update:modelValue",[v.value]);else if(m()){const y={},[o]=Object.keys(e.modelValue);y[o]=v.value,a("update:modelValue",y)}else a("update:modelValue",v.value);c&&c()},ae=c=>{if(!P())return!1;if($.value)k.value=[$.value,c];else return k.value=[],!1},me=c=>{if($.value&&e.autoApply)return!1;let p,y;if(k.value.length>1){const[o,f]=k.value;p=u(o,e.formatter.date,!0),y=u(f,e.formatter.date,!0)}else if(h())if(e.autoApply){const[o,f]=e.modelValue;p=o&&u(o,e.formatter.date,!0),y=f&&u(f,e.formatter.date,!0)}else{const[o,f]=O.value;p=u(o,e.formatter.date,!0),y=u(f,e.formatter.date,!0)}else if(m())if(e.autoApply){if(e.modelValue){const[o,f]=Object.values(e.modelValue);p=o&&u(o,e.formatter.date,!0),y=f&&u(f,e.formatter.date,!0)}}else{const[o,f]=O.value;p=u(o,e.formatter.date,!0),y=u(f,e.formatter.date,!0)}else if(e.autoApply){const[o,f]=e.modelValue?e.modelValue.split(e.separator):[!1,!1];p=o&&u(o,e.formatter.date,!0),y=f&&u(f,e.formatter.date,!0)}else{const[o,f]=O.value;p=u(o,e.formatter.date,!0),y=u(f,e.formatter.date,!0)}return p&&y?A(c,{previous:p,next:y}):!1},fe=c=>{const{today:p,active:y,off:o,disabled:f}=c;let C,H,te;if(P())if(h())if(L.value){const[U,ee]=k.value;H=U&&u(U,e.formatter.date,!0),te=ee&&u(ee,e.formatter.date,!0)}else if(e.autoApply){const[U,ee]=e.modelValue;H=U&&u(U,e.formatter.date,!0),te=ee&&u(ee,e.formatter.date,!0)}else{const[U,ee]=O.value;H=U&&u(U,e.formatter.date,!0),te=ee&&u(ee,e.formatter.date,!0)}else if(m())if(L.value){const[U,ee]=k.value;H=U&&u(U,e.formatter.date,!0),te=ee&&u(ee,e.formatter.date,!0)}else if(e.autoApply){const[U,ee]=e.modelValue?Object.values(e.modelValue):[!1,!1];H=U&&u(U,e.formatter.date,!0),te=ee&&u(ee,e.formatter.date,!0)}else{const[U,ee]=O.value;H=U&&u(U,e.formatter.date,!0),te=ee&&u(ee,e.formatter.date,!0)}else if(L.value){const[U,ee]=k.value;H=U&&u(U,e.formatter.date,!0),te=ee&&u(ee,e.formatter.date,!0)}else if(e.autoApply){const[U,ee]=e.modelValue?e.modelValue.split(e.separator):[!1,!1];H=U&&u(U,e.formatter.date,!0),te=ee&&u(ee,e.formatter.date,!0)}else{const[U,ee]=O.value;H=U&&u(U,e.formatter.date,!0),te=ee&&u(ee,e.formatter.date,!0)}else if(h())if(e.autoApply){if(e.modelValue.length>0){const[U]=e.modelValue;H=u(U,e.formatter.date,!0)}}else{const[U]=O.value;H=U&&u(U,e.formatter.date,!0)}else if(m())if(e.autoApply){if(e.modelValue){const[U]=Object.values(e.modelValue);H=u(U,e.formatter.date,!0)}}else{const[U]=O.value;H=U&&u(U,e.formatter.date,!0)}else if(e.autoApply){if(e.modelValue){const[U]=e.modelValue.split(e.separator);H=u(U,e.formatter.date,!0)}}else{const[U]=O.value;H=U&&u(U,e.formatter.date,!0)}return y&&(C=p?"text-vtd-primary-500 font-semibold dark:text-vtd-primary-400 rounded-full focus:bg-vtd-primary-50 focus:text-vtd-secondary-900 focus:border-vtd-primary-300 focus:ring focus:ring-vtd-primary-500 focus:ring-opacity-10 focus:outline-none dark:bg-vtd-secondary-800 dark:text-vtd-secondary-300 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-secondary-300 dark:focus:bg-vtd-secondary-600 dark:focus:text-vtd-secondary-100 dark:focus:border-vtd-primary-500 dark:focus:ring-opacity-25 dark:focus:bg-opacity-50":f?"text-vtd-secondary-600 font-normal disabled:text-vtd-secondary-500 disabled:cursor-not-allowed rounded-full":c.isBetween(H,te,"date","()")?"text-vtd-secondary-700 font-medium dark:text-vtd-secondary-100 rounded-full":"text-vtd-secondary-600 font-medium dark:text-vtd-secondary-200 rounded-full"),o&&(C="text-vtd-secondary-400 font-light disabled:cursor-not-allowed"),H&&te&&!o?(c.isSame(H,"date")&&(C=te.isAfter(H,"date")?"bg-vtd-primary-500 text-white font-bold rounded-l-full disabled:cursor-not-allowed":"bg-vtd-primary-500 text-white font-bold rounded-r-full disabled:cursor-not-allowed",H.isSame(te,"date")&&(C="bg-vtd-primary-500 text-white font-bold rounded-full disabled:cursor-not-allowed")),c.isSame(te,"date")&&(C=te.isAfter(H,"date")?"bg-vtd-primary-500 text-white font-bold rounded-r-full disabled:cursor-not-allowed":"bg-vtd-primary-500 text-white font-bold rounded-l-full disabled:cursor-not-allowed",H.isSame(te,"date")&&(C="bg-vtd-primary-500 text-white font-bold rounded-full disabled:cursor-not-allowed"))):H&&c.isSame(H,"date")&&!o&&(C="bg-vtd-primary-500 text-white font-bold rounded-full disabled:cursor-not-allowed"),C},ge=c=>{let p,y,o;if(p="",!P())return p;if(h())if(k.value.length>1){const[f,C]=k.value;y=f&&u(f,e.formatter.date,!0),o=C&&u(C,e.formatter.date,!0)}else if(e.autoApply){const[f,C]=e.modelValue;y=f&&u(f,e.formatter.date,!0),o=C&&u(C,e.formatter.date,!0)}else{const[f,C]=O.value;y=f&&u(f,e.formatter.date,!0),o=C&&u(C,e.formatter.date,!0)}else if(m())if(k.value.length>1){const[f,C]=k.value;y=f&&u(f,e.formatter.date,!0),o=C&&u(C,e.formatter.date,!0)}else if(e.autoApply){if(e.modelValue){const[f,C]=Object.values(e.modelValue);y=f&&u(f,e.formatter.date,!0),o=C&&u(C,e.formatter.date,!0)}}else{const[f,C]=O.value;y=f&&u(f,e.formatter.date,!0),o=C&&u(C,e.formatter.date,!0)}else if(k.value.length>1){const[f,C]=k.value;y=f&&u(f,e.formatter.date,!0),o=C&&u(C,e.formatter.date,!0)}else if(e.autoApply){const[f,C]=e.modelValue?e.modelValue.split(e.separator):[!1,!1];y=f&&u(f,e.formatter.date,!0),o=C&&u(C,e.formatter.date,!0)}else{const[f,C]=O.value;y=f&&u(f,e.formatter.date,!0),o=C&&u(C,e.formatter.date,!0)}return y&&o&&(c.isSame(y,"date")?(o.isBefore(y)&&(p+=" rounded-r-full inset-0"),y.isBefore(o)&&(p+=" rounded-l-full inset-0")):c.isSame(o,"date")?(o.isBefore(y)&&(p+=" rounded-l-full inset-0"),y.isBefore(o)&&(p+=" rounded-r-full inset-0")):p+=" inset-0"),p},ye=(c,p)=>{r.value.previous=u(c,e.formatter.date,!0),r.value.next=u(p,e.formatter.date,!0),(u.duration(r.value.next.diff(r.value.previous)).$d.months===2||u.duration(r.value.next.diff(r.value.previous)).$d.months===1&&u.duration(r.value.next.diff(r.value.previous)).$d.days===7)&&(r.value.next=r.value.next.subtract(1,"month")),(r.value.next.isSame(r.value.previous,"month")||r.value.next.isBefore(r.value.previous))&&(r.value.next=r.value.previous.add(1,"month"))},se=(c,p)=>{if(P())if(e.autoApply){if(h())a("update:modelValue",[c,p]);else if(m()){const y={},[o,f]=Object.keys(e.modelValue);y[o]=c,y[f]=p,a("update:modelValue",y)}else a("update:modelValue",x({previous:u(c,e.formatter.date,!0),next:u(p,e.formatter.date,!0)},e));v.value=`${c}${e.separator}${p}`}else O.value=[u(c,e.formatter.date,!0),u(p,e.formatter.date,!0)];else if(e.autoApply){if(h())a("update:modelValue",[c]);else if(m()){const y={},[o]=Object.keys(e.modelValue);y[o]=c,a("update:modelValue",y)}else a("update:modelValue",c);v.value=c}else O.value=[u(c,e.formatter.date,!0),u(p,e.formatter.date,!0)];ye(c,p)},ne=c=>{const p=u().format(e.formatter.date),y=u().format(e.formatter.date);se(p,y),c&&c()},ue=c=>{const p=u().subtract(1,"day").format(e.formatter.date),y=u().subtract(1,"day").format(e.formatter.date);se(p,y),c&&c()},_e=(c,p)=>{const y=u().subtract(c-1,"day").format(e.formatter.date),o=u().format(e.formatter.date);se(y,o),p&&p()},be=c=>{const p=u().date(1).format(e.formatter.date),y=u().date(u().daysInMonth()).format(e.formatter.date);se(p,y),c&&c()},Ie=c=>{const p=u().date(1).subtract(1,"month").format(e.formatter.date),y=u().date(0).format(e.formatter.date);se(p,y),c&&c()},Re=(c,p)=>{let y,o;const[f,C]=c.atClick();y=u(f).format(e.formatter.date),o=u(C).format(e.formatter.date),se(y,o),p&&p()};At(()=>O.value,c=>{c.length>0&&(s.previous.calendar=!0,s.previous.month=!1,s.previous.year=!1,s.next.calendar=!0,s.next.month=!1,s.next.year=!1)}),xe(()=>{e.placeholder?D.value=e.placeholder:P()?D.value=`${e.formatter.date}${e.separator}${e.formatter.date}`:D.value=e.formatter.date}),xe(()=>{const c=e.i18n;Ce(()=>{const p=Object.assign({"./locale/af.js":()=>l(()=>import("./af.78b9c933-e32bc916.js"),["assets/af.78b9c933-e32bc916.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/am.js":()=>l(()=>import("./am.1d8f1477-7671b720.js"),["assets/am.1d8f1477-7671b720.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ar-dz.js":()=>l(()=>import("./ar-dz.42346512-1247e878.js"),["assets/ar-dz.42346512-1247e878.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ar-iq.js":()=>l(()=>import("./ar-iq.457ddb59-a1d6ce56.js"),["assets/ar-iq.457ddb59-a1d6ce56.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ar-kw.js":()=>l(()=>import("./ar-kw.1cf9cf9c-a852bcf2.js"),["assets/ar-kw.1cf9cf9c-a852bcf2.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ar-ly.js":()=>l(()=>import("./ar-ly.3d4ab74a-d5e2b7ef.js"),["assets/ar-ly.3d4ab74a-d5e2b7ef.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ar-ma.js":()=>l(()=>import("./ar-ma.2649ce4c-2446d7d2.js"),["assets/ar-ma.2649ce4c-2446d7d2.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ar-sa.js":()=>l(()=>import("./ar-sa.3924e169-d1279002.js"),["assets/ar-sa.3924e169-d1279002.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ar-tn.js":()=>l(()=>import("./ar-tn.6d7d9860-8d5cab1c.js"),["assets/ar-tn.6d7d9860-8d5cab1c.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ar.js":()=>l(()=>import("./ar.a8232bae-ca537a13.js"),["assets/ar.a8232bae-ca537a13.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/az.js":()=>l(()=>import("./az.7d7f4a08-1ddbb10b.js"),["assets/az.7d7f4a08-1ddbb10b.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/be.js":()=>l(()=>import("./be.36888435-a33845e6.js"),["assets/be.36888435-a33845e6.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/bg.js":()=>l(()=>import("./bg.9f42555f-e9ba56fc.js"),["assets/bg.9f42555f-e9ba56fc.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/bi.js":()=>l(()=>import("./bi.eb4985db-ac777355.js"),["assets/bi.eb4985db-ac777355.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/bm.js":()=>l(()=>import("./bm.fc6c02b0-4feb1c1b.js"),["assets/bm.fc6c02b0-4feb1c1b.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/bn-bd.js":()=>l(()=>import("./bn-bd.54903b23-0951f5b0.js"),["assets/bn-bd.54903b23-0951f5b0.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/bn.js":()=>l(()=>import("./bn.5ccaeabb-a917ae11.js"),["assets/bn.5ccaeabb-a917ae11.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/bo.js":()=>l(()=>import("./bo.f62543f4-895053b2.js"),["assets/bo.f62543f4-895053b2.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/br.js":()=>l(()=>import("./br.d8150366-37a8e72a.js"),["assets/br.d8150366-37a8e72a.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/bs.js":()=>l(()=>import("./bs.36836819-a3017428.js"),["assets/bs.36836819-a3017428.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ca.js":()=>l(()=>import("./ca.072fd974-9382ae15.js"),["assets/ca.072fd974-9382ae15.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/cs.js":()=>l(()=>import("./cs.c487664b-c0cb9038.js"),["assets/cs.c487664b-c0cb9038.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/cv.js":()=>l(()=>import("./cv.dd61033c-4c64206d.js"),["assets/cv.dd61033c-4c64206d.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/cy.js":()=>l(()=>import("./cy.961b82f0-3c986b3c.js"),["assets/cy.961b82f0-3c986b3c.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/da.js":()=>l(()=>import("./da.5f65840b-2b7844d2.js"),["assets/da.5f65840b-2b7844d2.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/de-at.js":()=>l(()=>import("./de-at.d2039119-e74a7316.js"),["assets/de-at.d2039119-e74a7316.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/de-ch.js":()=>l(()=>import("./de-ch.c2c590f0-1d273941.js"),["assets/de-ch.c2c590f0-1d273941.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/de.js":()=>l(()=>import("./de.e59fd8ef-09da5552.js"),["assets/de.e59fd8ef-09da5552.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/dv.js":()=>l(()=>import("./dv.7bde5145-cf30ed8c.js"),["assets/dv.7bde5145-cf30ed8c.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/el.js":()=>l(()=>import("./el.5081a6aa-ea89f490.js"),["assets/el.5081a6aa-ea89f490.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/en-au.js":()=>l(()=>import("./en-au.ad1bb4e7-d9675615.js"),["assets/en-au.ad1bb4e7-d9675615.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/en-ca.js":()=>l(()=>import("./en-ca.21ddbca0-18a25a6e.js"),["assets/en-ca.21ddbca0-18a25a6e.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/en-gb.js":()=>l(()=>import("./en-gb.08a92534-1cacd784.js"),["assets/en-gb.08a92534-1cacd784.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/en-ie.js":()=>l(()=>import("./en-ie.5fdaa7a9-4cb4eec9.js"),["assets/en-ie.5fdaa7a9-4cb4eec9.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/en-il.js":()=>l(()=>import("./en-il.0ed4a502-84ef3c3a.js"),["assets/en-il.0ed4a502-84ef3c3a.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/en-in.js":()=>l(()=>import("./en-in.c9d56fc3-a8eda734.js"),["assets/en-in.c9d56fc3-a8eda734.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/en-nz.js":()=>l(()=>import("./en-nz.11b9f1e2-36492462.js"),["assets/en-nz.11b9f1e2-36492462.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/en-sg.js":()=>l(()=>import("./en-sg.db434006-d4769e98.js"),["assets/en-sg.db434006-d4769e98.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/en-tt.js":()=>l(()=>import("./en-tt.1955abe1-90d6d598.js"),["assets/en-tt.1955abe1-90d6d598.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/en.js":()=>l(()=>import("./en.c289298e-aaf0afdb.js"),[]),"./locale/eo.js":()=>l(()=>import("./eo.9dd27be9-01edf6c6.js"),["assets/eo.9dd27be9-01edf6c6.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/es-do.js":()=>l(()=>import("./es-do.0d938f68-396b7cf8.js"),["assets/es-do.0d938f68-396b7cf8.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/es-mx.js":()=>l(()=>import("./es-mx.d46c7f89-50dafedc.js"),["assets/es-mx.d46c7f89-50dafedc.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/es-pr.js":()=>l(()=>import("./es-pr.7ba6c2af-72632b00.js"),["assets/es-pr.7ba6c2af-72632b00.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/es-us.js":()=>l(()=>import("./es-us.76724c6d-5105ef8b.js"),["assets/es-us.76724c6d-5105ef8b.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/es.js":()=>l(()=>import("./es.df7fd3d9-0259225d.js"),["assets/es.df7fd3d9-0259225d.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/et.js":()=>l(()=>import("./et.777d6fee-37281e3d.js"),["assets/et.777d6fee-37281e3d.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/eu.js":()=>l(()=>import("./eu.ace54020-3172ac6a.js"),["assets/eu.ace54020-3172ac6a.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/fa.js":()=>l(()=>import("./fa.fd258ea1-1543ed2d.js"),["assets/fa.fd258ea1-1543ed2d.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/fi.js":()=>l(()=>import("./fi.0f01b077-2c6a32d8.js"),["assets/fi.0f01b077-2c6a32d8.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/fo.js":()=>l(()=>import("./fo.c7a2f5b1-84389730.js"),["assets/fo.c7a2f5b1-84389730.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/fr-ca.js":()=>l(()=>import("./fr-ca.563e4fce-48085485.js"),["assets/fr-ca.563e4fce-48085485.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/fr-ch.js":()=>l(()=>import("./fr-ch.b03dbf82-c8b86dcf.js"),["assets/fr-ch.b03dbf82-c8b86dcf.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/fr.js":()=>l(()=>import("./fr.04ee9d68-3c7178a0.js"),["assets/fr.04ee9d68-3c7178a0.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/fy.js":()=>l(()=>import("./fy.28222af1-9d3b4f19.js"),["assets/fy.28222af1-9d3b4f19.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ga.js":()=>l(()=>import("./ga.7661ff1f-8dea347f.js"),["assets/ga.7661ff1f-8dea347f.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/gd.js":()=>l(()=>import("./gd.140449ba-08c6d1b0.js"),["assets/gd.140449ba-08c6d1b0.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/gl.js":()=>l(()=>import("./gl.25bd5a97-88f514aa.js"),["assets/gl.25bd5a97-88f514aa.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/gom-latn.js":()=>l(()=>import("./gom-latn.3ee46935-cee75fc5.js"),["assets/gom-latn.3ee46935-cee75fc5.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/gu.js":()=>l(()=>import("./gu.024a987c-1ae030d9.js"),["assets/gu.024a987c-1ae030d9.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/he.js":()=>l(()=>import("./he.5d94292f-0aef2b25.js"),["assets/he.5d94292f-0aef2b25.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/hi.js":()=>l(()=>import("./hi.9036dcd4-bea5ed1d.js"),["assets/hi.9036dcd4-bea5ed1d.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/hr.js":()=>l(()=>import("./hr.5062be1d-342dd4aa.js"),["assets/hr.5062be1d-342dd4aa.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ht.js":()=>l(()=>import("./ht.69059849-15144b9e.js"),["assets/ht.69059849-15144b9e.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/hu.js":()=>l(()=>import("./hu.33a15367-0be1eb9f.js"),["assets/hu.33a15367-0be1eb9f.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/hy-am.js":()=>l(()=>import("./hy-am.79001de4-6b5b733c.js"),["assets/hy-am.79001de4-6b5b733c.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/id.js":()=>l(()=>import("./id.66cfe616-d5991a4e.js"),["assets/id.66cfe616-d5991a4e.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/is.js":()=>l(()=>import("./is.c7a21122-2d976285.js"),["assets/is.c7a21122-2d976285.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/it-ch.js":()=>l(()=>import("./it-ch.3b126b32-bd128838.js"),["assets/it-ch.3b126b32-bd128838.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/it.js":()=>l(()=>import("./it.c151e274-11765b5a.js"),["assets/it.c151e274-11765b5a.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ja.js":()=>l(()=>import("./ja.c78cf541-0c130606.js"),["assets/ja.c78cf541-0c130606.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/jv.js":()=>l(()=>import("./jv.a747a1a3-81c1463c.js"),["assets/jv.a747a1a3-81c1463c.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ka.js":()=>l(()=>import("./ka.41d428db-02adde97.js"),["assets/ka.41d428db-02adde97.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/kk.js":()=>l(()=>import("./kk.3b7b4220-bbca7a5d.js"),["assets/kk.3b7b4220-bbca7a5d.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/km.js":()=>l(()=>import("./km.c842e4e6-8be0b7d4.js"),["assets/km.c842e4e6-8be0b7d4.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/kn.js":()=>l(()=>import("./kn.ee6d6468-96592432.js"),["assets/kn.ee6d6468-96592432.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ko.js":()=>l(()=>import("./ko.0d60f7bc-14699c93.js"),["assets/ko.0d60f7bc-14699c93.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ku.js":()=>l(()=>import("./ku.9f8e49b8-8c556bbf.js"),["assets/ku.9f8e49b8-8c556bbf.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ky.js":()=>l(()=>import("./ky.93291eaa-dde98b57.js"),["assets/ky.93291eaa-dde98b57.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/lb.js":()=>l(()=>import("./lb.d1834c83-1d9e697b.js"),["assets/lb.d1834c83-1d9e697b.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/lo.js":()=>l(()=>import("./lo.31c82cd3-7ac3bc05.js"),["assets/lo.31c82cd3-7ac3bc05.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/lt.js":()=>l(()=>import("./lt.5798cdca-6be8c200.js"),["assets/lt.5798cdca-6be8c200.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/lv.js":()=>l(()=>import("./lv.7222c068-a42de473.js"),["assets/lv.7222c068-a42de473.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/me.js":()=>l(()=>import("./me.5ebeffe5-59eb33bf.js"),["assets/me.5ebeffe5-59eb33bf.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/mi.js":()=>l(()=>import("./mi.1c2375d0-95f1d4c0.js"),["assets/mi.1c2375d0-95f1d4c0.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/mk.js":()=>l(()=>import("./mk.3746980f-e2f7d4c9.js"),["assets/mk.3746980f-e2f7d4c9.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ml.js":()=>l(()=>import("./ml.09f4db49-5499d262.js"),["assets/ml.09f4db49-5499d262.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/mn.js":()=>l(()=>import("./mn.3937f421-d600580a.js"),["assets/mn.3937f421-d600580a.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/mr.js":()=>l(()=>import("./mr.764d0b33-6a29a95a.js"),["assets/mr.764d0b33-6a29a95a.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ms-my.js":()=>l(()=>import("./ms-my.70947be7-6d56c03c.js"),["assets/ms-my.70947be7-6d56c03c.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ms.js":()=>l(()=>import("./ms.62cce7b5-858e693e.js"),["assets/ms.62cce7b5-858e693e.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/mt.js":()=>l(()=>import("./mt.628e2b0e-9832f40a.js"),["assets/mt.628e2b0e-9832f40a.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/my.js":()=>l(()=>import("./my.b8e9dc5f-296c3403.js"),["assets/my.b8e9dc5f-296c3403.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/nb.js":()=>l(()=>import("./nb.959a2d1a-dc68dd40.js"),["assets/nb.959a2d1a-dc68dd40.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ne.js":()=>l(()=>import("./ne.bc92b43e-46e0d80a.js"),["assets/ne.bc92b43e-46e0d80a.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/nl-be.js":()=>l(()=>import("./nl-be.67b82913-dee97532.js"),["assets/nl-be.67b82913-dee97532.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/nl.js":()=>l(()=>import("./nl.c9873428-d4bdf4a2.js"),["assets/nl.c9873428-d4bdf4a2.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/nn.js":()=>l(()=>import("./nn.4610d068-dce0107c.js"),["assets/nn.4610d068-dce0107c.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/oc-lnc.js":()=>l(()=>import("./oc-lnc.8945d1ce-26f3409c.js"),["assets/oc-lnc.8945d1ce-26f3409c.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/pa-in.js":()=>l(()=>import("./pa-in.52b608bf-f74faaf7.js"),["assets/pa-in.52b608bf-f74faaf7.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/pl.js":()=>l(()=>import("./pl.4c1086f0-6df4055e.js"),["assets/pl.4c1086f0-6df4055e.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/pt-br.js":()=>l(()=>import("./pt-br.6b29a398-dbffffd4.js"),["assets/pt-br.6b29a398-dbffffd4.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/pt.js":()=>l(()=>import("./pt.08903cb0-f7d53949.js"),["assets/pt.08903cb0-f7d53949.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/rn.js":()=>l(()=>import("./rn.b311783d-4362fa1c.js"),["assets/rn.b311783d-4362fa1c.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ro.js":()=>l(()=>import("./ro.5ebccc9f-61ee34c3.js"),["assets/ro.5ebccc9f-61ee34c3.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ru.js":()=>l(()=>import("./ru.d91e83d3-835fd8e1.js"),["assets/ru.d91e83d3-835fd8e1.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/rw.js":()=>l(()=>import("./rw.a9406a95-36f7d0cf.js"),["assets/rw.a9406a95-36f7d0cf.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/sd.js":()=>l(()=>import("./sd.f2aeb928-3e8c0cd5.js"),["assets/sd.f2aeb928-3e8c0cd5.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/se.js":()=>l(()=>import("./se.32bf71de-07f91017.js"),["assets/se.32bf71de-07f91017.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/si.js":()=>l(()=>import("./si.c99551c6-ea48ed9b.js"),["assets/si.c99551c6-ea48ed9b.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/sk.js":()=>l(()=>import("./sk.de35eedb-6e35dd99.js"),["assets/sk.de35eedb-6e35dd99.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/sl.js":()=>l(()=>import("./sl.22c4ba87-2bb255fe.js"),["assets/sl.22c4ba87-2bb255fe.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/sq.js":()=>l(()=>import("./sq.b30cbaca-3cc01749.js"),["assets/sq.b30cbaca-3cc01749.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/sr-cyrl.js":()=>l(()=>import("./sr-cyrl.0fd1daac-0b5a9682.js"),["assets/sr-cyrl.0fd1daac-0b5a9682.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/sr.js":()=>l(()=>import("./sr.2707a1bd-be1b6f6d.js"),["assets/sr.2707a1bd-be1b6f6d.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ss.js":()=>l(()=>import("./ss.9ac39d7a-5db317ae.js"),["assets/ss.9ac39d7a-5db317ae.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/sv-fi.js":()=>l(()=>import("./sv-fi.6bac7f9a-0a8edc52.js"),["assets/sv-fi.6bac7f9a-0a8edc52.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/sv.js":()=>l(()=>import("./sv.caf7bb40-0af70b17.js"),["assets/sv.caf7bb40-0af70b17.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/sw.js":()=>l(()=>import("./sw.59b71c0d-48238711.js"),["assets/sw.59b71c0d-48238711.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ta.js":()=>l(()=>import("./ta.4227d78a-efceddb9.js"),["assets/ta.4227d78a-efceddb9.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/te.js":()=>l(()=>import("./te.176edbc1-4bc6b60f.js"),["assets/te.176edbc1-4bc6b60f.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/tet.js":()=>l(()=>import("./tet.caf866ff-5f67c734.js"),["assets/tet.caf866ff-5f67c734.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/tg.js":()=>l(()=>import("./tg.a99bbdc2-8264d115.js"),["assets/tg.a99bbdc2-8264d115.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/th.js":()=>l(()=>import("./th.8c157ad1-48482510.js"),["assets/th.8c157ad1-48482510.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/tk.js":()=>l(()=>import("./tk.882ec3cc-a56eb008.js"),["assets/tk.882ec3cc-a56eb008.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/tl-ph.js":()=>l(()=>import("./tl-ph.1f9806a1-2e5783c0.js"),["assets/tl-ph.1f9806a1-2e5783c0.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/tlh.js":()=>l(()=>import("./tlh.52616aff-0488327f.js"),["assets/tlh.52616aff-0488327f.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/tr.js":()=>l(()=>import("./tr.5000c4a3-706dcb0f.js"),["assets/tr.5000c4a3-706dcb0f.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/tzl.js":()=>l(()=>import("./tzl.4084c20a-cc01e881.js"),["assets/tzl.4084c20a-cc01e881.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/tzm-latn.js":()=>l(()=>import("./tzm-latn.6fc428fb-21ac202e.js"),["assets/tzm-latn.6fc428fb-21ac202e.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/tzm.js":()=>l(()=>import("./tzm.277f8193-16492d80.js"),["assets/tzm.277f8193-16492d80.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ug-cn.js":()=>l(()=>import("./ug-cn.17ee2d0d-67d1ce46.js"),["assets/ug-cn.17ee2d0d-67d1ce46.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/uk.js":()=>l(()=>import("./uk.78979c97-33fbf289.js"),["assets/uk.78979c97-33fbf289.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/ur.js":()=>l(()=>import("./ur.002c5392-a1ffc0d6.js"),["assets/ur.002c5392-a1ffc0d6.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/uz-latn.js":()=>l(()=>import("./uz-latn.7fa3ceb8-1416d823.js"),["assets/uz-latn.7fa3ceb8-1416d823.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/uz.js":()=>l(()=>import("./uz.12fffa69-ff330ba3.js"),["assets/uz.12fffa69-ff330ba3.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/vi.js":()=>l(()=>import("./vi.b41f7d47-fd30abef.js"),["assets/vi.b41f7d47-fd30abef.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/x-pseudo.js":()=>l(()=>import("./x-pseudo.2a72f78d-363ef206.js"),["assets/x-pseudo.2a72f78d-363ef206.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/yo.js":()=>l(()=>import("./yo.13a0bc5a-e5207c11.js"),["assets/yo.13a0bc5a-e5207c11.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/zh-cn.js":()=>l(()=>import("./zh-cn.41c9e979-7293508a.js"),["assets/zh-cn.41c9e979-7293508a.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/zh-hk.js":()=>l(()=>import("./zh-hk.f63ec35c-2432adb3.js"),["assets/zh-hk.f63ec35c-2432adb3.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/zh-tw.js":()=>l(()=>import("./zh-tw.5843df61-743efd03.js"),["assets/zh-tw.5843df61-743efd03.js","assets/index-bfe6943f.js","assets/index-465427c9.css"]),"./locale/zh.js":()=>l(()=>import("./zh.3d7de479-8725578e.js"),["assets/zh.3d7de479-8725578e.js","assets/index-bfe6943f.js","assets/index-465427c9.css"])});for(const y in p)p[y]().then(()=>{u.locale(c);let o,f;if(P()){if(h()){if(e.modelValue.length>0){const[H,te]=e.modelValue;o=u(H,e.formatter.date,!0),f=u(te,e.formatter.date,!0)}}else if(m()){if(!Tt(e.modelValue))try{Object.keys(e.modelValue)}catch{console.warn("[Vue Tailwind Datepicker]: It looks like you want to use Object as the argument %cv-model","font-style: italic; color: #42b883;",", but you pass it undefined or null."),console.warn("[Vue Tailwind Datepicker]: We has replace with %c{ startDate: '', endDate: '' }","font-style: italic; color: #42b883;",", but you can replace manually."),a("update:modelValue",{startDate:"",endDate:""})}if(e.modelValue){const[H,te]=Object.values(e.modelValue);o=H&&u(H,e.formatter.date,!0),f=te&&u(te,e.formatter.date,!0)}}else if(e.modelValue){const[H,te]=e.modelValue.split(e.separator);o=u(H,e.formatter.date,!0),f=u(te,e.formatter.date,!0)}o&&f?(v.value=x({previous:o,next:f},e),f.isBefore(o,"month")?(r.value.previous=f,r.value.next=o,r.value.year.previous=f.year(),r.value.year.next=o.year()):f.isSame(o,"month")?(r.value.previous=o,r.value.next=f.add(1,"month"),r.value.year.previous=o.year(),r.value.year.next=o.add(1,"year").year()):(r.value.previous=o,r.value.next=f,r.value.year.previous=o.year(),r.value.year.next=f.year()),e.autoApply||(O.value=[o,f])):(r.value.previous=u(e.startFrom),r.value.next=u(e.startFrom).add(1,"month"),r.value.year.previous=r.value.previous.year(),r.value.year.next=r.value.next.year())}else{if(h()){if(e.modelValue.length>0){const[H]=e.modelValue;o=u(H,e.formatter.date,!0)}}else if(m()){if(e.modelValue){const[H]=Object.values(e.modelValue);o=u(H,e.formatter.date,!0)}}else if(e.modelValue.length){const[H]=e.modelValue.split(e.separator);o=u(H,e.formatter.date,!0)}o&&o.isValid()?(v.value=j(o,e),r.value.previous=o,r.value.next=o.add(1,"month"),r.value.year.previous=o.year(),r.value.year.next=o.add(1,"year").year(),e.autoApply||(O.value=[o])):(r.value.previous=u(e.startFrom),r.value.next=u(e.startFrom).add(1,"month"),r.value.year.previous=r.value.previous.year(),r.value.year.next=r.value.next.year())}const C=e.weekdaysSize==="min"?u.weekdaysMin():u.weekdaysShort();r.value.weeks=X()?M(C):C,r.value.months=e.formatter.month==="MMM"?u.monthsShort():u.months()}).catch(o=>{console.warn(o.message)})})});const Ye=c=>(c&&Q.value===null&&(Q.value=_(b.value)),c&&Q.value?"place-right":"place-left"),Ot=c=>(c&&Q.value===null&&(Q.value=_(b.value)),Q.value?"left-auto right-0":"left-0 right-auto");return ce("isBetweenRange",me),ce("betweenRangeClasses",ge),ce("datepickerClasses",fe),ce("atMouseOver",ae),ce("setToToday",ne),ce("setToYesterday",ue),ce("setToLastDay",_e),ce("setToThisMonth",be),ce("setToLastMonth",Ie),ce("setToCustomShortcut",Re),(c,p)=>e.noInput?Z.value?(G(),J("div",uo,[T("div",co,[T("div",vo,[e.shortcuts?(G(),Be(at,{key:0,shortcuts:e.shortcuts,"as-range":P(),"as-single":e.asSingle,i18n:e.options.shortcuts},null,8,["shortcuts","as-range","as-single","i18n"])):he("",!0),T("div",po,[P()&&!e.asSingle?(G(),J("div",fo,ho)):he("",!0),T("div",{class:we(["relative w-full lg:w-80",{"mb-3 sm:mb-0 sm:mr-2 md:w-1/2":P()&&!e.asSingle}])},[re(ze,{panel:s.previous,calendar:N(w).previous},null,8,["panel","calendar"]),T("div",yo,[ie(re(We,{months:N(R),"onUpdate:month":N(w).previous.setMount},null,8,["months","onUpdate:month"]),[[de,s.previous.month]]),ie(re(Ze,{years:N(w).previous.years(),"onUpdate:year":N(w).previous.setYear},null,8,["years","onUpdate:year"]),[[de,s.previous.year]]),ie(T("div",null,[re(Ge,{weeks:N(F)},null,8,["weeks"]),re(Qe,{calendar:N(w).previous,weeks:N(F),"as-range":P(),"onUpdate:date":p[2]||(p[2]=(y,o)=>K(y,o))},null,8,["calendar","weeks","as-range"])],512),[[de,s.previous.calendar]])])],2),P()&&!e.asSingle?(G(),J("div",_o,[re(ze,{"as-prev-or-next":"",panel:s.next,calendar:N(w).next},null,8,["panel","calendar"]),T("div",bo,[ie(re(We,{months:N(R),"onUpdate:month":N(w).next.setMount},null,8,["months","onUpdate:month"]),[[de,s.next.month]]),ie(re(Ze,{"as-prev-or-next":"",years:N(w).next.years(),"onUpdate:year":N(w).next.setYear},null,8,["years","onUpdate:year"]),[[de,s.next.year]]),ie(T("div",null,[re(Ge,{weeks:N(F)},null,8,["weeks"]),re(Qe,{"as-prev-or-next":"",calendar:N(w).next,weeks:N(F),"as-range":P(),"onUpdate:date":p[3]||(p[3]=(y,o)=>K(y,o))},null,8,["calendar","weeks","as-range"])],512),[[de,s.next.calendar]])])])):he("",!0)])]),e.autoApply?he("",!0):(G(),J("div",go,[T("div",xo,[T("div",ko,[T("button",{type:"button",class:"away-apply-picker w-full transition ease-out duration-300 inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-vtd-primary-600 text-base font-medium text-white hover:bg-vtd-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-vtd-primary-500 sm:ml-3 sm:w-auto sm:text-sm dark:ring-offset-vtd-secondary-800 disabled:cursor-not-allowed",disabled:e.asSingle?O.value.length<1:O.value.length<2,onClick:p[4]||(p[4]=y=>q()),textContent:le(e.options.footer.apply)},null,8,wo)])])]))])])):he("",!0):(G(),Be(N(_t),{key:0,as:"div",id:"vtd",class:"relative w-full"},{default:Le(({open:y})=>[e.overlay&&!e.disabled?(G(),Be(N(Lr),{key:0,class:"fixed inset-0 bg-black opacity-30"})):he("",!0),re(N(jr),{as:"label",class:"relative block"},{default:Le(()=>[$t(c.$slots,"default",{value:v.value,placeholder:D.value,clear:I},()=>[ie(T("input",St({ref_key:"VtdInputRef",ref:B,type:"text",class:["relative block w-full",[e.disabled?"cursor-default opacity-50":"opacity-100",t.inputClasses||"pl-3 pr-12 py-2.5 rounded-lg overflow-hidden border-solid text-sm text-vtd-secondary-700 placeholder-vtd-secondary-400 transition-colors bg-white border border-vtd-secondary-300 focus:border-vtd-primary-300 focus:ring focus:ring-vtd-primary-500 focus:ring-opacity-10 focus:outline-none dark:bg-vtd-secondary-800 dark:border-vtd-secondary-700 dark:text-vtd-secondary-100 dark:placeholder-vtd-secondary-500 dark:focus:border-vtd-primary-500 dark:focus:ring-opacity-20"]],disabled:e.disabled,autocomplete:"off","data-lpignore":"true","data-form-type":"other"},c.$attrs,{"onUpdate:modelValue":p[0]||(p[0]=o=>v.value=o),placeholder:D.value,onKeyup:W}),null,16,Br),[[Mt,v.value]]),T("div",Fr,[T("button",{type:"button",disabled:e.disabled,class:we([e.disabled?"cursor-default opacity-50":"opacity-100","px-2 py-1 mr-1 focus:outline-none text-vtd-secondary-400 dark:text-opacity-70 rounded-md"]),onClick:p[1]||(p[1]=o=>e.disabled?!1:v.value?I():c.$refs.VtdInputRef.focus())},[(G(),J("svg",Hr,[v.value?(G(),J("path",Ur)):(G(),J("path",zr))]))],10,Nr)])])]),_:3}),re(st,{"enter-from-class":"opacity-0 translate-y-3","enter-to-class":"opacity-100 translate-y-0","enter-active-class":"transform transition ease-out duration-200","leave-active-class":"transform transition ease-in duration-150","leave-from-class":"opacity-100 translate-y-0","leave-to-class":"opacity-0 translate-y-3"},{default:Le(()=>[e.disabled?he("",!0):(G(),Be(N(Ar),{key:0,as:"div",class:"relative z-50"},{default:Le(({close:o})=>[T("div",{class:we(["absolute z-50 top-full sm:mt-2.5",Ot(y)])},[T("div",{ref_key:"VtdRef",ref:b,class:"fixed inset-0 z-50 overflow-y-auto sm:overflow-visible sm:static sm:z-auto bg-white dark:bg-vtd-secondary-800 sm:rounded-lg shadow-sm"},[T("div",{class:we(["vtd-datepicker static sm:relative w-full bg-white sm:rounded-lg sm:shadow-sm border-0 sm:border border-black/[.1] px-3 py-3 sm:px-4 sm:py-4 dark:bg-vtd-secondary-800 dark:border-vtd-secondary-700/[1]",Ye(y)])},[T("div",Wr,[e.shortcuts?(G(),Be(at,{key:0,shortcuts:e.shortcuts,"as-range":P(),"as-single":e.asSingle,i18n:e.options.shortcuts,close:o},null,8,["shortcuts","as-range","as-single","i18n","close"])):he("",!0),T("div",Gr,[P()&&!e.asSingle?(G(),J("div",Zr,Kr)):he("",!0),T("div",{class:we(["relative",{"mb-3 sm:mb-0 sm:mr-2 w-full md:w-1/2 lg:w-80":P()&&!e.asSingle,"w-full":!P()&&e.asSingle}])},[re(ze,{panel:s.previous,calendar:N(w).previous},null,8,["panel","calendar"]),T("div",Jr,[ie(re(We,{months:N(R),"onUpdate:month":N(w).previous.setMount},null,8,["months","onUpdate:month"]),[[de,s.previous.month]]),ie(re(Ze,{years:N(w).previous.years(),"onUpdate:year":N(w).previous.setYear},null,8,["years","onUpdate:year"]),[[de,s.previous.year]]),ie(T("div",null,[re(Ge,{weeks:N(F)},null,8,["weeks"]),re(Qe,{calendar:N(w).previous,weeks:N(F),"as-range":P(),"onUpdate:date":(f,C)=>K(f,C,o)},null,8,["calendar","weeks","as-range","onUpdate:date"])],512),[[de,s.previous.calendar]])])],2),P()&&!e.asSingle?(G(),J("div",Xr,[re(ze,{"as-prev-or-next":"",panel:s.next,calendar:N(w).next},null,8,["panel","calendar"]),T("div",qr,[ie(re(We,{months:N(R),"onUpdate:month":N(w).next.setMount},null,8,["months","onUpdate:month"]),[[de,s.next.month]]),ie(re(Ze,{"as-prev-or-next":"",years:N(w).next.years(),"onUpdate:year":N(w).next.setYear},null,8,["years","onUpdate:year"]),[[de,s.next.year]]),ie(T("div",null,[re(Ge,{weeks:N(F)},null,8,["weeks"]),re(Qe,{"as-prev-or-next":"",calendar:N(w).next,weeks:N(F),"as-range":P(),"onUpdate:date":(f,C)=>K(f,C,o)},null,8,["calendar","weeks","as-range","onUpdate:date"])],512),[[de,s.next.calendar]])])])):he("",!0)])]),e.autoApply?(G(),J("div",no,[T("div",so,[T("div",lo,[T("button",{type:"button",onClick:f=>o(),class:"away-cancel-picker w-full transition ease-out duration-300 inline-flex justify-center rounded-md border border-vtd-secondary-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-vtd-secondary-700 hover:bg-vtd-secondary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-vtd-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:ring-offset-vtd-secondary-800",textContent:le(e.options.footer.cancel)},null,8,io)])])])):(G(),J("div",eo,[T("div",to,[T("div",ro,[T("button",{type:"button",class:"away-apply-picker w-full transition ease-out duration-300 inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-vtd-primary-600 text-base font-medium text-white hover:bg-vtd-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-vtd-primary-500 sm:ml-3 sm:w-auto sm:text-sm dark:ring-offset-vtd-secondary-800 disabled:cursor-not-allowed",disabled:e.asSingle?O.value.length<1:O.value.length<2,onClick:f=>q(o),textContent:le(e.options.footer.apply)},null,8,oo),T("button",{type:"button",onClick:f=>o(),class:"mt-3 away-cancel-picker w-full transition ease-out duration-300 inline-flex justify-center rounded-md border border-vtd-secondary-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-vtd-secondary-700 hover:bg-vtd-secondary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-vtd-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:ring-offset-vtd-secondary-800",textContent:le(e.options.footer.cancel)},null,8,ao)])])]))],2)],512)],2)]),_:2},1024))]),_:2},1024)]),_:3}))}},Vt=(()=>{const t=Eo;return t.install=n=>{n.component("VueTailwindDatepicker",t)},t})(),Do=Object.freeze(Object.defineProperty({__proto__:null,default:Vt},Symbol.toStringTag,{value:"Module"}));Object.entries(Do).forEach(([t,n])=>{t!=="default"&&(Vt[t]=n)});(function(){try{if(typeof document<"u"){var t=document.createElement("style");t.appendChild(document.createTextNode('.vtd-datepicker-overlay.open:before{display:block;opacity:.5}.vtd-datepicker:before{--vtd-datepicker: 0px;content:"";position:absolute;top:0px;height:1rem;width:1rem;border-width:1px;border-color:#0000001a;--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity));--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.dark .vtd-datepicker:before{--tw-border-opacity: 1;border-color:rgb(55 65 81 / var(--tw-border-opacity));--tw-bg-opacity: 1;background-color:rgb(31 41 55 / var(--tw-bg-opacity))}.vtd-datepicker:before{transform:translate(50%,-50%) rotate(-45deg);-webkit-clip-path:polygon(calc(var(--vtd-datepicker) * -1) calc(var(--vtd-datepicker) * -1),calc(100% + var(--vtd-datepicker)) calc(var(--vtd-datepicker) * -1),calc(100% + var(--vtd-datepicker)) calc(100% + var(--vtd-datepicker)));clip-path:polygon(calc(var(--vtd-datepicker) * -1) calc(var(--vtd-datepicker) * -1),calc(100% + var(--vtd-datepicker)) calc(var(--vtd-datepicker) * -1),calc(100% + var(--vtd-datepicker)) calc(100% + var(--vtd-datepicker)))}.vtd-datepicker.place-left:before{left:.25rem}.dark .vtd-datepicker.place-left:before{--tw-border-opacity: 1;border-color:rgb(55 65 81 / var(--tw-border-opacity));--tw-bg-opacity: 1;background-color:rgb(31 41 55 / var(--tw-bg-opacity))}.vtd-datepicker.place-right:before{right:1.25rem}.dark .vtd-datepicker.place-right:before{--tw-border-opacity: 1;border-color:rgb(55 65 81 / var(--tw-border-opacity));--tw-bg-opacity: 1;background-color:rgb(31 41 55 / var(--tw-bg-opacity))}')),document.head.appendChild(t)}}catch(n){console.error("vite-plugin-css-injected-by-js",n)}})();export{Vt as S,u as l};
