import{a as u,o,c as Y,w as d,e as n,h as le,d as e,u as a,z as ne,x as E,E as I,H as L,l as D,v as R,p as re,O as ie,D as de,G as ce,r as q,b as i,B as ue,a4 as me,_ as pe,F as T,f as U,y as C,t as p,T as G,n as P,a3 as S}from"./index-04cb9130.js";import{_ as ve}from"./AppTopbar-73f760e5.js";import{_ as ge}from"./pagination-813f544d.js";import{d as K}from"./index-b2d17f43.js";import{X as O,R as xe,$ as ye}from"./index-4485c523.js";import{S as fe}from"./vue-tailwind-datepicker-08bb3bad.js";import{c as h}from"./checkPermission.service-6ab2ad75.js";import{_ as he}from"./basicModal-ebaee5b8.js";import{S as be}from"./transition-14eed2e8.js";import{S as ke,b as _e,M as we,g as Me}from"./menu-212d8500.js";import{A as Ve,F as Ce,B as Se,E as Ye}from"./listbox-b603cc4e.js";import"./dialog-a0a12ec2.js";import"./hidden-0aacd91d.js";import"./use-tracked-pointer-3bd62e78.js";import"./use-tree-walker-dc102a2d.js";import"./use-resolve-button-type-0913c19f.js";import"./use-controllable-f4b23304.js";const $e={class:"p-6 space-y-6"},Te={class:"mt-2"},De={class:"mt-2"},ze={class:"border-t p-5"},Ne={class:"text-right space-x-3"},je={__name:"createTicketModal",emits:["reloadTickets"],setup(X,{expose:z,emit:N}){const M=u(!1),V=u(""),b=u(""),f=N;function g(){M.value=!1}function c(){V.value="",b.value="",M.value=!0}function j(){D.post("/api/tickets",{subject:V.value,text:b.value}).then(v=>{R.success(v.data.message),f("reloadTickets",!0),g()}).catch(v=>{console.log(v)})}return z({openModal:c}),(v,r)=>(o(),Y(a(be),{appear:"",show:M.value,as:"template",onClose:r[4]||(r[4]=x=>g())},{default:d(()=>[n(he,null,{"modal-title":d(()=>r[5]||(r[5]=[le("Založení nového požadavku")])),"modal-close-button":d(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:r[0]||(r[0]=x=>g())},[n(a(O),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":d(()=>[n(a(ne),{onSubmit:j},{default:d(({values:x})=>[e("div",$e,[e("div",null,[r[6]||(r[6]=e("label",{for:"ticket_name",class:"block text-sm leading-6 text-gray-900"},"Název požadavku:",-1)),e("div",Te,[n(a(E),{rules:"required",type:"text",name:"ticket_name",id:"ticket_name",modelValue:V.value,"onUpdate:modelValue":r[1]||(r[1]=y=>V.value=y),class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte název nového požadavku..."},null,8,["modelValue"]),n(a(I),{name:"ticket_name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[r[7]||(r[7]=e("label",{for:"ticket_desc",class:"block text-sm leading-6 text-gray-900"},"Text požadavku:",-1)),e("div",De,[n(a(E),{as:"textarea",rules:"required",rows:"4",name:"ticket_desc",id:"ticket_desc",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 resize-none",placeholder:"Zadejte text Vašeho požadavku...",modelValue:b.value,"onUpdate:modelValue":r[2]||(r[2]=y=>b.value=y)},null,8,["modelValue"]),n(a(I),{name:"ticket_desc",class:"text-rose-400 text-sm block pt-1"})])])]),e("div",ze,[e("div",Ne,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:r[3]||(r[3]=L(y=>g(),["prevent"]))}," Zavřít "),r[8]||(r[8]=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Vytvořit ",-1))])])]),_:1})]),_:1})]),_:1},8,["show"]))}},Be={class:"space-y-6"},Fe={class:"px-0"},Ue={class:"bg-white border border-zinc-200/70 rounded-md p-5"},Pe={class:"sm:flex justify-between items-center gap-4"},Re={class:"flex items-center gap-4"},Ze={class:"w-72"},Ae={class:"w-44 text-left"},Ee={key:0,class:"text-gray-900"},Ie={key:1,class:"text-gray-400"},qe=["onClick"],Ge={class:"rounded-l-full w-60"},Ke={type:"button",class:"rounded-md border border-gray-300 block font-normal px-2.5 py-1.5 w-full text-sm leading-6 items-center justify-center space-x-2 sm:space-x-4 transition ease-out duration-300"},Le={class:"flex items-center w-full"},Oe={class:"w-6 h-6"},Xe={class:"flex-1"},He={key:0,class:"text-gray-900"},Je={key:1,class:"text-gray-400"},Qe={class:"flex items-center gap-4"},We={class:"flow-root bg-white border border-zinc-200/70 rounded-md"},et={class:"sm:-mx-6 lg:-mx-8"},tt={class:"inline-block min-w-full align-middle sm:px-6 lg:px-8"},st={key:0,class:"min-w-full divide-y divide-gray-200"},at={scope:"col",class:"py-4 pl-5 pr-3 text-center text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"},ot=["checked"],lt={key:0,class:"divide-y divide-gray-200"},nt={class:"whitespace-nowrap py-4 pl-5 pr-3 text-center"},rt=["onClick","value","checked"],it={class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600"},dt={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},ct={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},ut={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},mt={key:0,class:"bg-red-200/75 text-red-700 rounded-lg px-3 py-1 font-semibold"},pt={key:1,class:"bg-amber-200/75 text-amber-700 rounded-lg px-3 py-1 font-semibold"},vt={key:2,class:"space-x-4 space-y-4"},gt={class:"bg-green-200/75 text-green-700 rounded-lg px-3 py-1 font-semibold"},xt={class:"text-green-700"},yt={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},ft={key:1},ht={class:"bg-gray-100/70"},bt={colspan:"7",class:"py-4 pl-5 px-3 text-sm text-gray-900 bg-gray-100/70"},kt={class:"flex items-center gap-2"},_t={class:"relative"},wt={key:0,class:"block truncate"},Mt={key:1,class:"block h-6 text-gray-400"},Vt={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},Ct={key:1,class:"rounded-lg bg-main-color-300 px-4 py-2 text-sm text-white shadow-sm cursor-not-allowed"},qt={__name:"TicketsList",setup(X){const z=u(),N=re(),M=ie(),V=u(["tickets-parent","tickets-list"]),b=u({}),f=u({id:"",name:""}),g=u([]),c=u([]),j=[{id:1,name:"Vyřešit",permission:"tickets.close"}],v=u(),r=u(""),x=u(1),y=u({}),B=u({}),k=u(!1),m=u([]),H=u({date:"YYYY-MM-DD",month:"MM"});de(()=>{k.value=!0,Q(),_()}),ce(()=>M.perPage,(l,t)=>{k.value=!0,x.value=1,_()});function J(l){f.value=l}function Q(){D.get("/api/ticket-states").then(l=>{b.value=l.data.data}).catch(l=>{console.log(l)})}function _(){if(h.check("tickets.read")||h.check("tickets.master")){if(m.value[0])var l=[m.value[0],m.value[1]];else var l=["",""];if(h.check("tickets.master"))var t="";else var t=N.user.id;D.get("/api/tickets?page="+x.value+"&perpage="+M.perPage+"10&state_id="+f.value.id+"&user_id="+t+"&search="+r.value+"&date_from="+l[0]+"&date_to="+l[1]).then(w=>{y.value=w.data.data,B.value=w.data.meta,k.value=!1,g.value=[],w.data.data.filter((F,s)=>{g.value.push(F.id)})}).catch(w=>{console.log(w)})}else k.value=!1}function W(){m.value=[]}function ee(l){c.value.includes(l)?c.value=c.value.filter(t=>t!==l):c.value.push(l)}function te(){g.value.length!==c.value.length?(c.value=[],y.value.filter((l,t)=>{c.value.push(l.id)})):c.value=[]}function se(l){x.value=l,c.value=[],_()}function ae(){k.value=!0,x.value=1,r.value="",f.value={id:"",name:""},_()}function Z(){k.value=!0,x.value=1,c.value=[],_()}function oe(){c.value.length&&(h.check("tickets.close")||h.check("tickets.master"))?D.post("/api/tickets/close",{tickets:c.value}).then(l=>{R.success(l.data.message),c.value=[],_()}).catch(l=>{console.log(l)}):R.error("Nebyly vybrány žádné tikety")}return(l,t)=>{const w=q("router-link"),F=q("VueSpinner");return o(),i(T,null,[n(ve,{breadCrumbs:V.value},{topbarButtons:d(()=>[a(h).check("tickets.create")||a(h).check("tickets.master")?(o(),i("button",{key:0,onClick:t[0]||(t[0]=L(s=>l.$refs.createTicketRef.openModal(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},"Nový požadavek ")):C("",!0)]),_:1},8,["breadCrumbs"]),e("div",Be,[e("div",Fe,[e("div",Ue,[e("div",Pe,[e("div",Re,[e("div",Ze,[ue(e("input",{type:"text",name:"search",id:"search","onUpdate:modelValue":t[1]||(t[1]=s=>r.value=s),onKeyup:t[2]||(t[2]=pe(s=>Z(),["enter"])),class:"block w-full rounded-md py-1.5 text-gray-900 border border-gray-300 placeholder:text-gray-400 focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte hledaný výraz..."},null,544),[[me,r.value]])]),e("div",null,[n(a(Me),{as:"div",class:"relative inline-block text-left"},{default:d(()=>[e("div",null,[n(a(ke),{class:"inline-flex w-full justify-center rounded-md bg-white px-4 py-2 text-sm font-medium text-white border border-gray-300"},{default:d(()=>[e("div",Ae,[f.value&&f.value.name?(o(),i("span",Ee,p(f.value.name),1)):(o(),i("span",Ie,"Stav vyřešení..."))]),n(a(K),{class:"ml-2 -mr-1 h-5 w-5 text-violet-200 hover:text-violet-100","aria-hidden":"true"})]),_:1})]),n(G,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:d(()=>[n(a(_e),{class:"absolute right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:d(()=>[(o(!0),i(T,null,U(b.value,s=>(o(),i("div",{key:s.id,class:"px-1 py-1"},[n(a(we),null,{default:d(({active:$})=>[e("button",{onClick:A=>J(s),class:P([$?"bg-main-color-600 text-white":"text-gray-900","group flex w-full rounded-md px-2 py-2 text-sm text-left"])},p(s.name),11,qe)]),_:2},1024)]))),128))]),_:1})]),_:1})]),_:1})]),e("div",null,[n(a(fe),{i18n:"cs","use-range":"",shortcuts:!1,modelValue:m.value,"onUpdate:modelValue":t[3]||(t[3]=s=>m.value=s),formatter:H.value},{default:d(({clear:s})=>[e("div",null,[e("div",Ge,[e("button",Ke,[e("div",Le,[e("div",Oe,[m.value&&m.value[0]&&m.value[1]?(o(),Y(a(O),{key:0,onClick:$=>W(),class:"h-6 w-6 text-gray-900","aria-hidden":"true"},null,8,["onClick"])):(o(),Y(a(xe),{key:1,class:"h-6 w-6 text-gray-900","aria-hidden":"true"}))]),e("div",Xe,[m.value&&m.value[0]&&m.value[1]?(o(),i("span",He,[e("span",null,p(a(S)(m.value[0]).format("DD.MM.YYYY"))+" - "+p(a(S)(m.value[1]).format("DD.MM.YYYY")),1)])):(o(),i("span",Je,[e("span",null,p(a(S)(m.value).format("DD.MM.YYYY")),1)]))])])])])])]),_:1},8,["modelValue","formatter"])])]),e("div",Qe,[e("button",{onClick:t[4]||(t[4]=s=>ae()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},t[11]||(t[11]=[e("span",null,"Resetovat",-1)])),e("button",{onClick:t[5]||(t[5]=s=>Z()),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700"}," Filtrovat ")])])])]),e("div",null,[e("div",We,[e("div",et,[e("div",tt,[k.value==!1?(o(),i("table",st,[e("thead",null,[e("tr",null,[e("th",at,[e("input",{id:"comments","aria-describedby":"comments-description",name:"comments",type:"checkbox",onClick:t[6]||(t[6]=s=>te()),checked:g.value.length&&g.value.length==c.value.length,class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent ring-transparent cursor-pointer"},null,8,ot)]),t[12]||(t[12]=e("th",{scope:"col",class:"py-4 pl-5 pr-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"}," Název požadavku ",-1)),t[13]||(t[13]=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"},"Datum vytvoření ",-1)),t[14]||(t[14]=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Zadavatel ",-1)),t[15]||(t[15]=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"},"Stav ",-1)),t[16]||(t[16]=e("th",{scope:"col",class:"py-4 pl-3 pr-5 text-left text-sm font-semibold text-gray-900 rounded-tr-md bg-gray-100/70"},null,-1))])]),y.value&&y.value.length?(o(),i("tbody",lt,[(o(!0),i(T,null,U(y.value,s=>(o(),i("tr",{key:s.id},[e("td",nt,[e("input",{id:"comments","aria-describedby":"comments-description",name:"comments",type:"checkbox",onClick:()=>{ee(s.id)},value:s.id,class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",checked:c.value.includes(s.id)},null,8,rt)]),e("td",it,p(s.subject),1),e("td",dt,p(a(S)(s.created_at).format("DD.MM.YYYY")),1),e("td",ct,p(s.user.full_name),1),e("td",ut,[s.state.id==1?(o(),i("span",mt,p(s.state.name),1)):C("",!0),s.state.id==2||s.state.id==3?(o(),i("span",pt,p(s.state.name),1)):C("",!0),s.state.id==4?(o(),i("span",vt,[e("span",gt,p(s.state.name),1),e("span",xt,p(a(S)(s.updated_at).format("DD.MM.YYYY"))+" - "+p(s.user.full_name),1)])):C("",!0)]),e("td",yt,[n(w,{to:{name:"ticket-detail",params:{id:s.id}}},{default:d(()=>[n(a(ye),{class:"ml-2 -mr-1 h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 p-2 rounded-lg","aria-hidden":"true"})]),_:2},1032,["to"])])]))),128))])):(o(),i("tbody",ft,t[17]||(t[17]=[e("tr",null,[e("td",{colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},"Nebyly nalezeny žádné požadavky.")],-1)]))),e("tfoot",ht,[e("tr",null,[e("td",bt,[e("div",kt,[t[18]||(t[18]=e("span",null,"Označené:",-1)),n(a(Ye),{as:"div",modelValue:v.value,"onUpdate:modelValue":t[7]||(t[7]=s=>v.value=s)},{default:d(()=>[e("div",_t,[n(a(Ve),{class:"relative cursor-pointer rounded-lg bg-white w-48 py-1.5 pl-3 pr-10 text-left text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-main-color-600 sm:text-sm sm:leading-6"},{default:d(()=>[v.value&&v.value.name?(o(),i("span",wt,p(v.value.name),1)):(o(),i("span",Mt,"vyberte akci...")),e("span",Vt,[n(a(K),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]),_:1}),n(G,{"leave-active-class":"transition ease-in duration-100","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:d(()=>[n(a(Ce),{class:"absolute bottom-11 z-10 max-h-60 w-48 overflow-auto rounded-md bg-white divide-y divide-gray-100 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:d(()=>[(o(),i(T,null,U(j,s=>n(a(Se),{as:"template",key:s.id,value:s},{default:d(({active:$,selectedAction:A})=>[e("li",null,[a(h).check(s.permission)?(o(),i("span",{key:0,class:P([$?"bg-main-color-600 text-white":"text-gray-900","relative cursor-pointer select-none p-2 m-1 rounded-md block"])},[e("span",{class:P([A?"font-semibold":"font-normal","block truncate"])},p(s.name),3)],2)):C("",!0)])]),_:2},1032,["value"])),64))]),_:1})]),_:1})])]),_:1},8,["modelValue"]),v.value?(o(),i("button",{key:0,onClick:t[8]||(t[8]=s=>oe()),class:"rounded-lg bg-main-color-600 px-4 py-2 text-sm text-white shadow-sm hover:bg-main-color-700"}," Potvrdit ")):(o(),i("button",Ct," Potvrdit "))])])])])])):(o(),Y(F,{key:1,class:"mx-auto text-spinner-color",size:"40"}))])])]),B.value!==null?(o(),Y(ge,{key:0,meta:B.value,onSetPage:se,modelValue:x.value,"onUpdate:modelValue":t[9]||(t[9]=s=>x.value=s)},null,8,["meta","modelValue"])):C("",!0)])]),n(je,{ref_key:"createTicketRef",ref:z,onReloadTickets:t[10]||(t[10]=s=>_())},null,512)],64)}}};export{qt as default};
