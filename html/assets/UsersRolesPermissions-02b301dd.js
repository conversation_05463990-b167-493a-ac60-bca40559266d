import{y as q,a as d,B as G,k as h,r as P,o as t,b as o,e as r,w as f,d as e,t as p,v as _,u as a,C as A,F as y,f as C,g as H,q as R,s as j,E as J,U as B,x as K,D as O,G as Q}from"./index-f770c8ab.js";import{_ as T}from"./AppTopbar-bf151018.js";import{L as $,s as W}from"./index-3a3ac444.js";import{c as k}from"./checkPermission.service-5e6704d7.js";import{_ as X}from"./_plugin-vue_export-helper-c27b6911.js";import"./index-0d59a700.js";const b=x=>(O("data-v-8721a516"),x=x(),Q(),x),Y={class:"md:flex justify-between items-center"},ee={class:"pb-6 pl-2 flex items-center gap-6"},se={class:"text-xl"},te={key:0},oe={key:1},ae={key:2},re={key:0,class:"flex gap-2 items-center"},ne={class:"bg-main-color-100 rounded-full p-1"},ie=b(()=>e("span",{class:"text-xs text-main-color-600"},"Lokální účet",-1)),le={class:"mb-6 sm:border sm:border-zinc-200/70 rounded-md sm:flex gap-x-0.5 sm:bg-zinc-200/70 space-y-2 sm:space-y-0"},de=b(()=>e("span",{class:"flex items-center"},"Základní informace ",-1)),ce={class:"flex items-center"},me=b(()=>e("p",{class:"ml-2"},"Role a oprávnění",-1)),ue={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},pe={class:"bg-white border border-zinc-200/70 rounded-md p-5 pb-8"},_e={key:0},ve={class:"flex justify-between items-center"},he={class:"flex items-center"},be=b(()=>e("p",{class:"ml-4 text-lg text-gray-900"},"Zařazení do rolí",-1)),xe={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600"},ge={class:"relative flex gap-x-3"},fe={class:"flex h-6 items-center"},ye={class:"text-sm leading-6"},ke=["for"],we={key:1,class:"h-40 flex items-center"},ze={class:"bg-white border border-zinc-200/70 rounded-md pt-5 pb-8"},Ce={key:0},Ue={class:"flex justify-between items-center px-5 pb-0"},Se={class:"flex items-center"},Ve=b(()=>e("p",{class:"ml-4 text-lg text-gray-900"},"Individuální oprávnění",-1)),Ie={class:"p-5 pt-0"},Pe={class:"text-sm font-semibold leading-6 text-main-color"},Re={class:"relative flex gap-x-3"},je={class:"flex h-6 items-center"},Be={class:"text-sm leading-6"},$e=["for"],Fe=H('<div class="p-5 pb-0 border-t border-zinc-200/70" data-v-8721a516><div class="relative flex gap-x-3" data-v-8721a516><div class="flex h-6 items-center" data-v-8721a516><input id="" name="" type="checkbox" disabled class="relative h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-main-color-600 disabled:bg-main-color-300 disabled:border-main-color-300 disabled:text-main-color-300" data-v-8721a516></div><div class="text-sm leading-6" data-v-8721a516><label for="" class="font-normal text-gray-900" data-v-8721a516>Položky označené tímto znakem nelze měnit, jelikož přebírají nastaven z přidělené role.</label></div></div></div>',1),Le={key:1,class:"h-40 flex items-center"},Ne={__name:"UsersRolesPermissions",setup(x){const v=q(),g=d(!0),l=d({}),U=d({}),S=d({}),m=d([]),u=d([]),V=d([]),F=d(["skolasys-root","users-roles-permissions"]);G(()=>{g.value=!0,w(),N(),L(),g.value=!1});function w(){h.get("/api/users/"+v.params.id+"/roles-permissions").then(s=>{l.value=s.data.data,m.value=s.data.data.roles,u.value=s.data.data.direct_permissions,V.value=s.data.data.role_permissions}).catch(s=>{console.log(s)})}function L(){h.get("/api/permission-categories/permissions").then(s=>{S.value=s.data}).catch(s=>{console.log(s)})}async function N(){await h.get("/api/roles?page=1&perpage=9999").then(s=>{U.value=s.data.data}).catch(s=>{console.log(s)})}function D(){m.value.length||(m.value=[null]),h.post("/api/users/"+v.params.id+"/roles",{roles:m.value}).then(s=>{w(),R.success(s.data.message)}).catch(s=>{console.log(s)})}function E(){u.value.length||(u.value=[null]),h.post("/api/users/"+v.params.id+"/permissions",{permissions:u.value}).then(s=>{w(),R.success(s.data.message)}).catch(s=>{console.log(s)})}function M(s){return!!V.value.includes(s)}return(s,i)=>{const Z=P("ShieldCheckIcon"),I=P("router-link");return t(),o(y,null,[r(T,{breadCrumbs:F.value},{topbarButtons:f(()=>[e("button",{onClick:i[0]||(i[0]=c=>s.$router.go(-1)),type:"button",class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200 flex items-center"},"Zpět ")]),_:1},8,["breadCrumbs"]),e("div",Y,[e("div",ee,[e("h2",se,[l.value.first_name?(t(),o("span",te,p(l.value.first_name+" "),1)):_("",!0),l.value.midle_name?(t(),o("span",oe,p(l.value.midle_name+" "),1)):_("",!0),l.value.last_name?(t(),o("span",ae,p(l.value.last_name),1)):_("",!0)]),l.value.active_directory==0?(t(),o("div",re,[e("span",ne,[r(Z,{class:"h-4 w-4 text-main-color-600","aria-hidden":"true"})]),ie])):_("",!0)]),e("div",le,[r(I,{class:"flex rounded-md sm:rounded-r-none sm:rounded-t-none sm:rounded-b-none sm:rounded-l-md px-8 h-10 text-center text-sm font-medium text-gray-900 bg-white border-b-2 border-zinc-200 sm:border-white hover:bg-gray-50 hover:border-gray-50","exact-active-class":"!border-main-color-600",to:{name:"users-edit",params:{id:a(v).params.id}}},{default:f(()=>[de]),_:1},8,["to"]),r(I,{class:"flex rounded-md sm:rounded-l-none sm:rounded-t-none sm:rounded-b-none sm:rounded-r-md px-8 h-10 text-center text-sm font-medium text-gray-900 bg-white border-b-2 border-zinc-200 sm:border-white hover:bg-gray-50 hover:border-gray-50","exact-active-class":"!border-main-color-600",to:{name:"users-roles-permissions",params:{id:a(v).params.id}}},{default:f(()=>[e("span",ce,[r(a($),{class:"h-4"}),me])]),_:1},8,["to"])])]),e("div",ue,[r(a(K),{onSubmit:i[2]||(i[2]=c=>D())},{default:f(({values:c})=>[e("div",pe,[g.value?(t(),o("div",we,[r(a(B),{class:"mx-auto text-spinner-color",size:"40"})])):(t(),o("div",_e,[e("div",ve,[e("div",he,[r(a(W),{class:"w-7"}),be]),e("div",null,[a(k).check("users.update_roles")&&a(k).check("roles.edit")?(t(),o("button",xe,"Uložit")):_("",!0)])]),(t(!0),o(y,null,C(U.value,n=>(t(),o("div",{class:"mt-4 space-y-5 ml-1.5",key:n.id},[e("div",ge,[e("div",fe,[r(a(j),{rules:"requiredCheckbox",id:"role-"+n.id,name:"roles",type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-main-color-600 cursor-pointer",modelValue:m.value,"onUpdate:modelValue":i[1]||(i[1]=z=>m.value=z),value:n.name},null,8,["id","modelValue","value"])]),e("div",ye,[e("label",{for:"role-"+n.id,class:"font-medium text-gray-900 cursor-pointer"},p(n.name),9,ke)])])]))),128)),r(a(J),{name:"roles",class:"text-rose-400 text-sm block pt-1"})]))])]),_:1}),e("div",null,[e("div",ze,[g.value?(t(),o("div",Le,[r(a(B),{class:"mx-auto text-spinner-color",size:"40"})])):(t(),o("div",Ce,[e("div",Ue,[e("div",Se,[r(a($),{class:"w-7"}),Ve]),e("div",null,[a(k).check("users.update_permissions")&&a(k).check("permissions.edit")?(t(),o("button",{key:0,onClick:i[3]||(i[3]=A(c=>E(),["prevent"])),class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600"},"Uložit")):_("",!0)])]),e("fieldset",Ie,[(t(!0),o(y,null,C(S.value,c=>(t(),o("div",{class:"mb-6 mt-4",key:c.id},[e("legend",Pe,p(c.name),1),(t(!0),o(y,null,C(c.permissions,n=>(t(),o("div",{class:"mt-4 space-y-5 ml-3",key:n.id},[e("div",Re,[e("div",je,[r(a(j),{id:"permission-"+n.id,name:"",type:"checkbox",class:"relative h-4 w-4 rounded cursor-pointer border-gray-300 text-main-color-600 focus:ring-main-color-600 disabled:bg-main-color-300 disabled:border-main-color-300 disabled:text-main-color-300",modelValue:u.value,"onUpdate:modelValue":i[4]||(i[4]=z=>u.value=z),value:n.name,disabled:M(n.name)},null,8,["id","modelValue","value","disabled"])]),e("div",Be,[e("label",{for:"permission-"+n.id,class:"font-medium text-gray-900 cursor-pointer"},p(n.human_name),9,$e)])])]))),128))]))),128))]),Fe]))])])])],64)}}},Ae=X(Ne,[["__scopeId","data-v-8721a516"]]);export{Ae as default};
