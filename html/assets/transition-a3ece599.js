import{N as oe,C as ue,e as h,t as se,u as H,o as K,c as de,l as p,g as fe,H as W,p as ve,T as pe}from"./hidden-ab0c4efa.js";import{r as X}from"./dialog-e364ba6d.js";import{H as Y,a as g,N as Z,I as w,B as S,J as _,K as C,M as me,n as ce,j as D,L as x}from"./index-f770c8ab.js";function he(e){let t={called:!1};return(...r)=>{if(!t.called)return t.called=!0,e(...r)}}function O(e,...t){e&&t.length>0&&e.classList.add(...t)}function L(e,...t){e&&t.length>0&&e.classList.remove(...t)}var j=(e=>(e.Finished="finished",e.Cancelled="cancelled",e))(j||{});function ge(e,t){let r=X();if(!e)return r.dispose;let{transitionDuration:i,transitionDelay:o}=getComputedStyle(e),[n,l]=[i,o].map(a=>{let[s=0]=a.split(",").filter(Boolean).map(d=>d.includes("ms")?parseFloat(d):parseFloat(d)*1e3).sort((d,f)=>f-d);return s});return n!==0?r.setTimeout(()=>t("finished"),n+l):t("finished"),r.add(()=>t("cancelled")),r.dispose}function Q(e,t,r,i,o,n){let l=X(),a=n!==void 0?he(n):()=>{};return L(e,...o),O(e,...t,...r),l.nextFrame(()=>{L(e,...r),O(e,...i),l.add(ge(e,s=>(L(e,...i,...t),O(e,...o),a(s))))}),l.add(()=>L(e,...t,...r,...i,...o)),l.add(()=>a("cancelled")),l.dispose}function c(e=""){return e.split(" ").filter(t=>t.trim().length>1)}let M=Symbol("TransitionContext");var be=(e=>(e.Visible="visible",e.Hidden="hidden",e))(be||{});function ye(){return D(M,null)!==null}function Se(){let e=D(M,null);if(e===null)throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");return e}function Ee(){let e=D(P,null);if(e===null)throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");return e}let P=Symbol("NestingContext");function F(e){return"children"in e?F(e.children):e.value.filter(({state:t})=>t==="visible").length>0}function k(e){let t=g([]),r=g(!1);S(()=>r.value=!0),_(()=>r.value=!1);function i(n,l=h.Hidden){let a=t.value.findIndex(({id:s})=>s===n);a!==-1&&(H(l,{[h.Unmount](){t.value.splice(a,1)},[h.Hidden](){t.value[a].state="hidden"}}),!F(t)&&r.value&&(e==null||e()))}function o(n){let l=t.value.find(({id:a})=>a===n);return l?l.state!=="visible"&&(l.state="visible"):t.value.push({id:n,state:"visible"}),()=>i(n,h.Unmount)}return{children:t,register:o,unregister:i}}let q=oe.RenderStrategy,Te=Y({props:{as:{type:[Object,String],default:"div"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:""},enterFrom:{type:[String],default:""},enterTo:{type:[String],default:""},entered:{type:[String],default:""},leave:{type:[String],default:""},leaveFrom:{type:[String],default:""},leaveTo:{type:[String],default:""}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:r,slots:i,expose:o}){let n=g(0);function l(){n.value|=p.Opening,t("beforeEnter")}function a(){n.value&=~p.Opening,t("afterEnter")}function s(){n.value|=p.Closing,t("beforeLeave")}function d(){n.value&=~p.Closing,t("afterLeave")}if(!ye()&&ue())return()=>Z(we,{...e,onBeforeEnter:l,onAfterEnter:a,onBeforeLeave:s,onAfterLeave:d},i);let f=g(null),u=g("visible"),G=w(()=>e.unmount?h.Unmount:h.Hidden);o({el:f,$el:f});let{show:b,appear:$}=Se(),{register:N,unregister:B}=Ee(),R={value:!0},y=se(),E={value:!1},U=k(()=>{!E.value&&u.value!=="hidden"&&(u.value="hidden",B(y),d())});S(()=>{let v=N(y);_(v)}),C(()=>{if(G.value===h.Hidden&&y){if(b&&u.value!=="visible"){u.value="visible";return}H(u.value,{hidden:()=>B(y),visible:()=>N(y)})}});let I=c(e.enter),J=c(e.enterFrom),ee=c(e.enterTo),V=c(e.entered),te=c(e.leave),ne=c(e.leaveFrom),le=c(e.leaveTo);S(()=>{C(()=>{if(u.value==="visible"){let v=K(f);if(v instanceof Comment&&v.data==="")throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")}})});function re(v){let A=R.value&&!$.value,m=K(f);!m||!(m instanceof HTMLElement)||A||(E.value=!0,b.value&&l(),b.value||s(),v(b.value?Q(m,I,J,ee,V,T=>{E.value=!1,T===j.Finished&&a()}):Q(m,te,ne,le,V,T=>{E.value=!1,T===j.Finished&&(F(U)||(u.value="hidden",B(y),d()))})))}return S(()=>{me([b],(v,A,m)=>{re(m),R.value=!1},{immediate:!0})}),x(P,U),de(w(()=>H(u.value,{visible:p.Open,hidden:p.Closed})|n.value)),()=>{let{appear:v,show:A,enter:m,enterFrom:T,enterTo:Ce,entered:Fe,leave:Be,leaveFrom:Ae,leaveTo:Oe,...z}=e,ae={ref:f},ie={...z,...$&&b&&fe.isServer?{class:ce([r.class,z.class,...I,...J])}:{}};return W({theirProps:ie,ourProps:ae,slot:{},slots:i,attrs:r,features:q,visible:u.value==="visible",name:"TransitionChild"})}}}),Le=Te,we=Y({inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:""},enterFrom:{type:[String],default:""},enterTo:{type:[String],default:""},entered:{type:[String],default:""},leave:{type:[String],default:""},leaveFrom:{type:[String],default:""},leaveTo:{type:[String],default:""}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:r,slots:i}){let o=ve(),n=w(()=>e.show===null&&o!==null?(o.value&p.Open)===p.Open:e.show);C(()=>{if(![!0,!1].includes(n.value))throw new Error('A <Transition /> is used but it is missing a `:show="true | false"` prop.')});let l=g(n.value?"visible":"hidden"),a=k(()=>{l.value="hidden"}),s=g(!0),d={show:n,appear:w(()=>e.appear||!s.value)};return S(()=>{C(()=>{s.value=!1,n.value?l.value="visible":F(a)||(l.value="hidden")})}),x(P,a),x(M,d),()=>{let f=pe(e,["show","appear","unmount","onBeforeEnter","onBeforeLeave","onAfterEnter","onAfterLeave"]),u={unmount:e.unmount};return W({ourProps:{...u,as:"template"},theirProps:{},slot:{},slots:{...i,default:()=>[Z(Le,{onBeforeEnter:()=>t("beforeEnter"),onAfterEnter:()=>t("afterEnter"),onBeforeLeave:()=>t("beforeLeave"),onAfterLeave:()=>t("afterLeave"),...r,...u,...f},i.default)]},attrs:{},features:q,visible:l.value==="visible",name:"Transition"})}}});export{we as S,Te as h};
