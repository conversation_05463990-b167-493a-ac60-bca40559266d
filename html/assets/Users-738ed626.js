import{a as c,L as Le,I as pe,H as Se,B as ge,J as We,u as t,j as ie,M as _e,N as Ze,F as J,O as re,o as s,b as a,c as I,v,e as o,w as r,h as R,d as e,x as ve,t as _,s as Z,E as Q,C as D,k as B,q as F,r as be,f as W,n as N,P as le,T as ye,Q as Ce,z as et,R as tt,S as st}from"./index-f770c8ab.js";import{_ as ot}from"./AppTopbar-bf151018.js";import{q as at,r as Ge,s as fe,e as lt,t as Ve,u as he,L as Ne,E as De,a as Ae,B as nt,v as it,h as Me,w as rt,x as dt,y as ct,z as ut,F as mt,N as Ie,G as Ye,J as Ee,S as pt,m as vt}from"./index-3a3ac444.js";import{X as ee,P as _t,c as qe,B as ht,d as ke,C as He}from"./index-0d59a700.js";import{_ as te}from"./basicModal-bc7db752.js";import{c as q}from"./checkPermission.service-5e6704d7.js";import{S as se}from"./transition-a3ece599.js";import{t as Fe,H as ze,o as gt,f as xt,K as ft,b as bt,T as yt,a as Be}from"./hidden-ab0c4efa.js";import{M as kt}from"./dialog-e364ba6d.js";import{b as $t}from"./use-resolve-button-type-23b48367.js";import{d as wt,p as Ut}from"./use-controllable-eb3312b6.js";import{_ as Ct}from"./pagination-af3b0cce.js";/* empty css             */import{S as Oe}from"./vue-tailwind-datepicker-7114d3b6.js";import{s as Ke}from"./default.css_vue_type_style_index_1_src_true_lang-abba3fec.js";import{E as Pe,A as je,F as Re,B as Te}from"./listbox-331f328c.js";import{S as Vt,b as Mt,M as me,g as Ot}from"./menu-eea02f91.js";import"./use-tracked-pointer-f932b80e.js";import"./use-tree-walker-c3f4727c.js";let Je=Symbol("LabelContext");function Xe(){let j=ie(Je,null);if(j===null){let z=new Error("You used a <Label /> component, but it is not inside a parent.");throw Error.captureStackTrace&&Error.captureStackTrace(z,Xe),z}return j}function St({slot:j={},name:z="Label",props:V={}}={}){let b=c([]);function h(x){return b.value.push(x),()=>{let i=b.value.indexOf(x);i!==-1&&b.value.splice(i,1)}}return Le(Je,{register:h,slot:j,name:z,props:V}),pe(()=>b.value.length>0?b.value.join(" "):void 0)}Se({name:"Label",props:{as:{type:[Object,String],default:"label"},passive:{type:[Boolean],default:!1},id:{type:String,default:()=>`headlessui-label-${Fe()}`}},setup(j,{slots:z,attrs:V}){let b=Xe();return ge(()=>We(b.register(j.id))),()=>{let{name:h="Label",slot:x={},props:i={}}=b,{id:k,passive:g,...n}=j,p={...Object.entries(i).reduce(($,[d,u])=>Object.assign($,{[d]:t(u)}),{}),id:k};return g&&(delete p.onClick,delete p.htmlFor,delete n.onClick),ze({ourProps:p,theirProps:n,slot:x,attrs:V,slots:z,name:h})}}});let Qe=Symbol("GroupContext");Se({name:"SwitchGroup",props:{as:{type:[Object,String],default:"template"}},setup(j,{slots:z,attrs:V}){let b=c(null),h=St({name:"SwitchLabel",props:{htmlFor:pe(()=>{var i;return(i=b.value)==null?void 0:i.id}),onClick(i){b.value&&(i.currentTarget.tagName==="LABEL"&&i.preventDefault(),b.value.click(),b.value.focus({preventScroll:!0}))}}}),x=kt({name:"SwitchDescription"});return Le(Qe,{switchRef:b,labelledby:h,describedby:x}),()=>ze({theirProps:j,ourProps:{},slot:{},slots:z,attrs:V,name:"SwitchGroup"})}});let zt=Se({name:"Switch",emits:{"update:modelValue":j=>!0},props:{as:{type:[Object,String],default:"button"},modelValue:{type:Boolean,default:void 0},defaultChecked:{type:Boolean,optional:!0},name:{type:String,optional:!0},value:{type:String,optional:!0},id:{type:String,default:()=>`headlessui-switch-${Fe()}`}},inheritAttrs:!1,setup(j,{emit:z,attrs:V,slots:b,expose:h}){let x=ie(Qe,null),[i,k]=wt(pe(()=>j.modelValue),w=>z("update:modelValue",w),pe(()=>j.defaultChecked));function g(){k(!i.value)}let n=c(null),p=x===null?n:x.switchRef,$=$t(pe(()=>({as:j.as,type:V.type})),p);h({el:p,$el:p});function d(w){w.preventDefault(),g()}function u(w){w.key===Be.Space?(w.preventDefault(),g()):w.key===Be.Enter&&Ut(w.currentTarget)}function P(w){w.preventDefault()}let M=pe(()=>{var w,y;return(y=(w=gt(p))==null?void 0:w.closest)==null?void 0:y.call(w,"form")});return ge(()=>{_e([M],()=>{if(!M.value||j.defaultChecked===void 0)return;function w(){k(j.defaultChecked)}return M.value.addEventListener("reset",w),()=>{var y;(y=M.value)==null||y.removeEventListener("reset",w)}},{immediate:!0})}),()=>{let{id:w,name:y,value:Y,...L}=j,C={checked:i.value},G={id:w,ref:p,role:"switch",type:$.value,tabIndex:0,"aria-checked":i.value,"aria-labelledby":x==null?void 0:x.labelledby.value,"aria-describedby":x==null?void 0:x.describedby.value,onClick:d,onKeyup:u,onKeypress:P};return Ze(J,[y!=null&&i.value!=null?Ze(xt,ft({features:bt.Hidden,as:"input",type:"checkbox",hidden:!0,readOnly:!0,checked:i.value,name:y,value:Y})):null,ze({ourProps:G,theirProps:{...V,...yt(L,["modelValue","defaultChecked"])},slot:C,attrs:V,slots:b,name:"Switch"})])}}});const Pt={class:"p-6"},jt={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block"},Rt={class:"grid grid-cols-2"},Tt={class:"col-span-2 sm:col-span-1 space-y-6"},Zt=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolená OU:",-1),Dt={class:"uppercase text-2xl mt-1"},At=e("span",{class:"text-lg text-gray-400 font-light"},"Povýšitelné OU:",-1),It={class:"flex items-center"},Yt=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"promoteOu",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Lze povýšit ročník")],-1),Et={class:"col-span-2 sm:col-span-1 space-y-4"},Bt=e("span",{class:"text-lg text-gray-400 font-light"},"Název nové OU:",-1),Lt=e("span",{class:"text-lg text-gray-400 font-light"},[R("Alias třídy (kód) - "),e("span",{class:"font-semibold"},"Bakaláři")],-1),Gt=e("span",{class:"text-lg text-gray-400 font-light"},[R("Alias třídy (kód) - "),e("span",{class:"font-semibold"},"Škola Online")],-1),Nt={class:"border-t p-5"},qt={class:"text-right space-x-3"},Ht=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Vytvořit ",-1),Ft={class:"p-6"},Kt={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block"},Jt={class:"grid grid-cols-2 gap-y-6"},Xt={class:"col-span-2 sm:col-span-1 space-y-6"},Qt=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolená OU:",-1),Wt={class:"uppercase text-2xl mt-1"},es=e("span",{class:"text-lg text-gray-400 font-light"},"Povýšitelné OU:",-1),ts={class:"flex items-center"},ss=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"promoteOu",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Lze povýšit ročník")],-1),os={class:"col-span-2 sm:col-span-1 space-y-4"},as=e("span",{class:"text-lg text-gray-400 font-light"},"Název OU:",-1),ls=e("span",{class:"text-lg text-gray-400 font-light"},[R("Alias třídy (kód) - "),e("span",{class:"font-semibold"},"Bakaláři")],-1),ns=e("span",{class:"text-lg text-gray-400 font-light"},[R("Alias třídy (kód) - "),e("span",{class:"font-semibold"},"Škola Online")],-1),is={class:"border-t p-5"},rs={class:"text-right space-x-3"},ds=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Uložit ",-1),cs={class:"p-6"},us={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block"},ms={class:"uppercase font-semibold px-2 underline"},ps={class:"border-t p-5"},vs={class:"text-right space-x-3"},_s={__name:"EditModeActions",props:{item:Object},setup(j){const z=j,V=re(),b=ie("debugModeGlobalVar"),h=c({}),x=c(!1);function i(){x.value=!1}function k(){h.value={},h.value.promoteOu=0,x.value=!0}function g(){B.post("/api/organization-units",{name:h.value.name,parent_id:z.item.id,promotion:h.value.promoteOu,map_bakalari:h.value.map_bakalari,map_skola_online:h.value.map_skola_online}).then(L=>{V.reloadAdTree(!0),F.success(L.data.message),i()}).catch(L=>{console.log(L)})}const n=c({}),p=c(!1);function $(){n.value.promotion=!n.value.promotion}function d(){p.value=!1}function u(L){n.value=L,p.value=!0}function P(){B.post("/api/organization-units/"+n.value.id+"/update",{name:n.value.name,promotion:n.value.promotion,map_bakalari:n.value.map_bakalari,map_skola_online:n.value.map_skola_online}).then(L=>{V.reloadAdTree(!0),F.success(L.data.message),d()}).catch(L=>{console.log(L)})}const M=c(!1);function w(){M.value=!1}function y(L){n.value=L,M.value=!0}function Y(){B.post("/api/organization-units/"+n.value.id+"/delete").then(L=>{V.reloadAdTree(!0),F.success(L.data.message),w()}).catch(L=>{console.log(L)})}return(L,C)=>(s(),a(J,null,[t(q).check("active_directory_ou.delete")?(s(),I(t(ee),{key:0,class:"h-5 w-5 text-red-500 cursor-pointer","aria-hidden":"true",onClick:C[0]||(C[0]=G=>y(j.item))})):v("",!0),t(q).check("active_directory_ou.edit")?(s(),I(t(at),{key:1,class:"h-3 w-3 text-main-color-600 cursor-pointer","aria-hidden":"true",onClick:C[1]||(C[1]=G=>u(j.item))})):v("",!0),t(q).check("active_directory_ou.create")?(s(),I(t(_t),{key:2,class:"h-5 w-5 text-green-600 cursor-pointer","aria-hidden":"true",onClick:k})):v("",!0),o(t(se),{appear:"",show:x.value,as:"template",onClose:C[8]||(C[8]=G=>i())},{default:r(()=>[o(te,null,{"modal-title":r(()=>[R("Založení nové OU")]),"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:C[2]||(C[2]=G=>i())},[o(t(ee),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[o(t(ve),{onSubmit:g},{default:r(({values:G})=>[e("div",Pt,[t(b)?(s(),a("div",jt,_(h.value),1)):v("",!0),e("div",Rt,[e("div",Tt,[e("div",null,[Zt,e("h2",Dt,_(j.item.name),1)]),e("div",null,[At,e("div",It,[o(t(Z),{id:"promoteOu","aria-describedby":"promoteOu",name:"promoteOu",onClick:C[3]||(C[3]=H=>h.value.promoteOu=!h.value.promoteOu),type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",value:"false"}),Yt])])]),e("div",Et,[e("div",null,[Bt,o(t(Z),{rules:"required",type:"text",name:"newOuName",id:"newOuName",modelValue:h.value.name,"onUpdate:modelValue":C[4]||(C[4]=H=>h.value.name=H),class:"block w-full rounded-md border-0 py-2 mt-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Zadejte název nové OU..."},null,8,["modelValue"]),o(t(Q),{name:"newOuName",class:"text-rose-400 text-sm block pt-1"})]),e("div",null,[Lt,o(t(Z),{type:"text",name:"newOuBakalari",id:"newOuBakalari",modelValue:h.value.map_bakalari,"onUpdate:modelValue":C[5]||(C[5]=H=>h.value.map_bakalari=H),class:"block w-full rounded-md border-0 py-2 mt-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Zadejte alias, který se shoduje s třídou v Bakalářích..."},null,8,["modelValue"])]),e("div",null,[Gt,o(t(Z),{type:"text",name:"newOuSkolaOnline",id:"newOuSkolaOnline",modelValue:h.value.map_skola_online,"onUpdate:modelValue":C[6]||(C[6]=H=>h.value.map_skola_online=H),class:"block w-full rounded-md border-0 py-2 mt-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Zadejte alias, který se shoduje s třídou ve Škole Online..."},null,8,["modelValue"])])])])]),e("div",Nt,[e("div",qt,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:C[7]||(C[7]=D(H=>i(),["prevent"]))}," Zavřít "),Ht])])]),_:1})]),_:1})]),_:1},8,["show"]),o(t(se),{appear:"",show:p.value,as:"template",onClose:C[15]||(C[15]=G=>d())},{default:r(()=>[o(te,null,{"modal-title":r(()=>[R("Úprava OU")]),"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:C[9]||(C[9]=G=>d())},[o(t(ee),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[o(t(ve),{onSubmit:P},{default:r(({values:G})=>[e("div",Ft,[t(b)?(s(),a("div",Kt,_(n.value),1)):v("",!0),e("div",Jt,[e("div",Xt,[e("div",null,[Qt,e("h2",Wt,_(j.item.name),1)]),e("div",null,[es,e("div",ts,[o(t(Z),{id:"promoteOu","aria-describedby":"promoteOu",name:"promoteOu",onClick:C[10]||(C[10]=H=>$()),type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",value:"false"}),ss])])]),e("div",os,[e("div",null,[as,o(t(Z),{rules:"required",type:"text",name:"editOuName",id:"editOuName",modelValue:n.value.name,"onUpdate:modelValue":C[11]||(C[11]=H=>n.value.name=H),class:"block w-full rounded-md border-0 py-2 mt-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Zadejte nový název OU..."},null,8,["modelValue"]),o(t(Q),{name:"editOuName",class:"text-rose-400 text-sm block pt-1"})]),e("div",null,[ls,o(t(Z),{type:"text",name:"selectedOuBakalari",id:"selectedOuBakalari",modelValue:n.value.map_bakalari,"onUpdate:modelValue":C[12]||(C[12]=H=>n.value.map_bakalari=H),class:"block w-full rounded-md border-0 py-2 mt-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Zadejte alias, který se shoduje s třídou v Bakalářích..."},null,8,["modelValue"])]),e("div",null,[ns,o(t(Z),{type:"text",name:"newOuSkolaOnline",id:"newOuSkolaOnline",modelValue:n.value.map_skola_online,"onUpdate:modelValue":C[13]||(C[13]=H=>n.value.map_skola_online=H),class:"block w-full rounded-md border-0 py-2 mt-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Zadejte alias, který se shoduje s třídou ve Škole Online..."},null,8,["modelValue"])])])])]),e("div",is,[e("div",rs,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:C[14]||(C[14]=D(H=>d(),["prevent"]))}," Zavřít "),ds])])]),_:1})]),_:1})]),_:1},8,["show"]),o(t(se),{appear:"",show:M.value,as:"template",onClose:C[19]||(C[19]=G=>w())},{default:r(()=>[o(te,null,{"modal-title":r(()=>[R("Smazání OU")]),"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:C[16]||(C[16]=G=>w())},[o(t(ee),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[e("div",cs,[t(b)?(s(),a("div",us,_(n.value),1)):v("",!0),e("div",null,[e("span",null,[R("Opravdu si přejete zvolenou ou:"),e("span",ms,_(n.value.name),1),R("smazat?")])])]),e("div",ps,[e("div",vs,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:C[17]||(C[17]=D(G=>w(),["prevent"]))}," Zavřít "),e("button",{class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-red-600",onClick:C[18]||(C[18]=D(G=>Y(),["prevent"]))}," Smazat ")])])]),_:1})]),_:1},8,["show"])],64))}};const hs={class:"flex justify-between items-center"},gs=["onClick"],xs={class:"flex items-center gap-1"},fs={key:0,class:"flex items-center gap-0.5"},bs={__name:"RecursiveDropdownMenu",props:{items:{type:Array,required:!0},level:{type:Number,default:1},edit_mode:Boolean},setup(j){const z=re();function V(b){z.setTreePosition(b)}return(b,h)=>{const x=be("RecursiveDropdownMenu",!0);return s(),a("ul",{class:N(["tree_line",`level-${j.level}`])},[(s(!0),a(J,null,W(j.items,i=>(s(),a("li",{key:i.id},[e("div",hs,[e("button",{class:N(["text-gray-900 block pr-4 py-2 text-sm cursor-pointer uppercase hover:text-green-600 duration-150",{"text-green-600":t(z).treePosition.id===i.id}]),onClick:k=>V({id:i.id,name:i.name})},[e("span",xs,[i.promotion==1?(s(),I(t(Ge),{key:0,class:"h-3 w-3 text-main-color-600","aria-hidden":"true"})):v("",!0),e("span",null,_(i.name),1),t(z).treePosition.id&&t(z).treePosition.id===i.id?(s(),I(t(qe),{key:1,class:"h-5 w-5 text-green-600","aria-hidden":"true"})):v("",!0)])],10,gs),j.edit_mode?(s(),a("div",fs,[o(_s,{item:{id:i.id,name:i.name,promotion:i.promotion,map_bakalari:i.map_bakalari,map_skola_online:i.map_skola_online}},null,8,["item"])])):v("",!0)]),i.childrens?(s(),I(x,{key:0,edit_mode:j.edit_mode,items:i.childrens,level:j.level+1},null,8,["edit_mode","items","level"])):v("",!0)]))),128))],2)}}},ys={class:"grid grid-cols-12 gap-x-6 gap-y-8"},ks={class:"relative block text-left col-span-12 md:col-span-8 xl:col-span-12"},$s={class:"w-full"},ws={class:"inline-flex items-center w-full gap-x-3 bg-main-color-600 px-3 py-3.5 text-sm font-semibold text-white rounded-t-md"},Us={class:"w-full origin-top divide-y divide-gray-100 rounded-md bg-white shadow-lg focus:outline-none"},Cs={key:0,class:"px-4 py-3 flex justify-between items-center"},Vs=e("p",{class:"text-xs text-main-color-600"},"Editační režim",-1),Ms={class:"py-1 px-4"},Os={class:"py-4 px-4"},Ss={key:0,class:"relative block text-left col-span-12 md:col-span-4 xl:col-span-12"},zs={class:"w-full"},Ps={class:"inline-flex items-center w-full gap-x-3 bg-main-color-600 px-3 py-3.5 text-sm font-semibold text-white rounded-t-md"},js={class:"w-full origin-top divide-y divide-gray-100 rounded-md bg-white shadow-lg focus:outline-none"},Rs={class:"py-4 px-4"},Ts=e("span",null,"Zobrazit přehled skupin",-1),Zs={__name:"AdStructure",setup(j){const z=re(),V=c(!1),b=c([]),h=c(!1);_e(()=>z.reloadAdTreeValue,(k,g)=>{x(),z.reloadAdTree(!1)}),ge(()=>{x()});function x(){B.get("/api/organization-units/tree").then(k=>{b.value=k.data.data}).catch(k=>{console.log(k)})}function i(){z.setAllUsersTreePosition()}return(k,g)=>{const n=be("router-link");return s(),a("div",ys,[e("div",ks,[e("div",$s,[e("div",ws,[o(t(ht),{class:"h-6 w-6 text-white","aria-hidden":"true"}),R(" Struktura AD ")])]),e("div",Us,[t(q).check("active_directory_ou.create")||t(q).check("active_directory_ou.edit")&&t(q).check("active_directory_ou.delete")?(s(),a("div",Cs,[Vs,o(t(zt),{modelValue:V.value,"onUpdate:modelValue":g[0]||(g[0]=p=>V.value=p),class:N([V.value?"bg-indigo-600":"bg-gray-200","relative inline-flex h-5 w-9 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out"])},{default:r(()=>[e("span",{"aria-hidden":"true",class:N([V.value?"translate-x-4":"translate-x-0","pointer-events-none inline-block h-4 w-4 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"])},null,2)]),_:1},8,["modelValue","class"])])):v("",!0),e("div",Ms,[o(bs,{items:b.value,edit_mode:V.value},null,8,["items","edit_mode"])]),e("div",Os,[e("button",{class:"text-sm hover:text-green-600 duration-150",onClick:g[1]||(g[1]=p=>i())},"Zobrazit vše")])])]),t(q).check("active_directory_group.read")?(s(),a("div",Ss,[e("div",zs,[e("div",Ps,[o(t(fe),{class:"h-6 w-6 text-white","aria-hidden":"true"}),R(" Skupiny AD ")])]),e("div",js,[e("div",Rs,[o(n,{to:{name:"groups"},class:"text-gray-900 flex justify-between items-center text-sm",onMouseenter:g[2]||(g[2]=p=>h.value=!0),onMouseleave:g[3]||(g[3]=p=>h.value=!1)},{default:r(()=>[Ts,h.value?(s(),I(t(qe),{key:0,class:"h-5 w-5 text-green-600","aria-hidden":"true"})):v("",!0)]),_:1})])])])):v("",!0)])}}};const Ds={class:"pb-4 px-6 border-b"},As={class:"mt-4"},Is={class:"space-y-4 sm:flex sm:items-center sm:space-x-10 sm:space-y-0"},Ys=e("span",{class:"text-sm text-green-500 font-semibold"},"Typ zakládaného účtu:",-1),Es=["for"],Bs={class:"p-6"},Ls={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block mb-6"},Gs=e("br",null,null,-1),Ns=e("br",null,null,-1),qs=e("br",null,null,-1),Hs=e("br",null,null,-1),Fs=e("br",null,null,-1),Ks=e("br",null,null,-1),Js=e("br",null,null,-1),Xs=e("br",null,null,-1),Qs=e("br",null,null,-1),Ws=e("br",null,null,-1),eo={class:"grid grid-cols-2 gap-x-14"},to={class:"col-span-2 sm:col-span-1"},so={class:"space-y-4"},oo={class:"flex items-center"},ao=e("p",{class:"ml-4 text-lg text-gray-900"},"Základní informace",-1),lo={class:"grid grid-cols-1 sm:grid-cols-2 gap-6 gap-y-4"},no=e("label",{for:"first-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Jméno:",-1),io={class:"mt-2"},ro=e("label",{for:"last-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Příjmení:",-1),co={class:"mt-2"},uo=e("label",{for:"phone",class:"block text-sm font-normal leading-6 text-gray-900"},"Telefonní číslo:",-1),mo={class:"mt-2"},po=e("label",{for:"new-email",class:"block text-sm font-normal leading-6 text-gray-900"},"Emailová adresa:",-1),vo={class:"mt-2"},_o={class:"grid grid-cols-12 items-end gap-4"},ho={class:"col-span-12 sm:col-span-8"},go=e("label",{for:"new-email-confirmation",class:"block text-sm font-normal leading-6 text-gray-900"},"Opakovat email:",-1),xo={class:"mt-2"},fo={class:"col-span-12 sm:col-span-4 mb-2"},bo={class:"flex h-6 justify-end items-center"},yo=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"verifiedEmail",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Ověřený email")],-1),ko={key:0,class:"space-y-4 border-t pt-6 mt-6"},$o={class:"flex items-center"},wo=e("p",{class:"ml-4 text-lg text-gray-900"},"Host / expirace účtu",-1),Uo={class:"flex h-6 justify-start items-center"},Co=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"visitor",class:"text-gray-900 cursor-pointer text-sm"},[R("Uživatelský účet je "),e("strong",null,"účet hosta")])],-1),Vo={class:"rounded-l-full w-full"},Mo={type:"button",class:"rounded-md border border-gray-300 block font-normal px-2.5 py-1.5 w-full text-sm leading-6 items-center justify-center space-x-2 sm:space-x-4 transition ease-out duration-300"},Oo={class:"flex items-center w-full"},So={class:"flex-1 text-left"},zo={key:0,class:"text-gray-900"},Po={key:1,class:"text-gray-400 text-sm"},jo={class:"w-6 h-6"},Ro={class:"flex gap-2"},To=e("span",{class:"font-light text-sm"},"V případě nenastavení data, bude účet neomezeně platný.",-1),Zo={class:"col-span-2 sm:col-span-1"},Do={class:"space-y-4"},Ao={class:"flex items-center"},Io=e("p",{class:"ml-4 text-lg text-gray-900"},"Nastavení hesla",-1),Yo=e("label",{for:"newUserPassword",class:"block text-sm font-normal leading-6 text-gray-900"},"Heslo:",-1),Eo={class:"mt-2 relative"},Bo={class:"grid grid-cols-12 items-end gap-4 border-b pb-8 mb-6"},Lo={class:"col-span-12 sm:col-span-8"},Go=e("label",{for:"newUserPasswordConfirmation",class:"block text-sm font-normal leading-6 text-gray-900"},"Opakovat heslo:",-1),No={class:"mt-2 relative"},qo={class:"col-span-12 sm:col-span-4 mb-2"},Ho={class:"flex h-6 justify-end items-center"},Fo=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"sendSms",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Zaslat do SMS")],-1),Ko={key:0,class:"col-span-12 sm:col-span-12 mb-2"},Jo={class:"flex h-6 justify-start items-center"},Xo=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"mustChangePassword",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Vyžádat změnu hesla po přihlášení")],-1),Qo={key:0,class:"border-b pb-6"},Wo={class:"flex items-center my-4"},ea=e("p",{class:"ml-4 text-lg text-gray-900"},"Zařazení do OU",-1),ta={class:"relative mt-1"},sa={key:0,class:"block truncate"},oa={key:1,class:"block truncate text-gray-400"},aa={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},la={key:0,class:"absolute inset-y-0 left-0 flex items-center pl-3 text-main-color-600"},na={key:1},ia={class:"flex items-center my-4 text-sm"},ra=e("p",{class:"ml-4 text-lg text-gray-900"},"Zařazení do skupin",-1),da={key:2},ca={class:"flex items-center my-4"},ua=e("p",{class:"ml-4 text-lg text-gray-900"},"Nastavení role",-1),ma={class:"space-y-5"},pa={class:"flex h-6 items-center"},va={class:"ml-3 text-sm leading-6"},_a=["for"],ha={key:3,class:"mt-6"},ga={class:"flex items-center my-4"},xa=e("p",{class:"ml-4 text-lg text-gray-900"},"Uzamčení účtu",-1),fa={class:"mt-4"},ba={class:"space-y-4"},ya=["for"],ka={class:"border-t p-5"},$a={class:"text-right space-x-3"},wa=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Založit ",-1),Ua={__name:"createUserModal",emits:["reloadUsersTable"],setup(j,{expose:z,emit:V}){const b=ie("debugModeGlobalVar");re();const h=c({}),x=c({}),i=c(""),k=c([]),g=[{id:"1",title:"Účet Active Directory"},{id:"0",title:"Lokální účet"}],n=c(null),p=c("email"),$=c(0),d=c(0),u=c(0),P=c(0),M=c(0),w=c([]),y=c({}),Y=c(!1),L=c({}),C=c([]),G=c(2),H=()=>{Y.value=!Y.value};_e(()=>n.value,(E,S)=>{K()});function K(){n.value==0?p.value="required|email":p.value="email"}function U(){B.get("/api/groups?perpage=9999").then(E=>{k.value=E.data.data}).catch(E=>{console.log(E)})}function O(){B.get("/api/organization-units?perpage=9999").then(E=>{h.value=E.data.data}).catch(E=>{console.log(E)})}function m(){B.get("/api/account-control-codes?listing=1").then(E=>{x.value=E.data.data}).catch(E=>{console.log(E)})}function A(){B.get("/api/roles").then(E=>{L.value=E.data.data}).catch(E=>{console.log(E)})}function ae(E){C.value.includes(E)?C.value=C.value.filter(S=>S!==E):C.value.push(E)}const X=c(!1);function ce(){X.value=!1}function $e(){O(),U(),A(),m(),i.value="",w.value=[],y.value={},$.value=0,d.value=0,u.value=0,P.value=0,M.value=0,G.value=2,X.value=!0}function we(){n.value==0?Ue():n.value==1&&xe()}function Ue(){B.post("/api/users",{first_name:y.value.first_name,last_name:y.value.last_name,email:y.value.email,email_confirmation:y.value.email_confirmation,email_verified:$.value,phone:y.value.phone,show_password:1,password:y.value.password,password_confirmation:y.value.password_confirmation,roles:C.value}).then(E=>{F.success(E.data.message),V("reloadUsersTable",!0),ce()}).catch(E=>{console.log(E)})}function xe(){var E=[];w.value.forEach(T=>{E.push(T.id)});let S={first_name:y.value.first_name,last_name:y.value.last_name,email:y.value.email,phone:y.value.phone,organization_unit:i.value.id,password:y.value.password,password_confirmation:y.value.password_confirmation,send_sms:d.value,must_change_password:u.value,show_password:1,groups:E,visitor:P.value,account_control_code_id:G.value};y.value.expire_date&&y.value.expire_date[0]&&y.value.expire_date[0].length&&(S.expire=le(y.value.expire_date[0]).format("YYYY-MM-DD HH:MM")),B.post("/api/users/ad",S).then(T=>{F.success(T.data.message),V("reloadUsersTable",!0),E=[],ce()}).catch(T=>{console.log(T)})}return z({openModal:$e}),(E,S)=>(s(),I(t(se),{appear:"",show:X.value,as:"template",onClose:S[19]||(S[19]=T=>ce())},{default:r(()=>[o(te,{size:"xl"},{"modal-title":r(()=>[R("Vytvoření nového uživatele")]),"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:S[0]||(S[0]=T=>ce())},[o(t(ee),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[o(t(ve),{onSubmit:we},{default:r(({values:T})=>[e("div",Ds,[e("fieldset",As,[e("div",Is,[Ys,(s(),a(J,null,W(g,l=>e("div",{key:l.id,class:"flex items-center"},[o(t(Z),{id:l.id,name:"accountType",type:"radio",rules:"requiredRadio",value:l.id,modelValue:n.value,"onUpdate:modelValue":S[1]||(S[1]=ne=>n.value=ne),class:"h-4 w-4 border-gray-300 text-main-color-600 focus:ring-0 focus:ring-offset-0 cursor-pointer"},null,8,["id","value","modelValue"]),e("label",{for:l.id,class:"ml-3 block text-sm text-gray-900 cursor-pointer"},_(l.title),9,Es)])),64)),o(t(Q),{name:"accountType",class:"text-rose-400 text-sm block pt-1"})])])]),e("div",Bs,[t(b)?(s(),a("div",Ls,[e("span",null,"new user: "+_(y.value),1),Gs,e("span",null,"selected account type: "+_(n.value),1),Ns,e("span",null,"verified email: "+_($.value),1),qs,e("span",null,"send sms: "+_(d.value),1),Hs,e("span",null,"password change: "+_(u.value),1),Fs,e("span",null,"selected ou: "+_(i.value),1),Ks,e("span",null,[R("groups: "),e("pre",null,_(w.value),1)]),Js,e("span",null,"role: "+_(L.value),1),Xs,e("span",null,"selected roles: "+_(C.value),1),Qs,e("span",null,"account control codes: "+_(x.value),1),Ws,e("span",null,"selected account control code: "+_(G.value),1)])):v("",!0),e("div",eo,[e("div",to,[e("div",so,[e("div",oo,[o(t(lt),{class:"w-7"}),ao]),e("div",lo,[e("div",null,[no,e("div",io,[o(t(Z),{modelValue:y.value.first_name,"onUpdate:modelValue":S[2]||(S[2]=l=>y.value.first_name=l),type:"text",name:"first-name",id:"first-name",rules:"required|textOnly|minLength:2|maxLength:50",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte jméno..."},null,8,["modelValue"]),o(t(Q),{name:"first-name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[ro,e("div",co,[o(t(Z),{modelValue:y.value.last_name,"onUpdate:modelValue":S[3]||(S[3]=l=>y.value.last_name=l),type:"text",name:"last-name",id:"last-name",rules:"required|textOnly|minLength:2|maxLength:50",autocomplete:"family-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte Příjmení..."},null,8,["modelValue"]),o(t(Q),{name:"last-name",class:"text-rose-400 text-sm block pt-1"})])])]),e("div",null,[uo,e("div",mo,[o(t(Z),{modelValue:y.value.phone,"onUpdate:modelValue":S[4]||(S[4]=l=>y.value.phone=l),type:"tel",name:"phone",id:"phone",rules:"phone",autocomplete:"phone",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte tel. číslo..."},null,8,["modelValue"]),o(t(Q),{name:"phone",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[po,e("div",vo,[o(t(Z),{modelValue:y.value.email,"onUpdate:modelValue":S[5]||(S[5]=l=>y.value.email=l),id:"new-email",name:"new-email",type:"email",rules:p.value,class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte emailovou adresu..."},null,8,["modelValue","rules"]),o(t(Q),{name:"new-email",class:"text-rose-400 text-sm block pt-1"})])]),e("div",_o,[e("div",ho,[go,e("div",xo,[o(t(Z),{modelValue:y.value.email_confirmation,"onUpdate:modelValue":S[6]||(S[6]=l=>y.value.email_confirmation=l),id:"new-email-confirmation",rules:p.value+"|isEqual:"+y.value.email,name:"new-email-confirmation",type:"email",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Opakujte emailovou adresu..."},null,8,["modelValue","rules"]),o(t(Q),{name:"new-email-confirmation",class:"text-rose-400 text-sm block pt-1"})])]),e("div",fo,[e("div",bo,[o(t(Z),{id:"verifiedEmail","aria-describedby":"verifiedEmail",name:"verifiedEmail",modelValue:$.value,"onUpdate:modelValue":S[7]||(S[7]=l=>$.value=l),value:!$.value,type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"]),yo])])])]),n.value==1?(s(),a("div",ko,[e("div",$o,[o(t(fe),{class:"w-7"}),wo]),e("div",Uo,[o(t(Z),{id:"visitor","aria-describedby":"visitor",name:"visitor",type:"checkbox",modelValue:P.value,"onUpdate:modelValue":S[8]||(S[8]=l=>P.value=l),value:!P.value,class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"]),Co]),e("div",null,[o(t(Oe),{i18n:"cs","as-single":"",shortcuts:!1,modelValue:y.value.expire_date,"onUpdate:modelValue":S[9]||(S[9]=l=>y.value.expire_date=l),placeholder:"Zvolte datum, do kdy je účet aktivní"},{default:r(({clear:l})=>[e("div",null,[e("div",Vo,[e("button",Mo,[e("div",Oo,[e("div",So,[y.value.expire_date&&y.value.expire_date.length?(s(),a("span",zo,[e("span",null,_(t(le)(y.value.expire_date[0]).format("DD.MM.YYYY")),1)])):(s(),a("span",Po,"Zvolte datum, do kdy je účet aktivní"))]),e("div",jo,[y.value.expire_date&&y.value.expire_date.length?(s(),I(t(ee),{key:0,onClick:l,class:"h-6 w-6 text-gray-900","aria-hidden":"true"},null,8,["onClick"])):(s(),I(t(Ve),{key:1,class:"h-6 w-6 text-gray-900","aria-hidden":"true"}))])])])])])]),_:1},8,["modelValue"])]),e("div",Ro,[e("div",null,[o(t(he),{class:"h-5 w-5 text-main-color-600","aria-hidden":"true"})]),To])])):v("",!0)]),e("div",Zo,[e("div",Do,[e("div",Ao,[o(t(Ne),{class:"w-7"}),Io]),e("div",null,[Yo,e("div",Eo,[o(t(Z),{modelValue:y.value.password,"onUpdate:modelValue":S[10]||(S[10]=l=>y.value.password=l),type:Y.value?"text":"password",id:"newUserPassword",name:"newUserPassword",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte heslo..."},null,8,["modelValue","type"]),o(t(Q),{name:"newUserPassword",class:"text-rose-400 text-sm block pt-1"}),e("button",{type:"button",class:"absolute top-2.5 right-0 pr-3 flex items-center text-gray-400",onClick:H},[Y.value?(s(),I(t(De),{key:0,name:"eye-off",class:"h-5 w-5"})):(s(),I(t(Ae),{key:1,name:"eye",class:"h-5 w-5"}))])])]),e("div",Bo,[e("div",Lo,[Go,e("div",No,[o(t(Z),{modelValue:y.value.password_confirmation,"onUpdate:modelValue":S[11]||(S[11]=l=>y.value.password_confirmation=l),id:"newUserPasswordConfirmation",name:"newUserPasswordConfirmation",type:Y.value?"text":"password",rules:"password|isEqual:"+y.value.password,class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Opakujte heslo..."},null,8,["modelValue","type","rules"]),o(t(Q),{name:"newUserPasswordConfirmation",class:"text-rose-400 text-sm block pt-1"}),e("button",{type:"button",class:"absolute top-2.5 right-0 pr-3 flex items-center text-gray-400",onClick:H},[Y.value?(s(),I(t(De),{key:0,name:"eye-off",class:"h-5 w-5"})):(s(),I(t(Ae),{key:1,name:"eye",class:"h-5 w-5"}))])])]),e("div",qo,[e("div",Ho,[o(t(Z),{id:"sendSms","aria-describedby":"sendSms",name:"sendSms",modelValue:d.value,"onUpdate:modelValue":S[12]||(S[12]=l=>d.value=l),value:!d.value,type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"]),Fo])]),n.value==1?(s(),a("div",Ko,[e("div",Jo,[o(t(Z),{id:"mustChangePassword","aria-describedby":"mustChangePassword",name:"mustChangePassword",modelValue:u.value,"onUpdate:modelValue":S[13]||(S[13]=l=>u.value=l),value:!u.value,type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"]),Xo])])):v("",!0)])]),n.value==1?(s(),a("div",Qo,[e("div",Wo,[o(t(nt),{class:"w-7"}),ea]),o(t(Pe),{modelValue:i.value,"onUpdate:modelValue":S[15]||(S[15]=l=>i.value=l)},{default:r(()=>[e("div",ta,[o(t(je),{class:"relative w-full text-left cursor-pointer block rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},{default:r(()=>[i.value&&i.value.name?(s(),a("span",sa,_(i.value.name),1)):(s(),a("span",oa,"Zvolte OU")),e("span",aa,[o(t(ke),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]),_:1}),o(t(Z),{modelValue:i.value,"onUpdate:modelValue":S[14]||(S[14]=l=>i.value=l),name:"selectedOu",rules:"required",class:"hidden"},null,8,["modelValue"]),o(t(Q),{name:"selectedOu",class:"text-rose-400 text-sm block pt-1"}),o(ye,{"leave-active-class":"transition duration-100 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:r(()=>[h.value&&h.value.length?(s(),I(t(Re),{key:0,class:"absolute z-20 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:r(()=>[(s(!0),a(J,null,W(h.value,l=>(s(),I(t(Te),{key:l.name,value:l,as:"template"},{default:r(({active:ne,selected:ue})=>[e("li",{class:N([ne?"bg-main-color-100 text-main-color-900":"text-gray-900","relative cursor-pointer select-none py-2 pl-10 pr-4"])},[e("span",{class:N([ue?"font-medium":"font-normal","block truncate"])},_(l.name),3),ue?(s(),a("span",la,[o(t(He),{class:"h-5 w-5","aria-hidden":"true"})])):v("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})):v("",!0)]),_:1})])]),_:1},8,["modelValue"])])):v("",!0),n.value==1?(s(),a("div",na,[e("div",ia,[o(t(fe),{class:"w-7"}),ra]),o(t(Z),{name:"selectedGroups"},{default:r(({handleChange:l,value:ne})=>[o(t(Ke),{name:"selectedGroups",modelValue:w.value,"onUpdate:modelValue":[S[16]||(S[16]=ue=>w.value=ue),l],mode:"tags",label:"name","value-prop":"id",options:k.value,object:!0,placeholder:"Zvolte si skupiny",class:"!rounded-lg !text-sm"},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1}),o(t(Q),{name:"selectedGroups",class:"text-rose-400 text-sm block pt-1"})])):v("",!0),n.value==0?(s(),a("div",da,[e("div",ca,[o(t(fe),{class:"w-7"}),ua]),e("div",null,[e("fieldset",null,[e("div",ma,[(s(!0),a(J,null,W(L.value,l=>(s(),a("div",{key:l.id,class:"relative flex items-start"},[e("div",pa,[o(t(Z),{rules:"requiredCheckbox",id:l.name,name:"roles",value:l.id,onClick:ne=>ae(l),type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["id","value","onClick"])]),e("div",va,[e("label",{for:l.name,class:"font-medium text-gray-900 cursor-pointer"},_(l.name),9,_a)])]))),128)),o(t(Q),{name:"roles",class:"text-rose-400 text-sm block pt-1"})])])])])):v("",!0),n.value==1?(s(),a("div",ha,[e("div",ga,[o(t(it),{class:"w-7"}),xa]),e("fieldset",fa,[e("div",ba,[(s(!0),a(J,null,W(x.value,l=>(s(),a("div",{key:l.id,class:"flex items-center"},[o(t(Z),{rules:"requiredRadio",id:l.id,modelValue:G.value,"onUpdate:modelValue":S[17]||(S[17]=ne=>G.value=ne),name:"selectedAccountControlCode",type:"radio",checked:G.value==l.id,value:l.id,class:"h-4 w-4 border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["id","modelValue","checked","value"]),e("label",{label:"",for:l.id,class:"ml-3 block text-sm leading-6 text-gray-900 cursor-pointer"},_(l.name),9,ya)]))),128)),o(t(Q),{name:"selectedAccountControlCode",class:"text-rose-400 text-sm block pt-1"})])])])):v("",!0)])])]),e("div",ka,[e("div",$a,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:S[18]||(S[18]=D(l=>ce(),["prevent"]))}," Zavřít "),wa])])]),_:1})]),_:1})]),_:1},8,["show"]))}},Ca=e("div",{class:"p-6"},[e("span",null,"Opravdu chcete synchronizovat Active Directory?")],-1),Va={class:"border-t p-5"},Ma={class:"text-right space-x-3"},Oa={__name:"syncAdModal",emits:["reloadAd"],setup(j,{expose:z,emit:V}){const b=c(!1);function h(){b.value=!1}function x(){b.value=!0}function i(){B.post("/api/users/sync").then(k=>{F.success("Synchronizace byla úspěšná!"),h()}).catch(k=>{console.log(k)})}return z({openModal:x}),(k,g)=>(s(),I(t(se),{appear:"",show:b.value,as:"template",onClose:g[3]||(g[3]=n=>h())},{default:r(()=>[o(te,null,{"modal-title":r(()=>[R("Synchronizovat AD")]),"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:g[0]||(g[0]=n=>h())},[o(t(ee),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[Ca,e("div",Va,[e("div",Ma,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:g[1]||(g[1]=D(n=>h(),["prevent"]))}," Zavřít "),e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",onClick:g[2]||(g[2]=D(n=>i(),["prevent"]))}," Synchronizovat ")])])]),_:1})]),_:1},8,["show"]))}},Sa={class:"p-6"},za={key:0,class:"grid grid-cols-2"},Pa={class:"col-span-1"},ja=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolená OU:",-1),Ra={key:0,class:"uppercase text-2xl"},Ta={class:"col-span-1 space-y-5"},Za=e("span",{class:"text-lg text-gray-400 font-light"},"Odeslání hesla do SMS:",-1),Da={class:"relative flex items-start"},Aa={class:"flex h-6 items-center"},Ia=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"sendPasswordSms",class:"text-gray-900 cursor-pointer"},"ANO")],-1),Ya={class:"flex gap-2"},Ea=e("span",{class:"font-light text-sm"},"Pokud je aktivní možnost odeslat heslo SMS, proběhne kontrola doplnění tel. čísel. V případě nedoplnění tel. čísla budete vyzváni k doplnění a proces budete muset opakovat.",-1),Ba={class:"col-span-12 sm:col-span-12 mb-2"},La={class:"flex h-6 justify-start items-center"},Ga=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"mustChangePassword",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Vyžádat změnu hesla po přihlášení")],-1),Na={key:1,class:"grid grid-cols-2"},qa={class:"col-span-1"},Ha=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolení uživatele:",-1),Fa={key:0,class:"col-span-1 space-y-1 py-4"},Ka={key:0},Ja={key:0},Xa={key:1},Qa={key:2},Wa={key:1},el=e("span",{class:"text-xs text-red-500"}," - Chybí některé osobní údaje",-1),tl={class:"col-span-1 space-y-5"},sl=e("span",{class:"text-lg text-gray-400 font-light"},"Odeslání hesla do SMS:",-1),ol={class:"relative flex items-start"},al={class:"flex h-6 items-center"},ll=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"sendPasswordSms",class:"text-gray-900 cursor-pointer"},"ANO")],-1),nl={class:"flex gap-2"},il=e("span",{class:"font-light text-sm"},"Pokud je aktivní možnost odeslat heslo SMS, proběhne kontrola doplnění tel. čísel. V případě nedoplnění tel. čísla budete vyzváni k doplnění a proces budete muset opakovat.",-1),rl={class:"col-span-12 sm:col-span-12 mb-2"},dl={class:"flex h-6 justify-start items-center"},cl=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"mustChangePassword",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Vyžádat změnu hesla po přihlášení")],-1),ul={class:"border-t p-5"},ml={class:"text-right space-x-3"},pl=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Změnit hesla ",-1),vl={class:"p-6 pb-0"},_l={class:"text-center text-xl mx-1"},hl={key:0,class:"border-b pb-6"},gl={key:1,class:"border-b pb-6"},xl={key:0,class:"grid grid-cols-2 divide-x"},fl={key:0,class:"col-span-1 space-y-1 py-4"},bl={key:0},yl={key:0},kl={key:1},$l={key:2},wl={key:1},Ul=e("span",{class:"text-xs text-red-500"}," - Chybí některé osobní údaje",-1),Cl={key:1,class:"col-span-1 space-y-1 py-4"},Vl={key:0},Ml={key:0},Ol={key:1},Sl={key:2},zl={key:1},Pl=e("span",{class:"text-xs text-red-500"}," - Chybí některé osobní údaje",-1),jl={class:"col-span-1 space-y-5 py-6 px-8"},Rl=e("p",{class:"text-center text-sm"},"U zobrazovaných uživatelů chybí tel. číslo. Doplňte telefonní čísla ke všem uživatelům a opakujte proces změny hesla znovu.",-1),Tl=e("p",{class:"text-center text-sm font-semibold"},"Nyní nebyla žádná změna provedena.",-1),Zl={class:"border-t p-5"},Dl={class:"text-right space-x-3"},Al={class:"p-6 pb-0"},Il={class:"text-center text-xl mx-1"},Yl={key:0,class:"border-b pb-6"},El={key:1,class:"border-b pb-6"},Bl={class:"grid grid-cols-2 divide-x"},Ll={class:"col-span-1 space-y-6 py-8 text-center"},Gl=e("span",{class:"text-sm"},"Stažení hesel v papírové podobě",-1),Nl={class:"col-span-1 space-y-5 py-6 px-8"},ql=e("p",{class:"text-center text-sm"},"U zvolených uživatelů proběhlo úspěšné restartování hesel.",-1),Hl=e("p",{class:"text-center text-sm font-semibold"},"V případě, že byla aktivována možnost odeslat hesla do SMS, uživatelům budou nyní hesla postupně rozeslány.",-1),Fl={class:"border-t p-5"},Kl={class:"text-right space-x-3"},Jl={__name:"resetUsersPasswordsModal",props:{openModal:Boolean},emits:["reloadUsersTable"],setup(j,{expose:z,emit:V}){ie("debugModeGlobalVar");const b=re(),h=c([]),x=c(0),i=c(0),k=c({}),g=c(null),n=c(null),p=c(null),$=c(!1);function d(){$.value=!1}function u(U,O){g.value=U,i.value=0,O&&(h.value=O),$.value=!0}function P(){x.value=!x.value,x.value==!0?x.value=1:x.value=0}function M(U){if(U=="organization_unit")g.value=="organization_unit",B.post("/api/organization-units/generate-password",{organization_units:[b.treePosition.id],send_sms:x.value,must_change_password:i.value},{responseType:"blob",headers:{"content-type":"multipart/form-data"}}).then(m=>{n.value=m.headers["file-name"],p.value=window.URL.createObjectURL(new Blob([m.data],{type:m.headers["content-type"]})),F.success("Hesla byla úspěšně změněna!"),x.value=0,d(),H(),h.value=[],V("reloadUsersTable",!0)}).catch(m=>{w(m.response.data),console.log(m)});else if(U=="users"){g.value=="users";var O=[];h.value.forEach(m=>{O.push(m.id)}),B.post("/api/users/generate-password",{users:O,send_sms:x.value,must_change_password:i.value},{responseType:"blob",headers:{"content-type":"multipart/form-data"}}).then(m=>{O=[],n.value=m.headers["file-name"],p.value=window.URL.createObjectURL(new Blob([m.data],{type:m.headers["content-type"]})),F.success("Hesla byla úspěšně změněna!"),x.value=0,d(),H(),h.value=[],V("reloadUsersTable",!0)}).catch(m=>{w(m.response.data),console.log(m)})}}async function w(U){try{const O=await U,m=await new Response(O).text(),A=JSON.parse(m);k.value=A.data,x.value=0,L(),$.value==!0&&d(),resetSelectedUsersPasswordsModal.value==!0&&closeResetSelectedUsersPasswordsModal()}catch(O){console.error("Error fetching data:",O)}}const y=c(!1);function Y(){y.value=!1,k.value={}}function L(){y.value=!0,h.value=[]}const C=c(!1);function G(){C.value=!1}function H(){C.value=!0}function K(){var U=n.value;U=decodeURIComponent(U),U=U.replaceAll("+"," ");var O=p.value,m=document.createElement("a");m.href=O,m.setAttribute("download",U),document.body.appendChild(m),m.click(),n.value=null,p.value=null,G()}return z({openModal:u}),(U,O)=>(s(),a(J,null,[o(t(se),{appear:"",show:$.value,as:"template",onClose:O[7]||(O[7]=m=>d())},{default:r(()=>[o(te,null,Ce({"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:O[0]||(O[0]=m=>d())},[o(t(ee),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[o(t(ve),{onSubmit:O[6]||(O[6]=m=>M(g.value))},{default:r(({values:m})=>[e("div",Sa,[g.value=="organization_unit"?(s(),a("div",za,[e("div",Pa,[ja,t(b).treePosition.name?(s(),a("h2",Ra,_(t(b).treePosition.name),1)):v("",!0)]),e("div",Ta,[Za,e("div",Da,[e("div",Aa,[o(t(Z),{id:"sendPasswordSms","aria-describedby":"passwords-sms",name:"sendPasswordSms",onClick:O[1]||(O[1]=A=>P()),type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",value:"0"})]),Ia]),e("div",Ya,[e("div",null,[o(t(he),{class:"h-5 w-5 text-main-color-600","aria-hidden":"true"})]),Ea]),e("div",Ba,[e("div",La,[o(t(Z),{id:"mustChangePassword","aria-describedby":"mustChangePassword",name:"mustChangePassword",modelValue:i.value,"onUpdate:modelValue":O[2]||(O[2]=A=>i.value=A),value:!i.value,type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"]),Ga])])])])):(s(),a("div",Na,[e("div",qa,[Ha,h.value?(s(),a("div",Fa,[(s(!0),a(J,null,W(h.value,A=>(s(),a("div",{key:A.id},[A.first_name&&A.last_name?(s(),a("div",Ka,[A.first_name?(s(),a("span",Ja,_(A.first_name+" "),1)):v("",!0),A.middle_name?(s(),a("span",Xa,_(A.middle_name+" "),1)):v("",!0),A.last_name?(s(),a("span",Qa,_(A.last_name),1)):v("",!0)])):(s(),a("div",Wa,[e("span",null,[R(_(A.account_name)+" ",1),el])]))]))),128))])):v("",!0)]),e("div",tl,[sl,e("div",ol,[e("div",al,[o(t(Z),{id:"sendPasswordSms","aria-describedby":"passwords-sms",name:"sendPasswordSms",onClick:O[3]||(O[3]=A=>P()),type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",value:"0"})]),ll]),e("div",nl,[e("div",null,[o(t(he),{class:"h-5 w-5 text-main-color-600","aria-hidden":"true"})]),il]),e("div",rl,[e("div",dl,[o(t(Z),{id:"mustChangePassword","aria-describedby":"mustChangePassword",name:"mustChangePassword",modelValue:i.value,"onUpdate:modelValue":O[4]||(O[4]=A=>i.value=A),value:!i.value,type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"]),cl])])])]))]),e("div",ul,[e("div",ml,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:O[5]||(O[5]=D(A=>d(),["prevent"]))}," Zavřít "),pl])])]),_:1})]),_:2},[g.value=="organization_unit"?{name:"modal-title",fn:r(()=>[R("Hromadná změna hesla")]),key:"0"}:{name:"modal-title",fn:r(()=>[R("Změna hesla")]),key:"1"}]),1024)]),_:1},8,["show"]),o(t(se),{appear:"",show:y.value,as:"template",onClose:O[10]||(O[10]=m=>Y())},{default:r(()=>[o(te,null,Ce({"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:O[8]||(O[8]=m=>Y())},[o(t(ee),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[e("div",vl,[e("div",_l,[g.value=="organization_unit"?(s(),a("h2",hl,"Hromadná změna hesla se nepodařila.")):(s(),a("h2",gl,"Změna hesla se nepodařila."))]),k.value&&k.value[0]?(s(),a("div",xl,[k.value[0].users?(s(),a("div",fl,[(s(!0),a(J,null,W(k.value[0].users,m=>(s(),a("div",{key:m.id,class:"flex items-center gap-x-3"},[o(t(Me),{class:"h-5 w-5 text-amber-500","aria-hidden":"true"}),m.first_name&&m.last_name?(s(),a("div",bl,[m.first_name?(s(),a("span",yl,_(m.first_name+" "),1)):v("",!0),m.middle_name?(s(),a("span",kl,_(m.middle_name+" "),1)):v("",!0),m.last_name?(s(),a("span",$l,_(m.last_name),1)):v("",!0)])):(s(),a("div",wl,[e("span",null,[R(_(m.account_name)+" ",1),Ul])]))]))),128))])):(s(),a("div",Cl,[(s(!0),a(J,null,W(k.value,m=>(s(),a("div",{key:m.id,class:"flex items-center gap-x-3"},[o(t(Me),{class:"h-5 w-5 text-amber-500","aria-hidden":"true"}),m.first_name&&m.last_name?(s(),a("div",Vl,[m.first_name?(s(),a("span",Ml,_(m.first_name+" "),1)):v("",!0),m.middle_name?(s(),a("span",Ol,_(m.middle_name+" "),1)):v("",!0),m.last_name?(s(),a("span",Sl,_(m.last_name),1)):v("",!0)])):(s(),a("div",zl,[e("span",null,[R(_(m.account_name)+" ",1),Pl])]))]))),128))])),e("div",jl,[o(t(rt),{class:"h-12 w-12 text-red-500 mx-auto","aria-hidden":"true"}),Rl,Tl])])):v("",!0)]),e("div",Zl,[e("div",Dl,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:O[9]||(O[9]=D(m=>Y(),["prevent"]))}," Zavřít ")])])]),_:2},[g.value=="organization_unit"?{name:"modal-title",fn:r(()=>[R("Hromadná změna hesla")]),key:"0"}:{name:"modal-title",fn:r(()=>[R("Změna hesla")]),key:"1"}]),1024)]),_:1},8,["show"]),o(t(se),{appear:"",show:C.value,as:"template",onClose:O[14]||(O[14]=m=>G())},{default:r(()=>[o(te,null,Ce({"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:O[11]||(O[11]=m=>G())},[o(t(ee),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[e("div",Al,[e("div",Il,[g.value=="organization_unit"?(s(),a("h2",Yl,"Hromadná změna hesla byla úspěšná")):(s(),a("h2",El,"Změna hesla byla úspěšná"))]),e("div",Bl,[e("div",Ll,[Gl,e("button",{class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700",onClick:O[12]||(O[12]=D(m=>K(),["prevent"]))}," Stáhnout hesla v PDF ")]),e("div",Nl,[o(t(dt),{class:"h-12 w-12 text-green-500 mx-auto","aria-hidden":"true"}),ql,Hl])])]),e("div",Fl,[e("div",Kl,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:O[13]||(O[13]=D(m=>G(),["prevent"]))}," Zavřít ")])])]),_:2},[g.value=="organization_unit"?{name:"modal-title",fn:r(()=>[R("Hromadná změna hesla")]),key:"0"}:{name:"modal-title",fn:r(()=>[R("Změna hesla")]),key:"1"}]),1024)]),_:1},8,["show"])],64))}};const Xl={class:"p-6 grid grid-cols-3"},Ql={class:"col-span-1"},Wl={key:0,class:"text-lg text-gray-400 font-light"},en={key:1,class:"text-lg text-gray-400 font-light"},tn={key:2,class:"col-span-1 space-y-1 py-4"},sn={key:0},on={key:1},an={key:2},ln={key:3,class:"col-span-1 py-4"},nn={class:"uppercase text-2xl"},rn={class:"col-span-2"},dn=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolte typ blokace",-1),cn={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block"},un=e("br",null,null,-1),mn={class:"mt-4"},pn={class:"space-y-4 sm:flex sm:items-center sm:space-x-10 sm:space-y-0 mb-6"},vn=["for"],_n={key:0},hn={class:"rounded-l-full"},gn={class:"rounded-md border border-gray-300 block font-normal px-2.5 py-1.5 w-full text-sm leading-6 items-center justify-center space-x-2 sm:space-x-4 transition ease-out duration-300"},xn={class:"flex items-center w-full"},fn={class:"w-6 h-6"},bn={class:"flex-1"},yn={key:0,class:"text-gray-900"},kn={key:1,class:"text-gray-400"},$n=e("span",null,"Zvolte datum od / do",-1),wn=[$n],Un={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block"},Cn={key:1},Vn={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block"},Mn=e("br",null,null,-1),On=e("span",null,"selected school hours",-1),Sn=e("div",null,[e("span",{class:"text-lg text-gray-400 font-light"},"Vyučující hodiny:")],-1),zn={class:"space-x-2 mb-4"},Pn=["for"],jn={class:"selectedTimetablesDate"},Rn={class:"rounded-l-full"},Tn={class:"rounded-md border border-gray-300 block font-normal px-2.5 py-1.5 w-full text-sm leading-6 items-center justify-center space-x-2 sm:space-x-4 transition ease-out duration-300"},Zn={class:"flex items-center w-full"},Dn={class:"w-6 h-6"},An={class:"flex-1"},In={key:0,class:"text-gray-900"},Yn={key:1,class:"text-gray-400"},En=e("span",null,"Zvolte datum blokace..",-1),Bn=[En],Ln={key:2},Gn=e("div",{class:"text-center"},[e("span",{class:"text-gray-400 font-light"},"Nastavení nemá žádné možnosti.")],-1),Nn=[Gn],qn={class:"border-t p-5"},Hn={class:"text-right space-x-3"},Fn=e("button",{type:"submit",class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600"}," Aktivovat blokaci ",-1),Kn={__name:"blockInternetModal",emits:["reloadUsersTable"],setup(j,{expose:z,emit:V}){const b=re(),h=ie("debugModeGlobalVar"),x=c(!1),i=c([]);ge(()=>{x.value=!0});const k=c(null);function g(){B.get("/api/timetables").then(K=>{k.value=K.data.data}).catch(K=>{console.log(K)})}const n=c("user"),p=[{id:1,title:"Termín od / do"},{id:2,title:"Vyučovací hodiny"},{id:3,title:"Trvale"}],$=c({date:"YYYY-MM-DD",month:"MM"}),d=c(null),u=c([]),P=c([]),M=c(null);function w(K){P.value.includes(K)?P.value=P.value.filter(U=>U!==K):P.value.push(K)}const y=c(!1);function Y(){y.value=!1}function L(K,U){g(),i.value=[],U&&(i.value=U),n.value=K,P.value=[],M.value="",d.value=null,y.value=!0}function C(){n.value=="users"?G():n.value=="organization_unit"&&H(P.value)}function G(){var K=[];i.value.forEach(U=>{K.push(U.id)}),d.value==1?B.post("/api/internet-blocks/datetime",{users:K,from:le(u.value[0]).format("YYYY-MM-DD HH:mm"),to:le(u.value[1]).format("YYYY-MM-DD HH:mm")}).then(U=>{F.success(U.data.message),V("reloadUsersTable",!0),Y(),u.value=["",""],i.value=[]}).catch(U=>{console.log(U)}):d.value==2?B.post("/api/internet-blocks/timetable",{users:K,date:le(M.value).format("YYYY-MM-DD"),timetables:P.value}).then(U=>{F.success(U.data.message),V("reloadUsersTable",!0),Y(),i.value=[]}).catch(U=>{console.log(U)}):d.value==3&&B.post("/api/internet-blocks/permanent",{users:K}).then(U=>{F.success(U.data.message),V("reloadUsersTable",!0),Y(),i.value=[]}).catch(U=>{console.log(U)})}function H(K){d.value==1?B.post("/api/internet-blocks/organization-units/datetime/",{organization_units:[b.treePosition.id],from:le(u.value[0]).format("YYYY-MM-DD HH:mm"),to:le(u.value[1]).format("YYYY-MM-DD HH:mm")}).then(U=>{F.success(U.data.message),V("reloadUsersTable",!0),Y(),i.value=[]}).catch(U=>{console.log(U)}):d.value==2?B.post("api/internet-blocks/organization-units/timetable/",{organization_units:[b.treePosition.id],date:le(M.value).format("YYYY-MM-DD"),timetables:P.value}).then(U=>{F.success(U.data.message),V("reloadUsersTable",!0),Y(),i.value=[]}).catch(U=>{console.log(U)}):d.value==3&&B.post("/api/internet-blocks/organization-units/permanent/",{organization_units:[b.treePosition.id]}).then(U=>{F.success(U.data.message),V("reloadUsersTable",!0),Y(),i.value=[]}).catch(U=>{console.log(U)})}return z({openModal:L}),(K,U)=>(s(),I(t(se),{appear:"",show:y.value,as:"template",onClose:U[5]||(U[5]=O=>Y())},{default:r(()=>[o(te,null,{"modal-title":r(()=>[R("Blokace Internetu")]),"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:U[0]||(U[0]=O=>Y())},[o(t(ee),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[o(t(ve),{onSubmit:C},{default:r(({values:O})=>[e("div",Xl,[e("div",Ql,[n.value=="users"?(s(),a("span",Wl,"Zvolení uživatele:")):n.value=="organization_unit"?(s(),a("span",en,"Zvolená OU:")):v("",!0),i.value&&n.value=="users"?(s(),a("div",tn,[(s(!0),a(J,null,W(i.value,m=>(s(),a("div",{key:m.id},[e("div",null,[m.first_name?(s(),a("span",sn,_(m.first_name+" "),1)):v("",!0),m.middle_name?(s(),a("span",on,_(m.middle_name+" "),1)):v("",!0),m.last_name?(s(),a("span",an,_(m.last_name),1)):v("",!0)])]))),128))])):n.value=="organization_unit"?(s(),a("div",ln,[e("span",nn,_(t(b).treePosition.name),1)])):v("",!0)]),e("div",rn,[dn,e("div",null,[t(h)?(s(),a("div",cn,[R(_(d.value)+" ",1),un])):v("",!0),e("fieldset",mn,[e("div",pn,[(s(),a(J,null,W(p,m=>e("div",{key:m.id,class:"flex items-center"},[o(t(Z),{id:m.id,name:"notification_method",type:"radio",rules:"requiredRadio",checked:m.id===d.value,modelValue:d.value,"onUpdate:modelValue":U[1]||(U[1]=A=>d.value=A),value:m.id,class:"h-4 w-4 border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["id","checked","modelValue","value"]),e("label",{for:m.id,class:"ml-3 block text-sm font-medium leading-6 text-gray-900 cursor-pointer"},_(m.title),9,vn)])),64))]),o(t(Q),{name:"notification_method",class:"text-rose-400 text-sm block pt-1"}),d.value==1?(s(),a("div",_n,[e("div",null,[o(t(Z),{name:"blockedInternetDate",rules:"required"},{default:r(({handleChange:m,value:A})=>[o(t(Oe),{i18n:"cs","use-range":"",shortcuts:!1,modelValue:u.value,"onUpdate:modelValue":[U[2]||(U[2]=ae=>u.value=ae),m],formatter:$.value},{default:r(({clear:ae})=>[e("div",null,[e("div",hn,[e("button",gn,[e("div",xn,[e("div",fn,[u.value&&u.value[0]&&u.value[1]?(s(),I(t(ee),{key:0,onClick:X=>K.unsetBlockedInternetDate(),class:"h-6 w-6 text-gray-900","aria-hidden":"true"},null,8,["onClick"])):(s(),I(t(Ve),{key:1,class:"h-6 w-6 text-gray-900","aria-hidden":"true"}))]),e("div",bn,[u.value&&u.value[0]&&u.value[1]?(s(),a("span",yn,[e("span",null,_(t(le)(u.value[0]).format("DD.MM.YYYY"))+" - "+_(t(le)(u.value[1]).format("DD.MM.YYYY")),1)])):(s(),a("span",kn,wn))])])])])])]),_:2},1032,["modelValue","onUpdate:modelValue","formatter"]),t(h)?(s(),a("div",Un,[e("p",null,"value: "+_(A),1)])):v("",!0)]),_:1}),o(t(Q),{name:"blockedInternetDate",class:"text-rose-400 text-sm block pt-1"})])])):d.value==2?(s(),a("div",Cn,[t(h)?(s(),a("div",Vn,[R(_(k.value)+" ",1),Mn,On,R(_(P.value)+" ",1),e("span",null,"selected school hours date: "+_(t(le)(M.value).format("YYYY-MM-DD")),1)])):v("",!0),Sn,e("div",null,[e("fieldset",null,[e("div",zn,[(s(!0),a(J,null,W(k.value,m=>(s(),a("div",{key:m.id,class:"inline-block border p-2 text-center"},[e("div",null,[e("label",{for:"timetable"+m.id,class:"font-medium text-gray-900 cursor-pointer"},_(m.teaching_hour_number),9,Pn)]),e("div",null,[o(t(Z),{type:"checkbox",onClick:A=>w(m.id),id:"timetable"+m.id,value:m.id,name:"timetables",rules:"requiredCheckbox",class:"h-5 w-5 rounded cursor-pointer border-gray-300 text-main-color-600 focus:ring-transparent"},null,8,["onClick","id","value"])])]))),128)),o(t(Q),{name:"timetables",class:"text-rose-400 text-sm"})])]),e("div",jn,[o(t(Z),{name:"blockedTimetableDate",rules:"required"},{default:r(({handleChange:m,value:A})=>[o(t(Oe),{name:"blockedTimetableDate",i18n:"cs","as-single":"",shortcuts:!1,modelValue:M.value,"onUpdate:modelValue":[U[3]||(U[3]=ae=>M.value=ae),m],formatter:$.value,placeholder:"Zvolte datum blokace.."},{default:r(({clear:ae})=>[e("div",null,[e("div",Rn,[e("button",Tn,[e("div",Zn,[e("div",Dn,[M.value?(s(),I(t(ee),{key:0,onClick:ae,class:"h-6 w-6 text-gray-900","aria-hidden":"true"},null,8,["onClick"])):(s(),I(t(Ve),{key:1,class:"h-6 w-6 text-gray-900","aria-hidden":"true"}))]),e("div",An,[M.value?(s(),a("span",In,[e("span",null,_(t(le)(M.value).format("DD.MM.YYYY")),1)])):(s(),a("span",Yn,Bn))])])])])])]),_:2},1032,["modelValue","onUpdate:modelValue","formatter"])]),_:1}),o(t(Q),{name:"blockedTimetableDate",class:"text-rose-400 text-sm block pt-1"})])])])):d.value==3?(s(),a("div",Ln,Nn)):v("",!0)])])])]),e("div",qn,[e("div",Hn,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:U[4]||(U[4]=D(m=>Y(),["prevent"]))}," Zavřít "),Fn])])]),_:1})]),_:1})]),_:1},8,["show"]))}},Jn={class:"p-6"},Xn={class:"col-span-1"},Qn=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolení uživatele:",-1),Wn={key:0,class:"col-span-1 space-y-1 py-4"},ei={key:0},ti={key:1},si={key:2},oi={class:"border-t p-5"},ai={class:"text-right space-x-3"},li={__name:"deactivateUsersModal",emits:["reloadUsersTable"],setup(j,{expose:z,emit:V}){ie("debugModeGlobalVar");const b=c([]),h=c(!1);function x(){h.value=!1}function i(g){g&&(b.value=g),h.value=!0}function k(){var g=[];b.value.forEach(n=>{g.push(n.id)}),B.post("/api/users/disable",{users:g}).then(n=>{F.success(n.data.message),V("reloadUsersTable",!0),x(),b.value=[]}).catch(n=>{console.log(n)})}return z({openDeactivateUsersModal:i}),(g,n)=>(s(),I(t(se),{appear:"",show:h.value,as:"template",onClose:n[3]||(n[3]=p=>x())},{default:r(()=>[o(te,null,{"modal-title":r(()=>[R("Deaktivace účtu")]),"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:n[0]||(n[0]=p=>x())},[o(t(ee),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[e("div",Jn,[e("div",Xn,[Qn,b.value?(s(),a("div",Wn,[(s(!0),a(J,null,W(b.value,p=>(s(),a("div",{key:p.id},[e("div",null,[p.first_name?(s(),a("span",ei,_(p.first_name+" "),1)):v("",!0),p.middle_name?(s(),a("span",ti,_(p.middle_name+" "),1)):v("",!0),p.last_name?(s(),a("span",si,_(p.last_name),1)):v("",!0)])]))),128))])):v("",!0)])]),e("div",oi,[e("div",ai,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:n[1]||(n[1]=D(p=>x(),["prevent"]))}," Zavřít "),e("button",{class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-red-600",onClick:n[2]||(n[2]=D(p=>k(),["prevent"]))}," Deaktivovat účty ")])])]),_:1})]),_:1},8,["show"]))}},ni={class:"p-6"},ii={class:"col-span-1"},ri=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolení uživatele:",-1),di={key:0,class:"col-span-1 space-y-1 py-4"},ci={key:0},ui={key:1},mi={key:2},pi={class:"border-t p-5"},vi={class:"text-right space-x-3"},_i={__name:"enableUsersModal",emits:["reloadUsersTable"],setup(j,{expose:z,emit:V}){ie("debugModeGlobalVar");const b=c([]),h=c(!1);function x(){h.value=!1}function i(g){g&&(b.value=g),h.value=!0}function k(){var g=[];b.value.forEach(n=>{g.push(n.id)}),B.post("/api/users/enable",{users:g}).then(n=>{F.success(n.data.message),V("reloadUsersTable",!0),x(),b.value=[]}).catch(n=>{console.log(n)})}return z({openEnableUsersModal:i}),(g,n)=>(s(),I(t(se),{appear:"",show:h.value,as:"template",onClose:n[3]||(n[3]=p=>x())},{default:r(()=>[o(te,null,{"modal-title":r(()=>[R("Aktivace účtu")]),"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:n[0]||(n[0]=p=>x())},[o(t(ee),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[e("div",ni,[e("div",ii,[ri,b.value?(s(),a("div",di,[(s(!0),a(J,null,W(b.value,p=>(s(),a("div",{key:p.id},[e("div",null,[p.first_name?(s(),a("span",ci,_(p.first_name+" "),1)):v("",!0),p.middle_name?(s(),a("span",ui,_(p.middle_name+" "),1)):v("",!0),p.last_name?(s(),a("span",mi,_(p.last_name),1)):v("",!0)])]))),128))])):v("",!0)])]),e("div",pi,[e("div",vi,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:n[1]||(n[1]=D(p=>x(),["prevent"]))}," Zavřít "),e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",onClick:n[2]||(n[2]=D(p=>k(),["prevent"]))}," Aktivovat účty ")])])]),_:1})]),_:1},8,["show"]))}},hi={class:"p-6"},gi={class:"grid grid-cols-2"},xi={class:"col-span-1"},fi=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolená OU:",-1),bi={key:0,class:"uppercase text-2xl"},yi={class:"col-span-1"},ki=e("span",{class:"text-lg text-gray-400 font-light"},"Zdroj importu:",-1),$i={class:"mt-2"},wi={class:"space-y-4 sm:flex sm:items-center sm:space-x-10 sm:space-y-0"},Ui={class:"flex items-center"},Ci=e("label",{for:"import_bakalari",class:"ml-3 block leading-6 text-gray-900 cursor-pointer"},"Bakaláři",-1),Vi={class:"flex items-center"},Mi=e("label",{for:"import_skola_online",class:"ml-3 block leading-6 text-gray-900 cursor-pointer"},"Škola online",-1),Oi={class:"flex gap-2 pt-4"},Si=e("span",{class:"text-sm"},"Kliknutím na tlačítko Import založíte nové uživatele do zvolené organizační jednotky.",-1),zi={class:"py-4"},Pi={class:"rounded-md border border-gray-300 flex justify-between items-center font-normal px-2.5 py-1.5 w-full text-sm"},ji={key:0,class:"text-base text-gray-900 font-light"},Ri={key:1,class:"text-base text-gray-400 font-light"},Ti=e("label",{for:"importUsers",class:"rounded-lg bg-main-color-200/75 px-4 py-2 text-xs text-main-color-600 shadow-sm hover:bg-main-color-200 cursor-pointer"},"Vybrat soubor",-1),Zi={class:"relative flex items-start"},Di={class:"flex h-6 items-center"},Ai=e("div",{class:"ml-3 text-sm leading-6"},[e("label",{for:"ignore_ou",class:"text-gray-900 cursor-pointer"},"Ignorovat OU")],-1),Ii={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block"},Yi=e("br",null,null,-1),Ei={class:"border-t p-5"},Bi={class:"text-right space-x-3"},Li=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Importovat ",-1),Gi={__name:"importUsersModal",emits:["reloadUsersTable"],setup(j,{expose:z,emit:V}){const b=ie("debugModeGlobalVar"),h=re();c(!1);const x=c("bakalari"),i=c(!1),k=pe(()=>{var M;return(M=g.value)==null?void 0:M.name}),g=c(null),n=M=>{g.value=M.target.files[0]},p=c(!1);function $(){p.value=!p.value}function d(){i.value=!1}function u(){g.value=null,p.value=!1,i.value=!0}const P=async()=>{const M=new FormData;M.append("type",x.value),M.append("file",g.value),M.append("ignore_organization_unit",p.value),M.append("organization_unit",h.treePosition.id);try{const w=await B.post("/api/users/import",M,{headers:{"Content-Type":"multipart/form-data"}});V("reloadUsersTable",!0),d(),F.success(w.data.message)}catch(w){F.error(w.data.message)}};return z({openModal:u}),(M,w)=>(s(),I(t(se),{appear:"",show:i.value,as:"template",onClose:w[5]||(w[5]=y=>d())},{default:r(()=>[o(te,null,{"modal-title":r(()=>[R("Importovat uživatele")]),"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:w[0]||(w[0]=y=>d())},[o(t(ee),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[o(t(ve),{onSubmit:P},{default:r(({values:y})=>[e("div",hi,[e("div",gi,[e("div",xi,[fi,t(h).treePosition.name?(s(),a("h2",bi,_(t(h).treePosition.name),1)):v("",!0)]),e("div",yi,[ki,e("div",null,[e("fieldset",$i,[e("div",wi,[e("div",Ui,[o(t(Z),{id:"import_bakalari",rules:"requiredRadio",name:"import_users_checkbox",type:"radio",value:"bakalari",modelValue:x.value,"onUpdate:modelValue":w[1]||(w[1]=Y=>x.value=Y),class:"h-4 w-4 border-gray-300 text-indigo-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue"]),Ci]),e("div",Vi,[o(t(Z),{id:"import_skola_online",rules:"requiredRadio",name:"import_users_checkbox",type:"radio",value:"skolaonline",modelValue:x.value,"onUpdate:modelValue":w[2]||(w[2]=Y=>x.value=Y),class:"h-4 w-4 border-gray-300 text-indigo-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue"]),Mi])])]),o(t(Q),{name:"import_users_checkbox",class:"text-rose-400 text-sm block pt-1"})])])]),e("div",Oi,[e("div",null,[o(t(he),{class:"h-5 w-5 text-main-color-600","aria-hidden":"true"})]),Si]),e("div",zi,[e("div",Pi,[t(k)?(s(),a("span",ji,_(t(k)),1)):(s(),a("span",Ri,"Zvolte soubor k importu...")),o(t(Z),{rules:"requiredFile",onChange:n,class:"hidden",type:"file",id:"importUsers",name:"importUsers",accept:".xlsx"}),Ti]),o(t(Q),{name:"importUsers",class:"text-rose-400 text-sm block pt-1"})]),e("div",Zi,[e("div",Di,[o(t(Z),{id:"ignore_ou",name:"ignore_ou",value:"false",onClick:w[3]||(w[3]=Y=>$()),type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"})]),Ai]),t(b)?(s(),a("div",Ii,[e("span",null,"ignore ou: "+_(p.value),1),Yi,e("span",null,"selected import source: "+_(x.value),1)])):v("",!0)]),e("div",Ei,[e("div",Bi,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:w[4]||(w[4]=D(Y=>d(),["prevent"]))}," Zavřít "),Li])])]),_:1})]),_:1})]),_:1},8,["show"]))}};const Ni={class:"p-6 promotion-modal-data"},qi={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block"},Hi=e("h3",{class:"text-center text-gray-500 text-lg font-light pb-6"},"Seznam ročníků dostupných k povýšení",-1),Fi={key:1},Ki={class:"max-h-64 overflow-y-scroll pr-4"},Ji=e("div",{class:"grid grid-cols-2 gap-4 py-3 px-4"},[e("div",{class:"cols-span-1 flex justify-between items-center"},[e("span",{class:"text-sm text-gray-900"},"Původní název")]),e("div",{class:"cols-span-1"},[e("span",{class:"text-sm text-gray-900"},"Nový název")])],-1),Xi={class:"grid grid-cols-2 gap-4 py-3 px-4"},Qi={class:"cols-span-1 flex justify-between items-center"},Wi={class:"text-lg text-gray-900"},er={class:"cols-span-1"},tr={class:"flex gap-2 pt-10"},sr=e("span",{class:"font-light text-sm"},"Ke každé OU zadejte nový název. Pro urychlení se systém pokusil doplnit nový název automaticky.",-1),or={class:"border-t p-5"},ar={class:"text-right space-x-3"},lr=e("button",{type:"submit",class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600"}," Povýšit ročníky ",-1),nr={__name:"promoteOuModal",emits:["reloadUsersTable"],setup(j,{expose:z,emit:V}){const b=ie("debugModeGlobalVar"),h=re(),x=c(!1);function i(){B.get("/api/organization-units/promotions").then(u=>{k.value=u.data.data,x.value=!1}).catch(u=>{console.log(u)})}const k=c({}),g=c(!1);function n(){g.value=!1}function p(){i(),$.value=[],g.value=!0}const $=c([]);function d(){$.value=[],B.post("/api/organization-units/promotions",{organization_units:k.value}).then(u=>{F.success(u.data.message),h.reloadAdTree(!0),V("reloadUsersTable",!0),n()}).catch(u=>{u.response.data.data.failed.forEach(P=>{$.value.push(P)})})}return z({openModal:p}),(u,P)=>(s(),I(t(se),{appear:"",show:g.value,as:"template",onClose:P[2]||(P[2]=M=>n())},{default:r(()=>[o(te,null,{"modal-title":r(()=>[R("Povýšení ročníků")]),"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:P[0]||(P[0]=M=>n())},[o(t(ee),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[o(t(ve),{onSubmit:d},{default:r(({values:M})=>[e("div",Ni,[t(b)?(s(),a("div",qi,[e("span",null,"failed promotions: "+_($.value),1)])):v("",!0),Hi,k.value.length?(s(),a("div",Fi,[e("div",Ki,[Ji,(s(!0),a(J,null,W(k.value,w=>(s(),a("div",{key:w.id,class:"border-t"},[e("div",Xi,[e("div",Qi,[e("span",Wi,_(w.name),1),o(t(ct),{class:"h-7 w-7 p-1 text-gray-900","aria-hidden":"true"})]),e("div",er,[o(t(Z),{type:"text",name:"promotion-id:"+w.id,rules:"required",id:"promotion-id:"+w.id,modelValue:w.new_name,"onUpdate:modelValue":y=>w.new_name=y,class:N(["block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 duration-150",$.value.includes(w.id)?"bg-red-50 ring-red-500":""]),placeholder:"Zadejte název nové OU..."},null,8,["name","id","modelValue","onUpdate:modelValue","class"]),o(t(Q),{name:"promotion-id:"+w.id,class:"text-rose-400 text-sm block pt-1"},null,8,["name"])])])]))),128))])])):v("",!0),e("div",tr,[e("div",null,[o(t(he),{class:"h-5 w-5 text-main-color-600","aria-hidden":"true"})]),sr])]),e("div",or,[e("div",ar,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:P[1]||(P[1]=D(w=>n(),["prevent"]))}," Zavřít "),lr])])]),_:1})]),_:1})]),_:1},8,["show"]))}},ir={class:"grid grid-cols-2"},rr={class:"col-span-1 p-6"},dr=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolení uživatele:",-1),cr={key:0,class:"col-span-1 space-y-1 py-4"},ur={key:0},mr={key:0},pr={key:1},vr={key:2},_r={key:1},hr=e("span",{class:"text-xs text-red-500"}," - Chybí některé osobní údaje",-1),gr={class:"col-span-1 p-6"},xr={class:"border-t p-5"},fr={class:"text-right space-x-3"},br={__name:"changeGroupModal",emits:["reloadUsersTable"],setup(j,{expose:z,emit:V}){const b=c(!1),h=c([]),x=c([]),i=c([]);function k(){b.value=!1}function g($){if(console.log($),i.value=[],$)for(let d=0;d<$.length;d++)$[d].active_directory===1&&i.value.push($[d]);b.value=!0,n()}function n(){B.get("/api/groups?perpage=9999").then($=>{h.value=$.data.data}).catch($=>{console.log($)})}function p(){var $=[],d=[];i.value.forEach(u=>{$.push(u.id)}),x.value.forEach(u=>{d.push(u.id)}),B.post("/api/users/ad-group-update",{users:$,groups:d}).then(u=>{F.success("Změna skupin byla úspěšná!"),x.value=[],V("reloadUsersTable",!0),k()}).catch(u=>{console.log(u)})}return z({openModal:g}),($,d)=>(s(),I(t(se),{appear:"",show:b.value,as:"template",onClose:d[4]||(d[4]=u=>k())},{default:r(()=>[o(te,null,{"modal-title":r(()=>[R("Změna skupiny")]),"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:d[0]||(d[0]=u=>k())},[o(t(ee),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[e("div",ir,[e("div",rr,[dr,i.value?(s(),a("div",cr,[(s(!0),a(J,null,W(i.value,u=>(s(),a("div",{key:u.id},[u.first_name&&u.last_name?(s(),a("div",ur,[u.first_name?(s(),a("span",mr,_(u.first_name+" "),1)):v("",!0),u.middle_name?(s(),a("span",pr,_(u.middle_name+" "),1)):v("",!0),u.last_name?(s(),a("span",vr,_(u.last_name),1)):v("",!0)])):(s(),a("div",_r,[e("span",null,[R(_(u.account_name)+" ",1),hr])]))]))),128))])):v("",!0)]),e("div",gr,[o(t(Z),{name:"selectedGroups",rules:"required"},{default:r(({handleChange:u,value:P})=>[o(t(Ke),{name:"selectedGroups",modelValue:x.value,"onUpdate:modelValue":[d[1]||(d[1]=M=>x.value=M),u],mode:"tags",label:"name","value-prop":"id",options:h.value,object:!0,placeholder:"Zvolte si skupiny",class:"!rounded-lg"},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1})])]),e("div",xr,[e("div",fr,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:d[2]||(d[2]=D(u=>k(),["prevent"]))}," Zavřít "),e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",onClick:d[3]||(d[3]=D(u=>p(),["prevent"]))}," Uložit ")])])]),_:1})]),_:1},8,["show"]))}},yr={class:"grid grid-cols-2"},kr={class:"col-span-1 p-6"},$r=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolení uživatele:",-1),wr={key:0,class:"col-span-1 space-y-1 py-4"},Ur={key:0},Cr={key:0},Vr={key:1},Mr={key:2},Or={key:1},Sr=e("span",{class:"text-xs text-red-500"}," - Chybí některé osobní údaje",-1),zr={class:"col-span-1 p-6"},Pr=e("div",{class:"flex items-center my-4"},[e("p",{class:"ml-4 text-lg text-gray-900"},"Zařazení do OU")],-1),jr={class:"relative mt-1"},Rr={key:0,class:"block truncate"},Tr={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},Zr={key:0,class:"absolute inset-y-0 left-0 flex items-center pl-3 text-main-color-600"},Dr={class:"border-t p-5"},Ar={class:"text-right space-x-3"},Ir={__name:"changeOuModal",emits:["reloadUsersTable"],setup(j,{expose:z,emit:V}){const b=c(!1),h=c({}),x=c(),i=c([]);function k(){b.value=!1}function g($){if(console.log($),i.value=[],$)for(let d=0;d<$.length;d++)$[d].active_directory===1&&i.value.push($[d]);b.value=!0,n()}function n(){B.get("/api/organization-units?perpage=9999").then($=>{h.value=$.data.data,x.value=$.data.data[0]}).catch($=>{console.log($)})}function p(){var $=[];i.value.forEach(d=>{$.push(d.id)}),B.post("/api/users/ad-organization-unit-update",{users:$,organization_unit:x.value.id}).then(d=>{F.success("Změna organizační jednotky byla úspěšná!"),V("reloadUsersTable",!0),k()}).catch(d=>{console.log(d)})}return z({openModal:g}),($,d)=>(s(),I(t(se),{appear:"",show:b.value,as:"template",onClose:d[4]||(d[4]=u=>k())},{default:r(()=>[o(te,null,{"modal-title":r(()=>[R("Změna Organizační jednotky")]),"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:d[0]||(d[0]=u=>k())},[o(t(ee),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[e("div",yr,[e("div",kr,[$r,i.value?(s(),a("div",wr,[(s(!0),a(J,null,W(i.value,u=>(s(),a("div",{key:u.id},[u.first_name&&u.last_name?(s(),a("div",Ur,[u.first_name?(s(),a("span",Cr,_(u.first_name+" "),1)):v("",!0),u.middle_name?(s(),a("span",Vr,_(u.middle_name+" "),1)):v("",!0),u.last_name?(s(),a("span",Mr,_(u.last_name),1)):v("",!0)])):(s(),a("div",Or,[e("span",null,[R(_(u.account_name)+" ",1),Sr])]))]))),128))])):v("",!0)]),e("div",zr,[Pr,o(t(Pe),{modelValue:x.value,"onUpdate:modelValue":d[1]||(d[1]=u=>x.value=u)},{default:r(()=>[e("div",jr,[o(t(je),{class:"relative w-full text-left cursor-pointer block rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},{default:r(()=>[x.value&&x.value.name?(s(),a("span",Rr,_(x.value.name),1)):v("",!0),e("span",Tr,[o(t(ke),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]),_:1}),o(ye,{"leave-active-class":"transition duration-100 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:r(()=>[h.value&&h.value.length?(s(),I(t(Re),{key:0,class:"absolute z-20 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:r(()=>[(s(!0),a(J,null,W(h.value,u=>(s(),I(t(Te),{key:u.name,value:u,as:"template"},{default:r(({active:P,selected:M})=>[e("li",{class:N([P?"bg-main-color-100 text-main-color-900":"text-gray-900","relative cursor-pointer select-none py-2 pl-10 pr-4"])},[e("span",{class:N([M?"font-medium":"font-normal","block truncate"])},_(u.name),3),M?(s(),a("span",Zr,[o(t(He),{class:"h-5 w-5","aria-hidden":"true"})])):v("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})):v("",!0)]),_:1})])]),_:1},8,["modelValue"])])]),e("div",Dr,[e("div",Ar,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:d[2]||(d[2]=D(u=>k(),["prevent"]))}," Zavřít "),e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",onClick:d[3]||(d[3]=D(u=>p(),["prevent"]))}," Uložit ")])])]),_:1})]),_:1},8,["show"]))}},Yr={class:"grid grid-cols-2"},Er={class:"col-span-1 p-6"},Br=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolení uživatele:",-1),Lr={key:0,class:"col-span-1 space-y-1 py-4"},Gr={key:0},Nr={key:0},qr={key:1},Hr={key:2},Fr={key:1},Kr=e("span",{class:"text-xs text-red-500"}," - Chybí některé osobní údaje",-1),Jr=e("div",{class:"col-span-1 p-6"},[e("div",{class:"flex items-center my-4"},[e("p",{class:"ml-4 text-lg text-gray-900"},"Opravdu si přejete uživatele smazat?")])],-1),Xr={class:"border-t p-5"},Qr={class:"text-right space-x-3"},Wr={__name:"deleteUsersModal",emits:["reloadUsersTable"],setup(j,{expose:z,emit:V}){const b=c(!1),h=c([]);function x(){b.value=!1}function i(g){if(console.log(g),h.value=[],g)for(let n=0;n<g.length;n++)h.value.push(g[n]);b.value=!0}function k(){if(h.value.length>1){var g=[];h.value.forEach(n=>{g.push(n.id)}),B.post("/api/users/delete",{users:g}).then(n=>{F.success(n.data.message),V("reloadUsersTable",!0),x()}).catch(n=>{console.log(n)})}else B.post("/api/users/"+h.value[0].id+"/delete").then(n=>{F.success(n.data.message),V("reloadUsersTable",!0),x()}).catch(n=>{console.log(n)})}return z({openModal:i}),(g,n)=>(s(),I(t(se),{appear:"",show:b.value,as:"template",onClose:n[3]||(n[3]=p=>x())},{default:r(()=>[o(te,null,{"modal-title":r(()=>[R("Smazat uživatele")]),"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:n[0]||(n[0]=p=>x())},[o(t(ee),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[e("div",Yr,[e("div",Er,[Br,h.value?(s(),a("div",Lr,[(s(!0),a(J,null,W(h.value,p=>(s(),a("div",{key:p.id},[p.first_name&&p.last_name?(s(),a("div",Gr,[p.first_name?(s(),a("span",Nr,_(p.first_name+" "),1)):v("",!0),p.middle_name?(s(),a("span",qr,_(p.middle_name+" "),1)):v("",!0),p.last_name?(s(),a("span",Hr,_(p.last_name),1)):v("",!0)])):(s(),a("div",Fr,[e("span",null,[R(_(p.account_name)+" ",1),Kr])]))]))),128))])):v("",!0)]),Jr]),e("div",Xr,[e("div",Qr,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:n[1]||(n[1]=D(p=>x(),["prevent"]))}," Zavřít "),e("button",{class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-red-600",onClick:n[2]||(n[2]=D(p=>k(),["prevent"]))}," Smazat ")])])]),_:1})]),_:1},8,["show"]))}};const ed={class:"flex"},td=e("span",null,"Synchronizovat AD",-1),sd={class:"grid grid-cols-12 gap-4"},od={class:"col-span-12 xl:col-span-3"},ad={class:"col-span-12 xl:col-span-9"},ld={class:"px-0"},nd={class:"bg-white border border-zinc-200/70 rounded-md p-5"},id={class:"grid grid-cols-12 gap-y-4"},rd={class:"col-span-12 md:col-span-8 flex items-center gap-6"},dd={class:"w-72"},cd={class:"space-y-5"},ud={class:"relative flex items-start"},md={class:"flex h-6 items-center"},pd=["checked"],vd=e("div",{class:"ml-3 text-sm leading-6"},[e("label",{for:"missing_phone",class:"text-gray-900 cursor-pointer"},"Chybějící telefon")],-1),_d={class:"col-span-12 md:col-span-4 flex md:justify-end flex-wrap gap-x-2 gap-y-2"},hd=e("span",null,"Restartovat",-1),gd=[hd],xd={class:"py-6 flex flex-col 2xl:flex-row 2xl:justify-between 2xl:items-center gap-y-4"},fd={class:"flex items-end gap-4"},bd=e("span",{class:"text-lg text-gray-400"},"OU",-1),yd={key:0,class:"uppercase text-2xl"},kd={class:"flex items-center flex-wrap gap-3"},$d={class:"flex"},wd=e("span",null,"Povýšení ročníků",-1),Ud=["disabled"],Cd={class:"flex"},Vd=e("span",null,"Import účtů",-1),Md=["disabled"],Od={class:"flex"},Sd=e("span",null,"Blokace internetu",-1),zd=["disabled"],Pd={class:"flex"},jd=e("span",null,"Změna hesla",-1),Rd={class:"flow-root bg-white border border-zinc-200/70 rounded-md overflow-scroll md:overflow-x-visible"},Td={class:"inline-block w-full align-middle"},Zd={key:0,class:"min-w-full divide-y divide-gray-200"},Dd={scope:"col",class:"py-4 pl-5 pr-3 text-center text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"},Ad=["checked"],Id={scope:"col",class:"py-4 pl-5 pr-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"},Yd={class:"flex items-center gap-2"},Ed=e("span",{class:"mb-0.5"},"Jméno",-1),Bd={class:"flex items-center"},Ld={scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"},Gd={class:"flex items-center gap-2"},Nd=e("span",{class:"mb-0.5"},"Příjmení",-1),qd={class:"flex items-center"},Hd=e("th",{scope:"col",class:"pl-10 pr-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Login ",-1),Fd=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Telefon ",-1),Kd=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Email ",-1),Jd=e("th",{scope:"col",class:"py-4 pl-10 pr-5 text-left text-sm font-semibold text-gray-900 rounded-tr-md bg-gray-100/70"}," Akce ",-1),Xd={key:0,class:"divide-y divide-gray-200"},Qd=["onClick","value","checked"],Wd={class:"flex gap-2 items-center"},ec={key:0,class:"bg-main-color-100 rounded-full p-1"},tc={key:1,class:"w-5"},sc={key:0},oc={key:1,class:"flex items-center gap-2"},ac=e("span",{class:"text-amber-500"},"Chybí",-1),lc={class:"flex items-center gap-2"},nc={key:0},ic={key:1,class:"w-5"},rc={class:"flex items-center gap-2"},dc={key:0,class:"px-1 py-1"},cc=["onClick"],uc={key:1,class:"px-1 py-1"},mc=["onClick"],pc={key:2,class:"px-1 py-1"},vc=["onClick"],_c={key:3,class:"px-1 py-1"},hc=["onClick"],gc={key:4,class:"px-1 py-1"},xc=["onClick"],fc={key:5,class:"px-1 py-1 text-left"},bc=["onClick"],yc=e("div",{class:"whitespace-pre break-words text-left"},[e("p",{class:"w-full break-words"},[R("Změnit organizační "),e("br"),R("jednotku")])],-1),kc=[yc],$c={key:6,class:"px-1 py-1"},wc=["onClick"],Uc={key:1},Cc=e("tr",null,[e("td",{colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},"Nebyli nalezeni žádní úživatelé")],-1),Vc=[Cc],Mc={class:"bg-gray-100/70"},Oc={colspan:"7",class:"py-4 pl-5 px-3 text-sm text-gray-900 bg-gray-100/70"},Sc={class:"flex items-center gap-2"},zc=e("span",null,"Označené:",-1),Pc={class:"absolute -translate-y-5 pt-0.5"},jc={key:0,class:"block truncate"},Rc={key:1,class:"block h-6 text-gray-400"},Tc={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},Zc={key:1,class:"rounded-lg bg-main-color-300 px-4 py-2 text-sm text-white shadow-sm cursor-not-allowed"},Dc={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block"},Ac={key:0},Ic=e("br",null,null,-1),Yc=e("br",null,null,-1),Ec=e("br",null,null,-1),Bc=e("br",null,null,-1),ru={__name:"Users",setup(j){const z=c(),V=c(),b=ie("debugModeGlobalVar"),h=c(["skolasys-root","users"]),x=[{id:1,name:"Aktivovat účty",permission:"users.edit"},{id:2,name:"Deaktivovat účty",permission:"users.edit"},{id:3,name:"Resetovat hesla",permission:"users.set_password"},{id:4,name:"Blokace internetu",permission:"internet_blocks.create"},{id:5,name:"Změnit skupinu",permission:"users.edit"},{id:6,name:"Změnit organizační jednotku",permission:"users.edit"},{id:7,name:"Smazat uživatele",permission:"users.delete"}],i=c(),k=re(),g=c([]),n=c(null),p=c(!1),$=c([]),d=c([]),u=c(""),P=c(1),M=c(0),w=c(null),y=c(null),Y=c(null),L=c(null),C=c(null),G=c(null),H=c(null),K=c(null),U=c(null),O=c(null),m=c("asc"),A=c("first_name");ge(()=>{p.value=!0,X()});function ae(T,l){m.value=T,A.value=l,X()}_e(()=>k.treePosition,(T,l)=>{p.value=!0,xe(),X()}),_e(()=>k.perPage,(T,l)=>{p.value=!0,P.value=1,X()});function X(){if(q.check("users.read")){let T="";u.value&&u.value.length&&u.value.length>0?T="":T=k.treePosition.id,B.get("/api/users?page="+P.value+"&perpage="+k.perPage+"&search="+u.value+"&organization_unit="+T+"&missing_tel_number="+M.value+"&order="+m.value+"&orderby="+A.value).then(l=>{g.value=l.data.data,n.value=l.data.meta,$.value=[],d.value=[],l.data.data.filter((ne,ue)=>{$.value.push(ne)}),p.value=!1}).catch(l=>{console.log(l),F.error(l.data.message)})}else p.value=!1}function ce(T){d.value.includes(T)?d.value=d.value.filter(l=>l!==T):d.value.push(T)}function $e(){$.value.length!==d.value.length?(d.value=[],g.value.filter((T,l)=>{d.value.push(T)})):d.value=[]}function we(T){P.value=T,d.value=[],X()}function Ue(){M.value=!M.value,M.value==!0?M.value=1:M.value=0}function xe(){p.value=!0,P.value=1,u.value="",M.value=0,X()}function E(){p.value=!0,P.value=1,d.value=[],X()}function S(){i.value&&d.value.length?i.value.id==1?C.value.openEnableUsersModal(d.value):i.value.id==2?L.value.openDeactivateUsersModal(d.value):i.value.id==3?q.check("users.set_password")&&(w.value="users",y.value.openModal("users",d.value)):i.value.id==4?q.check("internet_blocks.create")&&Y.value.openModal("users",d.value):i.value.id==5?q.check("users.edit")&&K.value.openModal(d.value):i.value.id==6?q.check("users.edit")&&U.value.openModal(d.value):i.value.id==7&&q.check("users.delete")&&O.value.openModal(d.value):F.error("Nebyli vybráni žádní uživatele!")}return(T,l)=>{const ne=be("router-link"),ue=be("VueSpinner");return s(),a(J,null,[o(ot,{breadCrumbs:h.value},{topbarButtons:r(()=>[e("div",null,[t(q).check("active_directory.sync")?(s(),a("button",{key:0,onClick:l[0]||(l[0]=f=>T.$refs.syncAdRef.openModal()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},[e("div",ed,[o(t(ut),{class:"mr-2 h-5 w-5 text-main-color-600","aria-hidden":"true"}),td])])):v("",!0)]),t(q).check("users.create")?(s(),a("button",{key:0,onClick:l[1]||(l[1]=f=>T.$refs.createUserRef.openModal()),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},"Přidat uživatele ")):v("",!0)]),_:1},8,["breadCrumbs"]),e("div",sd,[e("div",od,[o(Zs)]),e("div",ad,[e("div",ld,[e("div",nd,[e("div",id,[e("div",rd,[e("div",dd,[et(e("input",{type:"text",name:"search",id:"search","onUpdate:modelValue":l[2]||(l[2]=f=>u.value=f),onKeyup:l[3]||(l[3]=st(f=>E(),["enter"])),class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Zadejte hledaný výraz..."},null,544),[[tt,u.value]])]),e("fieldset",null,[e("div",cd,[e("div",ud,[e("div",md,[e("input",{id:"missing_phone",name:"missing_phone",onClick:l[4]||(l[4]=f=>Ue()),type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",checked:M.value},null,8,pd)]),vd])])])]),e("div",_d,[e("div",null,[e("button",{onClick:l[5]||(l[5]=f=>xe()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},gd)]),e("button",{onClick:l[6]||(l[6]=f=>E()),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700"}," Filtrovat ")])])])]),e("div",xd,[e("div",fd,[bd,t(k).treePosition.name?(s(),a("h2",yd,_(t(k).treePosition.name),1)):v("",!0)]),e("div",kd,[t(q).check("active_directory_ou.promotion")?(s(),a("button",{key:0,onClick:l[7]||(l[7]=D(f=>T.$refs.promoteOuRef.openModal(),["prevent"])),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 font-medium shadow-sm hover:bg-main-color-200 disabled:opacity-50 disabled:hover:bg-main-color-200/75 disabled:cursor-not-allowed"},[e("div",$d,[o(t(Ge),{class:"mr-2 h-5 w-5 text-main-color-600","aria-hidden":"true"}),wd])])):v("",!0),t(q).check("users.create")?(s(),a("button",{key:1,onClick:l[8]||(l[8]=D(f=>T.$refs.importUsersRef.openModal(),["prevent"])),disabled:!t(k).treePosition.id,class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 font-medium shadow-sm hover:bg-main-color-200 disabled:opacity-50 disabled:hover:bg-main-color-200/75 disabled:cursor-not-allowed"},[e("div",Cd,[o(t(mt),{class:"mr-2 h-5 w-5 text-main-color-600","aria-hidden":"true"}),Vd])],8,Ud)):v("",!0),t(q).check("internet_blocks.create")?(s(),a("button",{key:2,onClick:l[9]||(l[9]=D(f=>T.$refs.blockInternetRef.openModal("organization_unit",null),["prevent"])),disabled:!t(k).treePosition.id,class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 font-medium shadow-sm hover:bg-main-color-200 disabled:opacity-50 disabled:hover:bg-main-color-200/75 disabled:cursor-not-allowed"},[e("div",Od,[o(t(Ie),{class:"mr-2 h-5 w-5 text-main-color-600","aria-hidden":"true"}),Sd])],8,Md)):v("",!0),t(q).check("users.set_password")?(s(),a("button",{key:3,onClick:l[10]||(l[10]=D(f=>T.$refs.resetPasswordRef.openModal("organization_unit",null),["prevent"])),disabled:!t(k).treePosition.id,class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 font-medium shadow-sm hover:bg-main-color-200 disabled:opacity-50 disabled:hover:bg-main-color-200/75 disabled:cursor-not-allowed"},[e("div",Pd,[o(t(Ne),{class:"mr-2 h-5 w-5 text-main-color-600","aria-hidden":"true"}),jd])],8,zd)):v("",!0)])]),e("div",null,[e("div",Rd,[e("div",null,[e("div",Td,[p.value==!1?(s(),a("table",Zd,[e("thead",null,[e("tr",null,[e("th",Dd,[e("input",{id:"users_select",name:"users_select",type:"checkbox",onClick:l[11]||(l[11]=f=>$e()),checked:$.value.length&&$.value.length==d.value.length,class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent ring-transparent cursor-pointer"},null,8,Ad)]),e("th",Id,[e("div",Yd,[Ed,e("div",Bd,[e("button",{type:"button",onClick:l[12]||(l[12]=f=>ae("asc","first_name"))},[o(t(Ye),{class:N([m.value==="asc"&&A.value==="first_name"?"text-black":"text-gray-600","w-4 h-4"])},null,8,["class"])]),e("button",{type:"button",onClick:l[13]||(l[13]=f=>ae("desc","first_name"))},[o(t(Ee),{class:N([m.value==="desc"&&A.value==="first_name"?"text-black":"text-gray-600","w-4 h-4"])},null,8,["class"])])])])]),e("th",Ld,[e("div",Gd,[Nd,e("div",qd,[e("button",{type:"button",onClick:l[14]||(l[14]=f=>ae("asc","last_name"))},[o(t(Ye),{class:N([m.value==="asc"&&A.value==="last_name"?"text-black":"text-gray-600","w-4 h-4"])},null,8,["class"])]),e("button",{type:"button",onClick:l[15]||(l[15]=f=>ae("desc","last_name"))},[o(t(Ee),{class:N([m.value==="desc"&&A.value==="last_name"?"text-black":"text-gray-600","w-4 h-4"])},null,8,["class"])])])])]),Hd,Fd,Kd,Jd])]),g.value&&g.value.length?(s(),a("tbody",Xd,[(s(!0),a(J,null,W(g.value,f=>(s(),a("tr",{key:f.id},[e("td",{class:N(["whitespace-nowrap py-4 pl-5 pr-3 text-center",{"bg-red-100/70":f.account_control_code&&f.account_control_code.id==3}])},[e("input",{id:"user_select",name:"user_select",type:"checkbox",onClick:()=>{ce(f)},value:f,class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",checked:d.value.includes(f)},null,8,Qd)],2),e("td",{class:N(["whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600",{"bg-red-100/70":f.account_control_code&&f.account_control_code.id==3}])},_(f.first_name),3),e("td",{class:N(["whitespace-nowrap px-3 py-4 text-sm text-gray-600",{"bg-red-100/70":f.account_control_code&&f.account_control_code.id==3}])},_(f.last_name),3),e("td",{class:N(["whitespace-nowrap px-3 py-4 text-sm text-gray-600",{"bg-red-100/70":f.account_control_code&&f.account_control_code.id==3}])},[e("div",Wd,[f.active_directory==0?(s(),a("span",ec,[o(t(pt),{class:"h-3 w-3 text-main-color-600","aria-hidden":"true"})])):(s(),a("span",tc)),e("span",null,_(f.account_name),1)])],2),e("td",{class:N(["whitespace-nowrap px-3 py-4 text-sm text-gray-600",{"bg-red-100/70":f.account_control_code&&f.account_control_code.id==3}])},[f.phone?(s(),a("span",sc,_(f.phone),1)):(s(),a("span",oc,[o(t(Me),{class:"h-5 w-5 text-amber-500","aria-hidden":"true"}),ac]))],2),e("td",{class:N(["whitespace-nowrap px-3 py-4 text-sm text-gray-600",{"bg-red-100/70":f.account_control_code&&f.account_control_code.id==3}])},_(f.email),3),e("td",{class:N(["whitespace-nowrap px-3 py-4 text-sm text-gray-600",{"bg-red-100/70":f.account_control_code&&f.account_control_code.id==3}])},[e("div",lc,[f.block_internet==1?(s(),a("span",nc,[o(t(Ie),{class:"h-5 w-5 text-red-600","aria-hidden":"true"})])):(s(),a("span",ic)),e("div",rc,[t(q).check("users.edit")?(s(),I(ne,{key:0,to:{name:"users-edit",params:{id:f.id}},class:"rounded-md bg-main-color-200/75 w-8 h-7 flex justify-center items-center text-main-color-600 shadow-sm hover:bg-main-color-200"},{default:r(()=>[o(t(vt),{class:"h-4 w-4 text-main-color-600","aria-hidden":"true"})]),_:2},1032,["to"])):v("",!0),o(t(Ot),{as:"div",class:"inline-block text-left"},{default:r(()=>[e("div",null,[o(t(Vt),{class:"rounded-md bg-main-color-200/75 w-8 h-7 flex justify-center items-center text-main-color-600 shadow-sm hover:bg-main-color-200"},{default:r(()=>[o(t(ke),{class:"h-6 w-6 text-main-color-600","aria-hidden":"true"})]),_:1})]),o(ye,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:r(()=>[o(t(Mt),{class:"absolute z-10 right-5 mt-2 w-40 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:r(()=>[f.active_directory&&t(q).check("users.edit")?(s(),a("div",dc,[o(t(me),null,{default:r(({active:oe})=>[e("button",{onClick:D(de=>T.$refs.deactivateUsersRef.openDeactivateUsersModal([f]),["prevent"]),class:N([oe?"bg-main-color-600 text-white":"text-gray-900","group flex w-full items-center rounded-md px-2 py-2 text-sm"])}," Deaktivovat účet ",10,cc)]),_:2},1024)])):v("",!0),f.active_directory&&t(q).check("users.edit")?(s(),a("div",uc,[o(t(me),null,{default:r(({active:oe})=>[e("button",{onClick:D(de=>T.$refs.enableUsersRef.openEnableUsersModal([f]),["prevent"]),class:N([oe?"bg-main-color-600 text-white":"text-gray-900","group flex w-full items-center rounded-md px-2 py-2 text-sm"])}," Aktivovat účet ",10,mc)]),_:2},1024)])):v("",!0),t(q).check("users.set_password")?(s(),a("div",pc,[o(t(me),null,{default:r(({active:oe})=>[e("button",{onClick:D(de=>T.$refs.resetPasswordRef.openModal("users",[f]),["prevent"]),class:N([oe?"bg-main-color-600 text-white":"text-gray-900","group flex w-full items-center rounded-md px-2 py-2 text-sm"])}," Resetovat heslo ",10,vc)]),_:2},1024)])):v("",!0),t(q).check("internet_blocks.create")?(s(),a("div",_c,[o(t(me),null,{default:r(({active:oe})=>[e("button",{onClick:D(de=>{T.$refs.blockInternetRef.openModal("users",[f]),d.value=[f],w.value="users"},["prevent"]),class:N([oe?"bg-main-color-600 text-white":"text-gray-900","group flex w-full items-center rounded-md px-2 py-2 text-sm"])}," Blokace internetu ",10,hc)]),_:2},1024)])):v("",!0),t(q).check("users.edit")&&f.active_directory==1?(s(),a("div",gc,[o(t(me),null,{default:r(({active:oe})=>[e("button",{onClick:D(de=>T.$refs.changeGroupRef.openModal([f]),["prevent"]),class:N([oe?"bg-main-color-600 text-white":"text-gray-900","group flex w-full items-center rounded-md px-2 py-2 text-sm"])}," Změnit skupinu ",10,xc)]),_:2},1024)])):v("",!0),t(q).check("users.edit")&&f.active_directory==1?(s(),a("div",fc,[o(t(me),null,{default:r(({active:oe})=>[e("button",{onClick:D(de=>T.$refs.changeOuRef.openModal([f]),["prevent"]),class:N([oe?"bg-main-color-600 text-white":"text-gray-900","group rounded-md px-2 pr-4 py-2 text-sm w-38"])},kc,10,bc)]),_:2},1024)])):v("",!0),t(q).check("users.delete")?(s(),a("div",$c,[o(t(me),null,{default:r(({active:oe})=>[e("button",{onClick:D(de=>T.$refs.deleteUsersRef.openModal([f]),["prevent"]),class:N([oe?"bg-main-color-600 text-white":"text-gray-900","group flex w-full items-center rounded-md px-2 py-2 text-sm"])}," Smazat uživatele ",10,wc)]),_:2},1024)])):v("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1024)])])],2)]))),128))])):(s(),a("tbody",Uc,Vc)),e("tfoot",Mc,[e("tr",null,[e("td",Oc,[e("div",Sc,[zc,o(t(Pe),{as:"div",modelValue:i.value,"onUpdate:modelValue":l[16]||(l[16]=f=>i.value=f),class:"w-48"},{default:r(()=>[e("div",Pc,[o(t(je),{class:"relative cursor-pointer rounded-lg bg-white w-48 py-1.5 pl-3 pr-10 text-left text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6"},{default:r(()=>[i.value&&i.value.name?(s(),a("span",jc,_(i.value.name),1)):(s(),a("span",Rc," vyberte akci... ")),e("span",Tc,[o(t(ke),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]),_:1}),o(ye,{"leave-active-class":"transition ease-in duration-100","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:r(()=>[o(t(Re),{class:"absolute bottom-11 z-10 max-h-60 w-48 overflow-auto rounded-md bg-white divide-y divide-gray-100 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:r(()=>[(s(),a(J,null,W(x,f=>o(t(Te),{as:"template",key:f.id,value:f},{default:r(({active:oe,selectedAction:de})=>[e("li",null,[t(q).check(f.permission)?(s(),a("span",{key:0,class:N([oe?"bg-indigo-600 text-white":"text-gray-900","relative cursor-pointer select-none p-2 m-1 rounded-md block"])},[e("span",{class:N([de?"font-semibold":"font-normal","block truncate"])},_(f.name),3)],2)):v("",!0)])]),_:2},1032,["value"])),64))]),_:1})]),_:1})])]),_:1},8,["modelValue"]),i.value?(s(),a("button",{key:0,onClick:l[17]||(l[17]=f=>S()),class:"rounded-lg bg-main-color-600 px-4 py-2 text-sm text-white shadow-sm hover:bg-main-color-700"}," Potvrdit ")):(s(),a("button",Zc," Potvrdit "))])])])])])):(s(),I(ue,{key:1,class:"mx-auto text-spinner-color",size:"40"}))])])]),n.value!==null?(s(),I(Ct,{key:0,meta:n.value,onSetPage:we,modelValue:P.value,"onUpdate:modelValue":l[18]||(l[18]=f=>P.value=f)},null,8,["meta","modelValue"])):v("",!0)]),t(b)?(s(),a("div",Dc,[e("p",null,_($.value.length),1),e("p",null,_(d.value.length),1),$.value==d.value?(s(),a("div",Ac,"jjjj")):v("",!0),R(" "+_($.value)+" ",1),Ic,Yc,R(" "+_(d.value)+" ",1),Ec,Bc])):v("",!0)])]),o(Jl,{ref_key:"resetPasswordRef",ref:y,onReloadUsersTable:l[19]||(l[19]=f=>X())},null,512),o(li,{ref_key:"deactivateUsersRef",ref:L,onReloadUsersTable:l[20]||(l[20]=f=>X())},null,512),o(_i,{ref_key:"enableUsersRef",ref:C,onReloadUsersTable:l[21]||(l[21]=f=>X())},null,512),o(Gi,{ref_key:"importUsersRef",ref:G,onReloadUsersTable:l[22]||(l[22]=f=>X())},null,512),o(nr,{ref_key:"promoteOuRef",ref:H,onReloadUsersTable:l[23]||(l[23]=f=>X())},null,512),o(Kn,{ref_key:"blockInternetRef",ref:Y,onReloadUsersTable:l[24]||(l[24]=f=>X())},null,512),o(Ua,{ref_key:"createUserRef",ref:z,onReloadUsersTable:l[25]||(l[25]=f=>X())},null,512),o(Oa,{ref_key:"syncAdRef",ref:V,onReloadAd:l[26]||(l[26]=f=>T.reloadAd())},null,512),o(br,{ref_key:"changeGroupRef",ref:K,onReloadUsersTable:l[27]||(l[27]=f=>X())},null,512),o(Ir,{ref_key:"changeOuRef",ref:U,onReloadUsersTable:l[28]||(l[28]=f=>X())},null,512),o(Wr,{ref_key:"deleteUsersRef",ref:O,onReloadUsersTable:l[29]||(l[29]=f=>X())},null,512)],64)}}};export{ru as default};
