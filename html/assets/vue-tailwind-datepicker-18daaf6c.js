import{a as oe,Q as Ot,K as fe,u as F,s as Re,G as Pt,M as xe,aa as jt,J as pe,o as G,b as J,d as T,c as Ye,l as he,n as we,e as re,B as ie,C as de,t as le,w as Le,V as Lt,Y as At,a5 as Tt,T as st,F as De,f as Fe,j as ce,O as St,H as Ve,I as Ne,ab as Te,ac as s,ad as $t,D as Mt}from"./index-9d9f1067.js";const It={class:"flex justify-between items-center px-2 py-1.5"},Rt={class:"flex-shrink-0"},Yt={class:"inline-flex rounded-full"},Ct={class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Bt=["d"],Ft={class:"px-1.5 space-x-1.5 flex flex-1"},Nt={class:"flex-1 flex rounded-md"},Ht=["textContent"],Ut={class:"flex-1 flex rounded-md"},zt=["textContent"],Wt={class:"flex-shrink-0"},Gt={class:"inline-flex rounded-full"},Qt={class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Zt=["d"],Ue={__name:"Header",props:{asPrevOrNext:Boolean,panel:Object,calendar:Object},setup(t){return(l,a)=>(G(),J("div",It,[T("div",Rt,[ie(T("span",Yt,[T("button",{type:"button",class:"p-1.5 rounded-full bg-white text-vtd-secondary-600 transition-colors border border-transparent hover:bg-vtd-secondary-100 hover:text-vtd-secondary-900 focus:bg-vtd-primary-50 focus:text-vtd-secondary-900 focus:border-vtd-primary-300 focus:ring focus:ring-vtd-primary-500 focus:ring-opacity-10 focus:outline-none dark:bg-vtd-secondary-800 dark:text-vtd-secondary-300 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-secondary-300 dark:focus:bg-vtd-secondary-600 dark:focus:text-vtd-secondary-100 dark:focus:border-vtd-primary-500 dark:focus:ring-opacity-25 dark:focus:bg-opacity-50",onClick:a[0]||(a[0]=e=>t.panel.calendar?t.calendar.onPrevious():t.calendar.onPreviousYear())},[(G(),J("svg",Ct,[T("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:t.panel.calendar?"M15 19l-7-7 7-7":"M11 19l-7-7 7-7m8 14l-7-7 7-7"},null,8,Bt)]))])],512),[[de,t.panel.calendar||t.panel.year]])]),T("div",Ft,[T("span",Nt,[T("button",{type:"button",class:"px-3 py-1.5 block w-full leading-relaxed rounded-md bg-white text-xs 2xl:text-sm tracking-wide text-vtd-secondary-600 font-semibold sm:font-medium transition-colors border border-transparent hover:bg-vtd-secondary-100 hover:text-vtd-secondary-900 focus:bg-vtd-primary-50 focus:text-vtd-secondary-900 focus:border-vtd-primary-300 focus:ring focus:ring-vtd-primary-500 focus:ring-opacity-10 focus:outline-none uppercase dark:bg-vtd-secondary-800 dark:text-vtd-secondary-300 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-secondary-300 dark:focus:bg-vtd-secondary-600 dark:focus:text-vtd-secondary-100 dark:focus:border-vtd-primary-500 dark:focus:ring-opacity-25 dark:focus:bg-opacity-50",textContent:le(t.calendar.month),onClick:a[1]||(a[1]=e=>t.calendar.openMonth())},null,8,Ht)]),T("span",Ut,[T("button",{type:"button",class:"px-3 py-1.5 block w-full leading-relaxed rounded-md bg-white text-xs 2xl:text-sm tracking-wide text-vtd-secondary-600 font-semibold sm:font-medium transition-colors border border-transparent hover:bg-vtd-secondary-100 hover:text-vtd-secondary-900 focus:bg-vtd-primary-50 focus:text-vtd-secondary-900 focus:border-vtd-primary-300 focus:ring focus:ring-vtd-primary-500 focus:ring-opacity-10 focus:outline-none uppercase dark:bg-vtd-secondary-800 dark:text-vtd-secondary-300 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-secondary-300 dark:focus:bg-vtd-secondary-600 dark:focus:text-vtd-secondary-100 dark:focus:border-vtd-primary-500 dark:focus:ring-opacity-25 dark:focus:bg-opacity-50",textContent:le(t.calendar.year),onClick:a[2]||(a[2]=e=>t.calendar.openYear())},null,8,zt)])]),T("div",Wt,[ie(T("span",Gt,[T("button",{type:"button",class:"p-1.5 rounded-full bg-white text-vtd-secondary-600 transition-colors border border-transparent hover:bg-vtd-secondary-100 hover:text-vtd-secondary-900 focus:bg-vtd-primary-50 focus:text-vtd-secondary-900 focus:border-vtd-primary-300 focus:ring focus:ring-vtd-primary-500 focus:ring-opacity-10 focus:outline-none dark:bg-vtd-secondary-800 dark:text-vtd-secondary-300 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-secondary-300 dark:focus:bg-vtd-secondary-600 dark:focus:text-vtd-secondary-100 dark:focus:border-vtd-primary-500 dark:focus:ring-opacity-25 dark:focus:bg-opacity-50",onClick:a[3]||(a[3]=e=>t.panel.calendar?t.calendar.onNext():t.calendar.onNextYear())},[(G(),J("svg",Qt,[T("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:t.panel.calendar?"M9 5l7 7-7 7":"M13 5l7 7-7 7M5 5l7 7-7 7"},null,8,Zt)]))])],512),[[de,t.panel.calendar||t.panel.year]])])]))}},Kt={class:"flex flex-wrap mt-1.5"},Jt={class:"flex rounded-md mt-1.5"},Xt=["textContent","onClick"],ze={__name:"Month",props:{months:Array},emits:["update:month"],setup(t,{emit:l}){return(a,e)=>(G(),J("div",Kt,[(G(!0),J(De,null,Fe(t.months,(i,d)=>(G(),J("div",{key:d,class:"w-1/2 px-0.5"},[T("span",Jt,[T("button",{type:"button",class:"px-3 py-2 block w-full leading-6 rounded-md bg-white text-xs 2xl:text-sm tracking-wide text-vtd-secondary-600 font-medium transition-colors border border-transparent hover:bg-vtd-secondary-100 hover:text-vtd-secondary-900 focus:bg-vtd-primary-50 focus:text-vtd-secondary-900 focus:border-vtd-primary-300 focus:ring focus:ring-vtd-primary-500 focus:ring-opacity-10 focus:outline-none uppercase dark:bg-vtd-secondary-800 dark:hover:bg-vtd-secondary-700 dark:text-vtd-secondary-300 dark:hover:text-vtd-secondary-100 dark:focus:bg-vtd-secondary-700",textContent:le(i),onClick:A=>l("update:month",d)},null,8,Xt)])]))),128))]))}},qt={class:"grid grid-cols-7 py-2 mt-0.5"},er=["textContent"],We={__name:"Week",props:{weeks:Array},setup(t){return(l,a)=>(G(),J("div",qt,[(G(!0),J(De,null,Fe(t.weeks,(e,i)=>(G(),J("div",{key:i,class:"text-vtd-secondary-500 text-xs 2xl:text-sm tracking-wide font-medium text-center cursor-default dark:text-vtd-secondary-400"},[T("span",{textContent:le(e)},null,8,er)]))),128))]))}},tr={class:"flex flex-wrap"},rr={class:"flex rounded-md mt-1.5"},or=["textContent","onClick"],Ge={__name:"Year",props:{asPrevOrNext:Boolean,years:Array},emits:["update:year"],setup(t,{emit:l}){return(a,e)=>(G(),J("div",tr,[(G(!0),J(De,null,Fe(t.years,(i,d)=>(G(),J("div",{key:d,class:"w-1/2 px-0.5"},[T("span",rr,[T("button",{type:"button",class:"px-3 py-2 block w-full leading-6 rounded-md bg-white text-xs 2xl:text-sm tracking-wide text-vtd-secondary-600 font-medium transition-colors border border-transparent hover:bg-vtd-secondary-100 hover:text-vtd-secondary-900 focus:bg-vtd-primary-50 focus:text-vtd-secondary-900 focus:border-vtd-primary-300 focus:ring focus:ring-vtd-primary-500 focus:ring-opacity-10 focus:outline-none uppercase dark:bg-vtd-secondary-800 dark:hover:bg-vtd-secondary-700 dark:text-vtd-secondary-300 dark:hover:text-vtd-secondary-100 dark:focus:bg-vtd-secondary-700",textContent:le(i),onClick:A=>l("update:year",i,t.asPrevOrNext)},null,8,or)])]))),128))]))}},ar={class:"grid grid-cols-7 gap-y-0.5 my-1"},nr=["data-tooltip"],sr=["disabled","onClick","onMouseenter","onFocusin","textContent","data-date"],Qe={__name:"Calendar",props:{asPrevOrNext:Boolean,calendar:Object,weeks:Array,asRange:Boolean},emits:["update:date"],setup(t,{emit:l}){const a=ce("isBetweenRange"),e=ce("betweenRangeClasses"),i=ce("datepickerClasses"),d=ce("atMouseOver");return(A,g)=>(G(),J("div",ar,[re(St,{"enter-from-class":"opacity-0","enter-to-class":"opacity-100","enter-active-class":"transition-opacity ease-out duration-300","leave-active-class":"transition-opacity ease-in duration-200","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:Le(()=>[(G(!0),J(De,null,Fe(t.calendar.date(),(E,x)=>(G(),J("div",{key:x,class:we(["relative",{"vtd-tooltip":t.asRange&&E.duration()}]),"data-tooltip":`${E.duration()}`},[re(st,{"enter-from-class":"opacity-0","enter-to-class":"opacity-100","enter-active-class":"transition-opacity ease-out duration-200","leave-active-class":"transition-opacity ease-in duration-150","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:Le(()=>[F(a)(E)||E.hovered()?(G(),J("span",{key:0,class:we(["absolute bg-vtd-primary-100 bg-opacity-60 dark:bg-vtd-secondary-700 dark:bg-opacity-50",F(e)(E)])},null,2)):he("",!0)]),_:2},1024),T("button",{type:"button",class:we(["vtd-datepicker-date relative w-[2.7rem] h-[2.7rem] lg:w-10 lg:h-10 flex justify-center items-center text-xs 2xl:text-sm",[F(i)(E),t.asRange?"transition-all":"transition-colors"]]),disabled:E.disabled||E.inRange(),onClick:j=>l("update:date",E,t.asPrevOrNext),onMouseenter:j=>F(d)(E),onFocusin:j=>F(d)(E),textContent:le(E.date()),"data-date":E.toDate()},null,42,sr)],10,nr))),128))]),_:1})]))}},lr={key:0,class:"relative w-full border-t border-b-0 sm:border-t-0 sm:border-b lg:border-b-0 lg:border-r border-black/[.1] order-last sm:order-none dark:border-vtd-secondary-700/[1] sm:mt-1 lg:mr-1 sm:mb-1 lg:mb-0 sm:mx-1 lg:mx-0"},ir={key:0,class:"grid grid-cols-2 sm:grid-cols-3 gap-1 lg:block w-full pr-5 sm:pr-6 mt-1.5 sm:mt-0 sm:mb-1.5 lg:mb-0"},ur=["onClick","textContent"],dr={key:1,class:"grid grid-cols-2 sm:grid-cols-3 gap-1 lg:block w-full pr-5 sm:pr-6 mt-1.5 sm:mt-0 sm:mb-1.5 lg:mb-0"},at={__name:"Shortcut",props:{shortcuts:[Boolean,Function],close:Function,asRange:Boolean,asSingle:Boolean,i18n:Object},setup(t){const l=t,a=ce("setToToday"),e=ce("setToYesterday"),i=ce("setToLastDay"),d=ce("setToThisMonth"),A=ce("setToLastMonth"),g=ce("setToCustomShortcut"),E=()=>typeof l.shortcuts=="function"?l.shortcuts():!1;return(x,j)=>l.asRange&&l.asSingle||l.asRange&&!l.asSingle?(G(),J("div",lr,[E()?(G(),J("ol",ir,[(G(!0),J(De,null,Fe(E(),(k,O)=>(G(),J("li",{key:O},[T("a",{href:"#",class:"vtd-shortcuts block text-sm lg:text-xs px-2 py-2 sm:leading-4 whitespace-nowrap font-medium rounded text-vtd-primary-600 hover:text-vtd-primary-700 transition-colors hover:bg-vtd-secondary-100 focus:bg-vtd-secondary-100 focus:text-vtd-primary-600 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-primary-300 dark:text-vtd-primary-400 dark:focus:bg-vtd-secondary-700 dark:focus:text-vtd-primary-300",onClick:Ve(N=>F(g)(k,t.close),["prevent"]),textContent:le(k.label)},null,8,ur)]))),128))])):(G(),J("ol",dr,[T("li",null,[T("a",{href:"#",class:"vtd-shortcuts block text-sm lg:text-xs px-2 py-2 sm:leading-4 whitespace-nowrap font-medium rounded text-vtd-primary-600 hover:text-vtd-primary-700 transition-colors hover:bg-vtd-secondary-100 focus:bg-vtd-secondary-100 focus:text-vtd-primary-600 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-primary-300 dark:text-vtd-primary-400 dark:focus:bg-vtd-secondary-700 dark:focus:text-vtd-primary-300",onClick:j[0]||(j[0]=Ve(k=>F(a)(t.close),["prevent"]))},le(l.i18n.today),1)]),T("li",null,[T("a",{href:"#",class:"vtd-shortcuts block text-sm lg:text-xs px-2 py-2 sm:leading-4 whitespace-nowrap font-medium rounded text-vtd-primary-600 hover:text-vtd-primary-700 transition-colors hover:bg-vtd-secondary-100 focus:bg-vtd-secondary-100 focus:text-vtd-primary-600 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-primary-300 dark:text-vtd-primary-400 dark:focus:bg-vtd-secondary-700 dark:focus:text-vtd-primary-300",onClick:j[1]||(j[1]=Ve(k=>F(e)(t.close),["prevent"]))},le(l.i18n.yesterday),1)]),T("li",null,[T("a",{href:"#",class:"vtd-shortcuts block text-sm lg:text-xs px-2 py-2 sm:leading-4 whitespace-nowrap font-medium rounded text-vtd-primary-600 hover:text-vtd-primary-700 transition-colors hover:bg-vtd-secondary-100 focus:bg-vtd-secondary-100 focus:text-vtd-primary-600 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-primary-300 dark:text-vtd-primary-400 dark:focus:bg-vtd-secondary-700 dark:focus:text-vtd-primary-300",onClick:j[2]||(j[2]=Ve(k=>F(i)(7,t.close),["prevent"]))},le(l.i18n.past(7)),1)]),T("li",null,[T("a",{href:"#",class:"vtd-shortcuts block text-sm lg:text-xs px-2 py-2 sm:leading-4 whitespace-nowrap font-medium rounded text-vtd-primary-600 hover:text-vtd-primary-700 transition-colors hover:bg-vtd-secondary-100 focus:bg-vtd-secondary-100 focus:text-vtd-primary-600 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-primary-300 dark:text-vtd-primary-400 dark:focus:bg-vtd-secondary-700 dark:focus:text-vtd-primary-300",onClick:j[3]||(j[3]=Ve(k=>F(i)(30,t.close),["prevent"]))},le(l.i18n.past(30)),1)]),T("li",null,[T("a",{href:"#",class:"vtd-shortcuts block text-sm lg:text-xs px-2 py-2 sm:leading-4 whitespace-nowrap font-medium rounded text-vtd-primary-600 hover:text-vtd-primary-700 transition-colors hover:bg-vtd-secondary-100 focus:bg-vtd-secondary-100 focus:text-vtd-primary-600 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-primary-300 dark:text-vtd-primary-400 dark:focus:bg-vtd-secondary-700 dark:focus:text-vtd-primary-300",onClick:j[4]||(j[4]=Ve(k=>F(d)(t.close),["prevent"]))},le(l.i18n.currentMonth),1)]),T("li",null,[T("a",{href:"#",class:"vtd-shortcuts block text-sm lg:text-xs px-2 py-2 sm:leading-4 whitespace-nowrap font-medium rounded text-vtd-primary-600 hover:text-vtd-primary-700 transition-colors hover:bg-vtd-secondary-100 focus:bg-vtd-secondary-100 focus:text-vtd-primary-600 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-primary-300 dark:text-vtd-primary-400 dark:focus:bg-vtd-secondary-700 dark:focus:text-vtd-primary-300",onClick:j[5]||(j[5]=Ve(k=>F(A)(t.close),["prevent"]))},le(l.i18n.pastMonth),1)])]))])):he("",!0)}};function Ee(t,l,...a){if(t in l){let i=l[t];return typeof i=="function"?i(...a):i}let e=new Error(`Tried to handle "${t}" but there is no handler defined. Only defined handlers are: ${Object.keys(l).map(i=>`"${i}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,Ee),e}var Ce=(t=>(t[t.None=0]="None",t[t.RenderStrategy=1]="RenderStrategy",t[t.Static=2]="Static",t))(Ce||{}),cr=(t=>(t[t.Unmount=0]="Unmount",t[t.Hidden=1]="Hidden",t))(cr||{});function He({visible:t=!0,features:l=0,ourProps:a,theirProps:e,...i}){var d;let A=vr(e,a),g=Object.assign(i,{props:A});if(t||l&2&&A.static)return qe(g);if(l&1){let E=(d=A.unmount)==null||d?0:1;return Ee(E,{[0](){return null},[1](){return qe({...i,props:{...A,hidden:!0,style:{display:"none"}}})}})}return qe(g)}function qe({props:t,attrs:l,slots:a,slot:e,name:i}){var d;let{as:A,...g}=it(t,["unmount","static"]),E=(d=a.default)==null?void 0:d.call(a,e),x={};if(e){let j=!1,k=[];for(let[O,N]of Object.entries(e))typeof N=="boolean"&&(j=!0),N===!0&&k.push(O);j&&(x["data-headlessui-state"]=k.join(" "))}if(A==="template"){if(E=lt(E),Object.keys(g).length>0||Object.keys(l).length>0){let[j,...k]=E??[];if(!pr(j)||k.length>0)throw new Error(['Passing props on "template"!',"",`The current component <${i} /> is rendering a "template".`,"However we need to passthrough the following props:",Object.keys(g).concat(Object.keys(l)).sort((O,N)=>O.localeCompare(N)).map(O=>`  - ${O}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "template".',"Render a single element as the child so that we can forward the props onto that element."].map(O=>`  - ${O}`).join(`
`)].join(`
`));return $t(j,Object.assign({},g,x))}return Array.isArray(E)&&E.length===1?E[0]:E}return Te(A,Object.assign({},g,x),E)}function lt(t){return t.flatMap(l=>l.type===De?lt(l.children):[l])}function vr(...t){if(t.length===0)return{};if(t.length===1)return t[0];let l={},a={};for(let e of t)for(let i in e)i.startsWith("on")&&typeof e[i]=="function"?(a[i]!=null||(a[i]=[]),a[i].push(e[i])):l[i]=e[i];if(l.disabled||l["aria-disabled"])return Object.assign(l,Object.fromEntries(Object.keys(a).map(e=>[e,void 0])));for(let e in a)Object.assign(l,{[e](i,...d){let A=a[e];for(let g of A){if(i instanceof Event&&i.defaultPrevented)return;g(i,...d)}}});return l}function it(t,l=[]){let a=Object.assign({},t);for(let e of l)e in a&&delete a[e];return a}function pr(t){return t==null?!1:typeof t.type=="string"||typeof t.type=="object"||typeof t.type=="function"}let fr=0;function mr(){return++fr}function Se(){return mr()}var ke=(t=>(t.Space=" ",t.Enter="Enter",t.Escape="Escape",t.Backspace="Backspace",t.Delete="Delete",t.ArrowLeft="ArrowLeft",t.ArrowUp="ArrowUp",t.ArrowRight="ArrowRight",t.ArrowDown="ArrowDown",t.Home="Home",t.End="End",t.PageUp="PageUp",t.PageDown="PageDown",t.Tab="Tab",t))(ke||{});function z(t){var l;return t==null||t.value==null?null:(l=t.value.$el)!=null?l:t.value}let ut=Symbol("Context");var Be=(t=>(t[t.Open=0]="Open",t[t.Closed=1]="Closed",t))(Be||{});function dt(){return ce(ut,null)}function hr(t){pe(ut,t)}function nt(t,l){if(t)return t;let a=l??"button";if(typeof a=="string"&&a.toLowerCase()==="button")return"button"}function yr(t,l){let a=oe(nt(t.value.type,t.value.as));return Mt(()=>{a.value=nt(t.value.type,t.value.as)}),xe(()=>{var e;a.value||!z(l)||z(l)instanceof HTMLButtonElement&&!((e=z(l))!=null&&e.hasAttribute("type"))&&(a.value="button")}),a}const Ke=typeof window>"u"||typeof document>"u";function Je(t){if(Ke)return null;if(t instanceof Node)return t.ownerDocument;if(t!=null&&t.hasOwnProperty("value")){let l=z(t);if(l)return l.ownerDocument}return document}let tt=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(t=>`${t}:not([tabindex='-1'])`).join(",");var Oe=(t=>(t[t.First=1]="First",t[t.Previous=2]="Previous",t[t.Next=4]="Next",t[t.Last=8]="Last",t[t.WrapAround=16]="WrapAround",t[t.NoScroll=32]="NoScroll",t))(Oe||{}),_r=(t=>(t[t.Error=0]="Error",t[t.Overflow=1]="Overflow",t[t.Success=2]="Success",t[t.Underflow=3]="Underflow",t))(_r||{}),br=(t=>(t[t.Previous=-1]="Previous",t[t.Next=1]="Next",t))(br||{});function ct(t=document.body){return t==null?[]:Array.from(t.querySelectorAll(tt))}var ot=(t=>(t[t.Strict=0]="Strict",t[t.Loose=1]="Loose",t))(ot||{});function vt(t,l=0){var a;return t===((a=Je(t))==null?void 0:a.body)?!1:Ee(l,{[0](){return t.matches(tt)},[1](){let e=t;for(;e!==null;){if(e.matches(tt))return!0;e=e.parentElement}return!1}})}let gr=["textarea","input"].join(",");function xr(t){var l,a;return(a=(l=t==null?void 0:t.matches)==null?void 0:l.call(t,gr))!=null?a:!1}function kr(t,l=a=>a){return t.slice().sort((a,e)=>{let i=l(a),d=l(e);if(i===null||d===null)return 0;let A=i.compareDocumentPosition(d);return A&Node.DOCUMENT_POSITION_FOLLOWING?-1:A&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function Ae(t,l,a=!0,e=null){var i;let d=(i=Array.isArray(t)?t.length>0?t[0].ownerDocument:document:t==null?void 0:t.ownerDocument)!=null?i:document,A=Array.isArray(t)?a?kr(t):t:ct(t);e=e??d.activeElement;let g=(()=>{if(l&5)return 1;if(l&10)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),E=(()=>{if(l&1)return 0;if(l&2)return Math.max(0,A.indexOf(e))-1;if(l&4)return Math.max(0,A.indexOf(e))+1;if(l&8)return A.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),x=l&32?{preventScroll:!0}:{},j=0,k=A.length,O;do{if(j>=k||j+k<=0)return 0;let N=E+j;if(l&16)N=(N+k)%k;else{if(N<0)return 3;if(N>=k)return 1}O=A[N],O==null||O.focus(x),j+=g}while(O!==d.activeElement);return l&6&&xr(O)&&O.select(),O.hasAttribute("tabindex")||O.setAttribute("tabindex","0"),2}function et(t,l,a){Ke||xe(e=>{document.addEventListener(t,l,a),e(()=>document.removeEventListener(t,l,a))})}function wr(t,l,a=fe(()=>!0)){function e(d,A){if(!a.value||d.defaultPrevented)return;let g=A(d);if(g===null||!g.ownerDocument.documentElement.contains(g))return;let E=function x(j){return typeof j=="function"?x(j()):Array.isArray(j)||j instanceof Set?j:[j]}(t);for(let x of E){if(x===null)continue;let j=x instanceof HTMLElement?x:z(x);if(j!=null&&j.contains(g))return}return!vt(g,ot.Loose)&&g.tabIndex!==-1&&d.preventDefault(),l(d,g)}let i=oe(null);et("mousedown",d=>{a.value&&(i.value=d.target)},!0),et("click",d=>{!i.value||(e(d,()=>i.value),i.value=null)},!0),et("blur",d=>e(d,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}var Ze=(t=>(t[t.None=1]="None",t[t.Focusable=2]="Focusable",t[t.Hidden=4]="Hidden",t))(Ze||{});let rt=Ne({name:"Hidden",props:{as:{type:[Object,String],default:"div"},features:{type:Number,default:1}},setup(t,{slots:l,attrs:a}){return()=>{let{features:e,...i}=t,d={"aria-hidden":(e&2)===2?!0:void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(e&4)===4&&(e&2)!==2&&{display:"none"}}};return He({ourProps:d,theirProps:i,slot:{},attrs:a,slots:l,name:"Hidden"})}}});function Er(t,l,a){Ke||xe(e=>{window.addEventListener(t,l,a),e(()=>window.removeEventListener(t,l,a))})}var Pe=(t=>(t[t.Forwards=0]="Forwards",t[t.Backwards=1]="Backwards",t))(Pe||{});function pt(){let t=oe(0);return Er("keydown",l=>{l.key==="Tab"&&(t.value=l.shiftKey?1:0)}),t}function Dr(t,l,a,e){Ke||xe(i=>{t=t??window,t.addEventListener(l,a,e),i(()=>t.removeEventListener(l,a,e))})}var Vr=(t=>(t[t.Open=0]="Open",t[t.Closed=1]="Closed",t))(Vr||{});let ft=Symbol("PopoverContext");function Xe(t){let l=ce(ft,null);if(l===null){let a=new Error(`<${t} /> is missing a parent <${yt.name} /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(a,Xe),a}return l}let Or=Symbol("PopoverGroupContext");function mt(){return ce(Or,null)}let ht=Symbol("PopoverPanelContext");function Pr(){return ce(ht,null)}let yt=Ne({name:"Popover",props:{as:{type:[Object,String],default:"div"}},setup(t,{slots:l,attrs:a,expose:e}){var i;let d=`headlessui-popover-button-${Se()}`,A=`headlessui-popover-panel-${Se()}`,g=oe(null);e({el:g,$el:g});let E=oe(1),x=oe(null),j=oe(null),k=oe(null),O=oe(null),N=fe(()=>Je(g)),K=fe(()=>{if(!z(x)||!z(O))return!1;for(let S of document.querySelectorAll("body > *"))if(Number(S==null?void 0:S.contains(z(x)))^Number(S==null?void 0:S.contains(z(O))))return!0;return!1}),V={popoverState:E,buttonId:d,panelId:A,panel:O,button:x,isPortalled:K,beforePanelSentinel:j,afterPanelSentinel:k,togglePopover(){E.value=Ee(E.value,{[0]:1,[1]:0})},closePopover(){E.value!==1&&(E.value=1)},close(S){V.closePopover();let $=(()=>S?S instanceof HTMLElement?S:S.value instanceof HTMLElement?z(S):z(V.button):z(V.button))();$==null||$.focus()}};pe(ft,V),hr(fe(()=>Ee(E.value,{[0]:Be.Open,[1]:Be.Closed})));let L={buttonId:d,panelId:A,close(){V.closePopover()}},v=mt(),_=v==null?void 0:v.registerPopover;function D(){var S,$,n,r;return(r=v==null?void 0:v.isFocusWithinPopoverGroup())!=null?r:((S=N.value)==null?void 0:S.activeElement)&&((($=z(x))==null?void 0:$.contains(N.value.activeElement))||((n=z(O))==null?void 0:n.contains(N.value.activeElement)))}return xe(()=>_==null?void 0:_(L)),Dr((i=N.value)==null?void 0:i.defaultView,"focus",S=>{var $,n;E.value===0&&(D()||!x||!O||($=z(V.beforePanelSentinel))!=null&&$.contains(S.target)||(n=z(V.afterPanelSentinel))!=null&&n.contains(S.target)||V.closePopover())},!0),wr([x,O],(S,$)=>{var n;V.closePopover(),vt($,ot.Loose)||(S.preventDefault(),(n=z(x))==null||n.focus())},fe(()=>E.value===0)),()=>{let S={open:E.value===0,close:V.close};return He({theirProps:t,ourProps:{ref:g},slot:S,slots:l,attrs:a,name:"Popover"})}}}),jr=Ne({name:"PopoverButton",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(t,{attrs:l,slots:a,expose:e}){let i=Xe("PopoverButton"),d=fe(()=>Je(i.button));e({el:i.button,$el:i.button});let A=mt(),g=A==null?void 0:A.closeOthers,E=Pr(),x=E===null?!1:E===i.panelId,j=oe(null),k=`headlessui-focus-sentinel-${Se()}`;x||xe(()=>{i.button.value=j.value});let O=yr(fe(()=>({as:t.as,type:l.type})),j);function N(v){var _,D,S,$,n;if(x){if(i.popoverState.value===1)return;switch(v.key){case ke.Space:case ke.Enter:v.preventDefault(),(D=(_=v.target).click)==null||D.call(_),i.closePopover(),(S=z(i.button))==null||S.focus();break}}else switch(v.key){case ke.Space:case ke.Enter:v.preventDefault(),v.stopPropagation(),i.popoverState.value===1&&(g==null||g(i.buttonId)),i.togglePopover();break;case ke.Escape:if(i.popoverState.value!==0)return g==null?void 0:g(i.buttonId);if(!z(i.button)||($=d.value)!=null&&$.activeElement&&!((n=z(i.button))!=null&&n.contains(d.value.activeElement)))return;v.preventDefault(),v.stopPropagation(),i.closePopover();break}}function K(v){x||v.key===ke.Space&&v.preventDefault()}function V(v){var _,D;t.disabled||(x?(i.closePopover(),(_=z(i.button))==null||_.focus()):(v.preventDefault(),v.stopPropagation(),i.popoverState.value===1&&(g==null||g(i.buttonId)),i.togglePopover(),(D=z(i.button))==null||D.focus()))}function L(v){v.preventDefault(),v.stopPropagation()}return()=>{let v=i.popoverState.value===0,_={open:v},D=x?{ref:j,type:O.value,onKeydown:N,onClick:V}:{ref:j,id:i.buttonId,type:O.value,"aria-expanded":t.disabled?void 0:i.popoverState.value===0,"aria-controls":z(i.panel)?i.panelId:void 0,disabled:t.disabled?!0:void 0,onKeydown:N,onKeyup:K,onClick:V,onMousedown:L},S=pt();function $(){let n=z(i.panel);if(!n)return;function r(){Ee(S.value,{[Pe.Forwards]:()=>Ae(n,Oe.First),[Pe.Backwards]:()=>Ae(n,Oe.Last)})}r()}return Te(De,[He({ourProps:D,theirProps:{...l,...t},slot:_,attrs:l,slots:a,name:"PopoverButton"}),v&&!x&&i.isPortalled.value&&Te(rt,{id:k,features:Ze.Focusable,as:"button",type:"button",onFocus:$})])}}}),Lr=Ne({name:"PopoverOverlay",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0}},setup(t,{attrs:l,slots:a}){let e=Xe("PopoverOverlay"),i=`headlessui-popover-overlay-${Se()}`,d=dt(),A=fe(()=>d!==null?d.value===Be.Open:e.popoverState.value===0);function g(){e.closePopover()}return()=>{let E={open:e.popoverState.value===0};return He({ourProps:{id:i,"aria-hidden":!0,onClick:g},theirProps:t,slot:E,attrs:l,slots:a,features:Ce.RenderStrategy|Ce.Static,visible:A.value,name:"PopoverOverlay"})}}}),Ar=Ne({name:"PopoverPanel",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},focus:{type:Boolean,default:!1}},inheritAttrs:!1,setup(t,{attrs:l,slots:a,expose:e}){let{focus:i}=t,d=Xe("PopoverPanel"),A=fe(()=>Je(d.panel)),g=`headlessui-focus-sentinel-before-${Se()}`,E=`headlessui-focus-sentinel-after-${Se()}`;e({el:d.panel,$el:d.panel}),pe(ht,d.panelId),xe(()=>{var L,v;if(!i||d.popoverState.value!==0||!d.panel)return;let _=(L=A.value)==null?void 0:L.activeElement;(v=z(d.panel))!=null&&v.contains(_)||Ae(z(d.panel),Oe.First)});let x=dt(),j=fe(()=>x!==null?x.value===Be.Open:d.popoverState.value===0);function k(L){var v,_;switch(L.key){case ke.Escape:if(d.popoverState.value!==0||!z(d.panel)||A.value&&!((v=z(d.panel))!=null&&v.contains(A.value.activeElement)))return;L.preventDefault(),L.stopPropagation(),d.closePopover(),(_=z(d.button))==null||_.focus();break}}function O(L){var v,_,D,S,$;let n=L.relatedTarget;!n||!z(d.panel)||(v=z(d.panel))!=null&&v.contains(n)||(d.closePopover(),((D=(_=z(d.beforePanelSentinel))==null?void 0:_.contains)!=null&&D.call(_,n)||($=(S=z(d.afterPanelSentinel))==null?void 0:S.contains)!=null&&$.call(S,n))&&n.focus({preventScroll:!0}))}let N=pt();function K(){let L=z(d.panel);if(!L)return;function v(){Ee(N.value,{[Pe.Forwards]:()=>{Ae(L,Oe.Next)},[Pe.Backwards]:()=>{var _;(_=z(d.button))==null||_.focus({preventScroll:!0})}})}v()}function V(){let L=z(d.panel);if(!L)return;function v(){Ee(N.value,{[Pe.Forwards]:()=>{var _,D;let S=z(d.button),$=z(d.panel);if(!S)return;let n=ct(),r=n.indexOf(S),B=n.slice(0,r+1),R=[...n.slice(r+1),...B];for(let b of R.slice())if((D=(_=b==null?void 0:b.id)==null?void 0:_.startsWith)!=null&&D.call(_,"headlessui-focus-sentinel-")||$!=null&&$.contains(b)){let Q=R.indexOf(b);Q!==-1&&R.splice(Q,1)}Ae(R,Oe.First,!1)},[Pe.Backwards]:()=>Ae(L,Oe.Previous)})}v()}return()=>{let L={open:d.popoverState.value===0,close:d.close},v={ref:d.panel,id:d.panelId,onKeydown:k,onFocusout:i&&d.popoverState.value===0?O:void 0,tabIndex:-1};return He({ourProps:v,theirProps:{...l,...it(t,["focus"])},attrs:l,slot:L,slots:{...a,default:(..._)=>{var D;return[Te(De,[j.value&&d.isPortalled.value&&Te(rt,{id:g,ref:d.beforePanelSentinel,features:Ze.Focusable,as:"button",type:"button",onFocus:K}),(D=a.default)==null?void 0:D.call(a,..._),j.value&&d.isPortalled.value&&Te(rt,{id:E,ref:d.afterPanelSentinel,features:Ze.Focusable,as:"button",type:"button",onFocus:V})])]}},features:Ce.RenderStrategy|Ce.Static,visible:j.value,name:"PopoverPanel"})}}});var je=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},_t={exports:{}};(function(t,l){(function(a,e){t.exports=e()})(je,function(){var a=1e3,e=6e4,i=36e5,d="millisecond",A="second",g="minute",E="hour",x="day",j="week",k="month",O="quarter",N="year",K="date",V="Invalid Date",L=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,v=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,_={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(M){var h=["th","st","nd","rd"],m=M%100;return"["+M+(h[(m-20)%10]||h[m]||h[0])+"]"}},D=function(M,h,m){var P=String(M);return!P||P.length>=h?M:""+Array(h+1-P.length).join(m)+M},S={s:D,z:function(M){var h=-M.utcOffset(),m=Math.abs(h),P=Math.floor(m/60),w=m%60;return(h<=0?"+":"-")+D(P,2,"0")+":"+D(w,2,"0")},m:function M(h,m){if(h.date()<m.date())return-M(m,h);var P=12*(m.year()-h.year())+(m.month()-h.month()),w=h.clone().add(P,k),Y=m-w<0,I=h.clone().add(P+(Y?-1:1),k);return+(-(P+(m-w)/(Y?w-I:I-w))||0)},a:function(M){return M<0?Math.ceil(M)||0:Math.floor(M)},p:function(M){return{M:k,y:N,w:j,d:x,D:K,h:E,m:g,s:A,ms:d,Q:O}[M]||String(M||"").toLowerCase().replace(/s$/,"")},u:function(M){return M===void 0}},$="en",n={};n[$]=_;var r=function(M){return M instanceof Q},B=function M(h,m,P){var w;if(!h)return $;if(typeof h=="string"){var Y=h.toLowerCase();n[Y]&&(w=Y),m&&(n[Y]=m,w=Y);var I=h.split("-");if(!w&&I.length>1)return M(I[0])}else{var W=h.name;n[W]=h,w=W}return!P&&w&&($=w),w||!P&&$},R=function(M,h){if(r(M))return M.clone();var m=typeof h=="object"?h:{};return m.date=M,m.args=arguments,new Q(m)},b=S;b.l=B,b.i=r,b.w=function(M,h){return R(M,{locale:h.$L,utc:h.$u,x:h.$x,$offset:h.$offset})};var Q=function(){function M(m){this.$L=B(m.locale,null,!0),this.parse(m)}var h=M.prototype;return h.parse=function(m){this.$d=function(P){var w=P.date,Y=P.utc;if(w===null)return new Date(NaN);if(b.u(w))return new Date;if(w instanceof Date)return new Date(w);if(typeof w=="string"&&!/Z$/i.test(w)){var I=w.match(L);if(I){var W=I[2]-1||0,Z=(I[7]||"0").substring(0,3);return Y?new Date(Date.UTC(I[1],W,I[3]||1,I[4]||0,I[5]||0,I[6]||0,Z)):new Date(I[1],W,I[3]||1,I[4]||0,I[5]||0,I[6]||0,Z)}}return new Date(w)}(m),this.$x=m.x||{},this.init()},h.init=function(){var m=this.$d;this.$y=m.getFullYear(),this.$M=m.getMonth(),this.$D=m.getDate(),this.$W=m.getDay(),this.$H=m.getHours(),this.$m=m.getMinutes(),this.$s=m.getSeconds(),this.$ms=m.getMilliseconds()},h.$utils=function(){return b},h.isValid=function(){return this.$d.toString()!==V},h.isSame=function(m,P){var w=R(m);return this.startOf(P)<=w&&w<=this.endOf(P)},h.isAfter=function(m,P){return R(m)<this.startOf(P)},h.isBefore=function(m,P){return this.endOf(P)<R(m)},h.$g=function(m,P,w){return b.u(m)?this[P]:this.set(w,m)},h.unix=function(){return Math.floor(this.valueOf()/1e3)},h.valueOf=function(){return this.$d.getTime()},h.startOf=function(m,P){var w=this,Y=!!b.u(P)||P,I=b.p(m),W=function(se,ne){var ue=b.w(w.$u?Date.UTC(w.$y,ne,se):new Date(w.$y,ne,se),w);return Y?ue:ue.endOf(x)},Z=function(se,ne){return b.w(w.toDate()[se].apply(w.toDate("s"),(Y?[0,0,0,0]:[23,59,59,999]).slice(ne)),w)},q=this.$W,ae=this.$M,me=this.$D,ve="set"+(this.$u?"UTC":"");switch(I){case N:return Y?W(1,0):W(31,11);case k:return Y?W(1,ae):W(0,ae+1);case j:var ge=this.$locale().weekStart||0,ye=(q<ge?q+7:q)-ge;return W(Y?me-ye:me+(6-ye),ae);case x:case K:return Z(ve+"Hours",0);case E:return Z(ve+"Minutes",1);case g:return Z(ve+"Seconds",2);case A:return Z(ve+"Milliseconds",3);default:return this.clone()}},h.endOf=function(m){return this.startOf(m,!1)},h.$set=function(m,P){var w,Y=b.p(m),I="set"+(this.$u?"UTC":""),W=(w={},w[x]=I+"Date",w[K]=I+"Date",w[k]=I+"Month",w[N]=I+"FullYear",w[E]=I+"Hours",w[g]=I+"Minutes",w[A]=I+"Seconds",w[d]=I+"Milliseconds",w)[Y],Z=Y===x?this.$D+(P-this.$W):P;if(Y===k||Y===N){var q=this.clone().set(K,1);q.$d[W](Z),q.init(),this.$d=q.set(K,Math.min(this.$D,q.daysInMonth())).$d}else W&&this.$d[W](Z);return this.init(),this},h.set=function(m,P){return this.clone().$set(m,P)},h.get=function(m){return this[b.p(m)]()},h.add=function(m,P){var w,Y=this;m=Number(m);var I=b.p(P),W=function(ae){var me=R(Y);return b.w(me.date(me.date()+Math.round(ae*m)),Y)};if(I===k)return this.set(k,this.$M+m);if(I===N)return this.set(N,this.$y+m);if(I===x)return W(1);if(I===j)return W(7);var Z=(w={},w[g]=e,w[E]=i,w[A]=a,w)[I]||1,q=this.$d.getTime()+m*Z;return b.w(q,this)},h.subtract=function(m,P){return this.add(-1*m,P)},h.format=function(m){var P=this,w=this.$locale();if(!this.isValid())return w.invalidDate||V;var Y=m||"YYYY-MM-DDTHH:mm:ssZ",I=b.z(this),W=this.$H,Z=this.$m,q=this.$M,ae=w.weekdays,me=w.months,ve=function(ne,ue,_e,be){return ne&&(ne[ue]||ne(P,Y))||_e[ue].slice(0,be)},ge=function(ne){return b.s(W%12||12,ne,"0")},ye=w.meridiem||function(ne,ue,_e){var be=ne<12?"AM":"PM";return _e?be.toLowerCase():be},se={YY:String(this.$y).slice(-2),YYYY:this.$y,M:q+1,MM:b.s(q+1,2,"0"),MMM:ve(w.monthsShort,q,me,3),MMMM:ve(me,q),D:this.$D,DD:b.s(this.$D,2,"0"),d:String(this.$W),dd:ve(w.weekdaysMin,this.$W,ae,2),ddd:ve(w.weekdaysShort,this.$W,ae,3),dddd:ae[this.$W],H:String(W),HH:b.s(W,2,"0"),h:ge(1),hh:ge(2),a:ye(W,Z,!0),A:ye(W,Z,!1),m:String(Z),mm:b.s(Z,2,"0"),s:String(this.$s),ss:b.s(this.$s,2,"0"),SSS:b.s(this.$ms,3,"0"),Z:I};return Y.replace(v,function(ne,ue){return ue||se[ne]||I.replace(":","")})},h.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},h.diff=function(m,P,w){var Y,I=b.p(P),W=R(m),Z=(W.utcOffset()-this.utcOffset())*e,q=this-W,ae=b.m(this,W);return ae=(Y={},Y[N]=ae/12,Y[k]=ae,Y[O]=ae/3,Y[j]=(q-Z)/6048e5,Y[x]=(q-Z)/864e5,Y[E]=q/i,Y[g]=q/e,Y[A]=q/a,Y)[I]||q,w?ae:b.a(ae)},h.daysInMonth=function(){return this.endOf(k).$D},h.$locale=function(){return n[this.$L]},h.locale=function(m,P){if(!m)return this.$L;var w=this.clone(),Y=B(m,P,!0);return Y&&(w.$L=Y),w},h.clone=function(){return b.w(this.$d,this)},h.toDate=function(){return new Date(this.valueOf())},h.toJSON=function(){return this.isValid()?this.toISOString():null},h.toISOString=function(){return this.$d.toISOString()},h.toString=function(){return this.$d.toUTCString()},M}(),X=Q.prototype;return R.prototype=X,[["$ms",d],["$s",A],["$m",g],["$H",E],["$W",x],["$M",k],["$y",N],["$D",K]].forEach(function(M){X[M[1]]=function(h){return this.$g(h,M[0],M[1])}}),R.extend=function(M,h){return M.$i||(M(h,Q,R),M.$i=!0),R},R.locale=B,R.isDayjs=r,R.unix=function(M){return R(1e3*M)},R.en=n[$],R.Ls=n,R.p={},R})})(_t);const u=_t.exports;var bt={exports:{}};(function(t,l){(function(a,e){t.exports=e()})(je,function(){return function(a,e,i){var d=e.prototype,A=function(k){return k&&(k.indexOf?k:k.s)},g=function(k,O,N,K,V){var L=k.name?k:k.$locale(),v=A(L[O]),_=A(L[N]),D=v||_.map(function($){return $.slice(0,K)});if(!V)return D;var S=L.weekStart;return D.map(function($,n){return D[(n+(S||0))%7]})},E=function(){return i.Ls[i.locale()]},x=function(k,O){return k.formats[O]||function(N){return N.replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(K,V,L){return V||L.slice(1)})}(k.formats[O.toUpperCase()])},j=function(){var k=this;return{months:function(O){return O?O.format("MMMM"):g(k,"months")},monthsShort:function(O){return O?O.format("MMM"):g(k,"monthsShort","months",3)},firstDayOfWeek:function(){return k.$locale().weekStart||0},weekdays:function(O){return O?O.format("dddd"):g(k,"weekdays")},weekdaysMin:function(O){return O?O.format("dd"):g(k,"weekdaysMin","weekdays",2)},weekdaysShort:function(O){return O?O.format("ddd"):g(k,"weekdaysShort","weekdays",3)},longDateFormat:function(O){return x(k.$locale(),O)},meridiem:this.$locale().meridiem,ordinal:this.$locale().ordinal}};d.localeData=function(){return j.bind(this)()},i.localeData=function(){var k=E();return{firstDayOfWeek:function(){return k.weekStart||0},weekdays:function(){return i.weekdays()},weekdaysShort:function(){return i.weekdaysShort()},weekdaysMin:function(){return i.weekdaysMin()},months:function(){return i.months()},monthsShort:function(){return i.monthsShort()},longDateFormat:function(O){return x(k,O)},meridiem:k.meridiem,ordinal:k.ordinal}},i.months=function(){return g(E(),"months")},i.monthsShort=function(){return g(E(),"monthsShort","months",3)},i.weekdays=function(k){return g(E(),"weekdays",null,null,k)},i.weekdaysShort=function(k){return g(E(),"weekdaysShort","weekdays",3,k)},i.weekdaysMin=function(k){return g(E(),"weekdaysMin","weekdays",2,k)}}})})(bt);const Tr=bt.exports;var gt={exports:{}};(function(t,l){(function(a,e){t.exports=e()})(je,function(){var a={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"};return function(e,i,d){var A=i.prototype,g=A.format;d.en.formats=a,A.format=function(E){E===void 0&&(E="YYYY-MM-DDTHH:mm:ssZ");var x=this.$locale().formats,j=function(k,O){return k.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,function(N,K,V){var L=V&&V.toUpperCase();return K||O[V]||a[V]||O[L].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(v,_,D){return _||D.slice(1)})})}(E,x===void 0?{}:x);return g.call(this,j)}}})})(gt);const Sr=gt.exports;var xt={exports:{}};(function(t,l){(function(a,e){t.exports=e()})(je,function(){var a={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},e=/(\[[^[]*\])|([-_:/.,()\s]+)|(A|a|YYYY|YY?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,i=/\d\d/,d=/\d\d?/,A=/\d*[^-_:/,()\s\d]+/,g={},E=function(V){return(V=+V)+(V>68?1900:2e3)},x=function(V){return function(L){this[V]=+L}},j=[/[+-]\d\d:?(\d\d)?|Z/,function(V){(this.zone||(this.zone={})).offset=function(L){if(!L||L==="Z")return 0;var v=L.match(/([+-]|\d\d)/g),_=60*v[1]+(+v[2]||0);return _===0?0:v[0]==="+"?-_:_}(V)}],k=function(V){var L=g[V];return L&&(L.indexOf?L:L.s.concat(L.f))},O=function(V,L){var v,_=g.meridiem;if(_){for(var D=1;D<=24;D+=1)if(V.indexOf(_(D,0,L))>-1){v=D>12;break}}else v=V===(L?"pm":"PM");return v},N={A:[A,function(V){this.afternoon=O(V,!1)}],a:[A,function(V){this.afternoon=O(V,!0)}],S:[/\d/,function(V){this.milliseconds=100*+V}],SS:[i,function(V){this.milliseconds=10*+V}],SSS:[/\d{3}/,function(V){this.milliseconds=+V}],s:[d,x("seconds")],ss:[d,x("seconds")],m:[d,x("minutes")],mm:[d,x("minutes")],H:[d,x("hours")],h:[d,x("hours")],HH:[d,x("hours")],hh:[d,x("hours")],D:[d,x("day")],DD:[i,x("day")],Do:[A,function(V){var L=g.ordinal,v=V.match(/\d+/);if(this.day=v[0],L)for(var _=1;_<=31;_+=1)L(_).replace(/\[|\]/g,"")===V&&(this.day=_)}],M:[d,x("month")],MM:[i,x("month")],MMM:[A,function(V){var L=k("months"),v=(k("monthsShort")||L.map(function(_){return _.slice(0,3)})).indexOf(V)+1;if(v<1)throw new Error;this.month=v%12||v}],MMMM:[A,function(V){var L=k("months").indexOf(V)+1;if(L<1)throw new Error;this.month=L%12||L}],Y:[/[+-]?\d+/,x("year")],YY:[i,function(V){this.year=E(V)}],YYYY:[/\d{4}/,x("year")],Z:j,ZZ:j};function K(V){var L,v;L=V,v=g&&g.formats;for(var _=(V=L.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,function(R,b,Q){var X=Q&&Q.toUpperCase();return b||v[Q]||a[Q]||v[X].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(M,h,m){return h||m.slice(1)})})).match(e),D=_.length,S=0;S<D;S+=1){var $=_[S],n=N[$],r=n&&n[0],B=n&&n[1];_[S]=B?{regex:r,parser:B}:$.replace(/^\[|\]$/g,"")}return function(R){for(var b={},Q=0,X=0;Q<D;Q+=1){var M=_[Q];if(typeof M=="string")X+=M.length;else{var h=M.regex,m=M.parser,P=R.slice(X),w=h.exec(P)[0];m.call(b,w),R=R.replace(w,"")}}return function(Y){var I=Y.afternoon;if(I!==void 0){var W=Y.hours;I?W<12&&(Y.hours+=12):W===12&&(Y.hours=0),delete Y.afternoon}}(b),b}}return function(V,L,v){v.p.customParseFormat=!0,V&&V.parseTwoDigitYear&&(E=V.parseTwoDigitYear);var _=L.prototype,D=_.parse;_.parse=function(S){var $=S.date,n=S.utc,r=S.args;this.$u=n;var B=r[1];if(typeof B=="string"){var R=r[2]===!0,b=r[3]===!0,Q=R||b,X=r[2];b&&(X=r[2]),g=this.$locale(),!R&&X&&(g=v.Ls[X]),this.$d=function(P,w,Y){try{if(["x","X"].indexOf(w)>-1)return new Date((w==="X"?1e3:1)*P);var I=K(w)(P),W=I.year,Z=I.month,q=I.day,ae=I.hours,me=I.minutes,ve=I.seconds,ge=I.milliseconds,ye=I.zone,se=new Date,ne=q||(W||Z?1:se.getDate()),ue=W||se.getFullYear(),_e=0;W&&!Z||(_e=Z>0?Z-1:se.getMonth());var be=ae||0,$e=me||0,Me=ve||0,Ie=ge||0;return ye?new Date(Date.UTC(ue,_e,ne,be,$e,Me,Ie+60*ye.offset*1e3)):Y?new Date(Date.UTC(ue,_e,ne,be,$e,Me,Ie)):new Date(ue,_e,ne,be,$e,Me,Ie)}catch{return new Date("")}}($,B,n),this.init(),X&&X!==!0&&(this.$L=this.locale(X).$L),Q&&$!=this.format(B)&&(this.$d=new Date("")),g={}}else if(B instanceof Array)for(var M=B.length,h=1;h<=M;h+=1){r[1]=B[h-1];var m=v.apply(this,r);if(m.isValid()){this.$d=m.$d,this.$L=m.$L,this.init();break}h===M&&(this.$d=new Date(""))}else D.call(this,S)}}})})(xt);const $r=xt.exports;var kt={exports:{}};(function(t,l){(function(a,e){t.exports=e()})(je,function(){return function(a,e,i){e.prototype.isToday=function(){var d="YYYY-MM-DD",A=i();return this.format(d)===A.format(d)}}})})(kt);const Mr=kt.exports;var wt={exports:{}};(function(t,l){(function(a,e){t.exports=e()})(je,function(){return function(a,e,i){e.prototype.isBetween=function(d,A,g,E){var x=i(d),j=i(A),k=(E=E||"()")[0]==="(",O=E[1]===")";return(k?this.isAfter(x,g):!this.isBefore(x,g))&&(O?this.isBefore(j,g):!this.isAfter(j,g))||(k?this.isBefore(x,g):!this.isAfter(x,g))&&(O?this.isAfter(j,g):!this.isBefore(j,g))}}})})(wt);const Ir=wt.exports;var Et={exports:{}};(function(t,l){(function(a,e){t.exports=e()})(je,function(){var a,e,i=1e3,d=6e4,A=36e5,g=864e5,E=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,x=31536e6,j=2592e6,k=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/,O={years:x,months:j,days:g,hours:A,minutes:d,seconds:i,milliseconds:1,weeks:6048e5},N=function($){return $ instanceof S},K=function($,n,r){return new S($,r,n.$l)},V=function($){return e.p($)+"s"},L=function($){return $<0},v=function($){return L($)?Math.ceil($):Math.floor($)},_=function($){return Math.abs($)},D=function($,n){return $?L($)?{negative:!0,format:""+_($)+n}:{negative:!1,format:""+$+n}:{negative:!1,format:""}},S=function(){function $(r,B,R){var b=this;if(this.$d={},this.$l=R,r===void 0&&(this.$ms=0,this.parseFromMilliseconds()),B)return K(r*O[V(B)],this);if(typeof r=="number")return this.$ms=r,this.parseFromMilliseconds(),this;if(typeof r=="object")return Object.keys(r).forEach(function(M){b.$d[V(M)]=r[M]}),this.calMilliseconds(),this;if(typeof r=="string"){var Q=r.match(k);if(Q){var X=Q.slice(2).map(function(M){return M!=null?Number(M):0});return this.$d.years=X[0],this.$d.months=X[1],this.$d.weeks=X[2],this.$d.days=X[3],this.$d.hours=X[4],this.$d.minutes=X[5],this.$d.seconds=X[6],this.calMilliseconds(),this}}return this}var n=$.prototype;return n.calMilliseconds=function(){var r=this;this.$ms=Object.keys(this.$d).reduce(function(B,R){return B+(r.$d[R]||0)*O[R]},0)},n.parseFromMilliseconds=function(){var r=this.$ms;this.$d.years=v(r/x),r%=x,this.$d.months=v(r/j),r%=j,this.$d.days=v(r/g),r%=g,this.$d.hours=v(r/A),r%=A,this.$d.minutes=v(r/d),r%=d,this.$d.seconds=v(r/i),r%=i,this.$d.milliseconds=r},n.toISOString=function(){var r=D(this.$d.years,"Y"),B=D(this.$d.months,"M"),R=+this.$d.days||0;this.$d.weeks&&(R+=7*this.$d.weeks);var b=D(R,"D"),Q=D(this.$d.hours,"H"),X=D(this.$d.minutes,"M"),M=this.$d.seconds||0;this.$d.milliseconds&&(M+=this.$d.milliseconds/1e3);var h=D(M,"S"),m=r.negative||B.negative||b.negative||Q.negative||X.negative||h.negative,P=Q.format||X.format||h.format?"T":"",w=(m?"-":"")+"P"+r.format+B.format+b.format+P+Q.format+X.format+h.format;return w==="P"||w==="-P"?"P0D":w},n.toJSON=function(){return this.toISOString()},n.format=function(r){var B=r||"YYYY-MM-DDTHH:mm:ss",R={Y:this.$d.years,YY:e.s(this.$d.years,2,"0"),YYYY:e.s(this.$d.years,4,"0"),M:this.$d.months,MM:e.s(this.$d.months,2,"0"),D:this.$d.days,DD:e.s(this.$d.days,2,"0"),H:this.$d.hours,HH:e.s(this.$d.hours,2,"0"),m:this.$d.minutes,mm:e.s(this.$d.minutes,2,"0"),s:this.$d.seconds,ss:e.s(this.$d.seconds,2,"0"),SSS:e.s(this.$d.milliseconds,3,"0")};return B.replace(E,function(b,Q){return Q||String(R[b])})},n.as=function(r){return this.$ms/O[V(r)]},n.get=function(r){var B=this.$ms,R=V(r);return R==="milliseconds"?B%=1e3:B=R==="weeks"?v(B/O[R]):this.$d[R],B===0?0:B},n.add=function(r,B,R){var b;return b=B?r*O[V(B)]:N(r)?r.$ms:K(r,this).$ms,K(this.$ms+b*(R?-1:1),this)},n.subtract=function(r,B){return this.add(r,B,!0)},n.locale=function(r){var B=this.clone();return B.$l=r,B},n.clone=function(){return K(this.$ms,this)},n.humanize=function(r){return a().add(this.$ms,"ms").locale(this.$l).fromNow(!r)},n.milliseconds=function(){return this.get("milliseconds")},n.asMilliseconds=function(){return this.as("milliseconds")},n.seconds=function(){return this.get("seconds")},n.asSeconds=function(){return this.as("seconds")},n.minutes=function(){return this.get("minutes")},n.asMinutes=function(){return this.as("minutes")},n.hours=function(){return this.get("hours")},n.asHours=function(){return this.as("hours")},n.days=function(){return this.get("days")},n.asDays=function(){return this.as("days")},n.weeks=function(){return this.get("weeks")},n.asWeeks=function(){return this.as("weeks")},n.months=function(){return this.get("months")},n.asMonths=function(){return this.as("months")},n.years=function(){return this.get("years")},n.asYears=function(){return this.as("years")},$}();return function($,n,r){a=r,e=r().$utils(),r.duration=function(b,Q){var X=r.locale();return K(b,{$l:X},Q)},r.isDuration=N;var B=n.prototype.add,R=n.prototype.subtract;n.prototype.add=function(b,Q){return N(b)&&(b=b.asMilliseconds()),B.bind(this)(b,Q)},n.prototype.subtract=function(b,Q){return N(b)&&(b=b.asMilliseconds()),R.bind(this)(b,Q)}}})})(Et);const Rr=Et.exports;function Yr(){const t=l=>{const a=[],e=l.localeData().firstDayOfWeek();for(let i=0;i<=l.date(0-e).day();i++)a.push(l.date(0).subtract(i,"day"));return a.sort((i,d)=>i.date()-d.date())};return{usePreviousDate:t,useCurrentDate:l=>Array.from({length:l.daysInMonth()},(a,e)=>l.date(e+1)),useNextDate:l=>{const a=[];for(let e=1;e<=42-(t(l).length+l.daysInMonth());e++)a.push(l.date(e).month(l.month()).add(1,"month"));return a},useDisableDate:(l,{disableDate:a})=>typeof a=="function"?a(l.toDate()):!1,useBetweenRange:(l,{previous:a,next:e})=>{let i;return a.isAfter(e,"date")?i="(]":i="[)",!!(l.isBetween(a,e,"date",i)&&!l.off)},useToValueFromString:(l,{formatter:a})=>l.format(a.date),useToValueFromArray:({previous:l,next:a},{formatter:e,separator:i})=>`${l.format(e.date)}${i}${a.format(e.date)}`}}function Cr(){return{useVisibleViewport:t=>{if(t){const{right:l}=t.getBoundingClientRect(),a=window.innerWidth||document.documentElement.clientWidth;return l>a}else return null}}}const Br=["disabled","placeholder"],Fr={class:"absolute inset-y-0 right-0 inline-flex items-center rounded-md overflow-hidden"},Nr=["disabled"],Hr={class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},Ur={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M6 18L18 6M6 6l12 12"},zr={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"},Wr={class:"flex flex-wrap lg:flex-nowrap"},Gr={class:"relative flex flex-wrap sm:flex-nowrap p-1 w-full"},Qr={key:0,class:"hidden h-full absolute inset-0 sm:flex justify-center items-center"},Zr=T("div",{class:"h-full border-r border-black/[.1] dark:border-vtd-secondary-700/[1]"},null,-1),Kr=[Zr],Jr={class:"px-0.5 sm:px-2"},Xr={key:1,class:"relative w-full md:w-1/2 lg:w-80 overflow-hidden mt-3 sm:mt-0 sm:ml-2"},qr={class:"px-0.5 sm:px-2"},eo={key:0},to={class:"mt-2 mx-2 py-1.5 border-t border-black/[.1] dark:border-vtd-secondary-700/[1]"},ro={class:"mt-1.5 sm:flex sm:flex-row-reverse"},oo=["disabled","onClick","textContent"],ao=["onClick","textContent"],no={key:1,class:"sm:hidden"},so={class:"mt-2 mx-2 py-1.5 border-t border-black/[.1] dark:border-vtd-secondary-700/[1]"},lo={class:"mt-1.5 sm:flex sm:flex-row-reverse"},io=["onClick","textContent"],uo={key:1,class:"flex"},co={class:"bg-white rounded-lg shadow-sm border border-black/[.1] px-3 py-3 sm:px-4 sm:py-4 dark:bg-vtd-secondary-800 dark:border-vtd-secondary-700/[1]"},vo={class:"flex flex-wrap lg:flex-nowrap"},po={class:"relative flex flex-wrap sm:flex-nowrap p-1 w-full"},fo={key:0,class:"hidden h-full absolute inset-0 sm:flex justify-center items-center"},mo=T("div",{class:"h-full border-r border-black/[.1] dark:border-vtd-secondary-700/[1]"},null,-1),ho=[mo],yo={class:"px-0.5 sm:px-2"},_o={key:1,class:"relative w-full md:w-1/2 lg:w-80 overflow-hidden mt-3 sm:mt-0 sm:ml-2"},bo={class:"px-0.5 sm:px-2"},go={key:0},xo={class:"mt-2 mx-2 py-1.5 border-t border-black/[.1] dark:border-vtd-secondary-700/[1]"},ko={class:"mt-1.5 sm:flex sm:flex-row-reverse"},wo=["disabled","textContent"],Eo={__name:"VueTailwindDatePicker",props:{noInput:Boolean,overlay:Boolean,asSingle:Boolean,useRange:Boolean,placeholder:{type:[Boolean,String],default:!1},i18n:{type:String,default:"en"},inputClasses:{type:String,default:""},disabled:{type:Boolean,default:!1},disableInRange:{type:Boolean,default:!0},disableDate:{type:[Boolean,Array,Function],default:!1},autoApply:{type:Boolean,default:!0},shortcuts:{type:[Boolean,Function],default:!0},separator:{type:String,default:" ~ "},formatter:{type:Object,default:()=>({date:"YYYY-MM-DD HH:mm:ss",month:"MMM"})},modelValue:{type:[Array,Object,String],default:()=>[]},startFrom:{type:[Object,String],default:()=>new Date},weekdaysSize:{type:String,default:"short"},options:{type:Object,default:()=>({shortcuts:{today:"Today",yesterday:"Yesterday",past:t=>`Last ${t} Days`,currentMonth:"This Month",pastMonth:"Last Month"},footer:{apply:"Apply",cancel:"Cancel"}})}},emits:["update:modelValue","select:month","select:year","select:right:month","select:right:year","click:prev","click:next","click:right:prev","click:right:next"],setup(t,{expose:l,emit:a}){const e=t,{useCurrentDate:i,useDisableDate:d,useBetweenRange:A,useNextDate:g,usePreviousDate:E,useToValueFromArray:x,useToValueFromString:j}=Yr(),{useVisibleViewport:k}=Cr();u.extend(Tr),u.extend(Sr),u.extend($r),u.extend(Mr),u.extend(Ir),u.extend(Rr);const O=oe(null),N=oe(null),K=oe(null),V=oe(""),L=oe(null),v=oe(""),_=oe([]),D=oe([]),S=oe(null),$=oe(null),n=Ot({previous:{calendar:!0,month:!1,year:!1},next:{calendar:!0,month:!1,year:!1}}),r=oe({previous:u(),next:u().add(1,"month"),year:{previous:u().year(),next:u().year()},weeks:e.weekdaysSize==="min"?u.weekdaysMin():u.weekdaysShort(),months:e.formatter.month==="MMM"?u.monthsShort():u.months()}),B=fe(()=>r.value.weeks),R=fe(()=>r.value.months),b=fe(()=>{const{previous:c,next:p,year:y}=F(r);return{previous:{date:()=>E(c).concat(i(c)).concat(g(c)).map(o=>(o.today=o.isToday(),o.active=c.month()===o.month(),o.off=c.month()!==o.month(),o.sunday=o.day()===0,o.disabled=d(o,e)&&!w(o),o.inRange=()=>{if(e.asSingle&&!e.useRange)return c.month()!==o.month()},o.hovered=()=>P()&&_.value.length>1?(o.isBetween(_.value[0],_.value[1],"date","()")||o.isBetween(_.value[1],_.value[0],"date","(]"))&&c.month()===o.month():!1,o.duration=()=>!1,o)),month:c&&c.format(e.formatter.month),year:c&&c.year(),years:()=>Array.from({length:12},(o,f)=>y.previous+f),onPrevious:()=>{r.value.previous=c.subtract(1,"month"),a("click:prev",r.value.previous)},onNext:()=>{r.value.previous=c.add(1,"month"),c.diff(p,"month")===-1&&(r.value.next=p.add(1,"month")),a("click:next",r.value.previous)},onPreviousYear:()=>{r.value.year.previous=r.value.year.previous-12},onNextYear:()=>{r.value.year.previous=r.value.year.previous+12},openMonth:()=>{n.previous.month=!n.previous.month,n.previous.year=!1,n.previous.calendar=!n.previous.month},setMount:o=>{r.value.previous=c.month(o),n.previous.month=!n.previous.month,n.previous.year=!1,n.previous.calendar=!n.previous.month,a("select:month",r.value.previous),Re(()=>{(r.value.next.isSame(r.value.previous,"month")||r.value.next.isBefore(r.value.previous))&&(r.value.next=r.value.previous.add(1,"month")),r.value.year.next=r.value.next.year()})},openYear:()=>{n.previous.year=!n.previous.year,n.previous.month=!1,n.previous.calendar=!n.previous.year},setYear:(o,f)=>{f||(r.value.previous=c.year(o),n.previous.year=!n.previous.year,n.previous.calendar=!n.previous.year,a("select:year",r.value.previous),Re(()=>{(r.value.next.isSame(r.value.previous,"month")||r.value.next.isBefore(r.value.previous))&&(r.value.next=r.value.previous.add(1,"month")),r.value.year.previous=r.value.previous.year(),r.value.year.next=r.value.next.year()}))}},next:{date:()=>E(p).concat(i(p)).concat(g(p)).map(o=>(o.today=o.isToday(),o.active=p.month()===o.month(),o.off=p.month()!==o.month(),o.sunday=o.day()===0,o.disabled=d(o,e)&&!w(o),o.inRange=()=>{if(e.asSingle&&!e.useRange)return p.month()!==o.month()},o.hovered=()=>_.value.length>1?(o.isBetween(_.value[0],_.value[1],"date","()")||o.isBetween(_.value[1],_.value[0],"date","(]"))&&p.month()===o.month():!1,o.duration=()=>!1,o)),month:p&&p.format(e.formatter.month),year:p&&p.year(),years:()=>Array.from({length:12},(o,f)=>y.next+f),onPrevious:()=>{r.value.next=p.subtract(1,"month"),p.diff(c,"month")===1&&(r.value.previous=c.subtract(1,"month")),a("click:right:prev",r.value.next)},onNext:()=>{r.value.next=p.add(1,"month"),a("click:right:next",r.value.next)},onPreviousYear:()=>{r.value.year.next=r.value.year.next-12},onNextYear:()=>{r.value.year.next=r.value.year.next+12},openMonth:()=>{n.next.month=!n.next.month,n.next.year=!1,n.next.calendar=!n.next.month},setMount:o=>{r.value.next=p.month(o),n.next.month=!n.next.month,n.next.year=!1,n.next.calendar=!n.next.month,a("select:right:month",r.value.next),Re(()=>{(r.value.previous.isSame(r.value.next,"month")||r.value.previous.isAfter(r.value.next))&&(r.value.previous=r.value.next.subtract(1,"month")),r.value.year.previous=r.value.previous.year()})},openYear:()=>{n.next.year=!n.next.year,n.next.month=!1,n.next.calendar=!n.next.year},setYear:(o,f)=>{f&&(r.value.next=p.year(o),n.next.year=!n.next.year,n.next.month=!1,n.next.calendar=!n.next.year,a("select:right:year",r.value.next),Re(()=>{(r.value.previous.isSame(r.value.next,"month")||r.value.previous.isAfter(r.value.next))&&(r.value.previous=r.value.next.subtract(1,"month")),r.value.year.previous=r.value.previous.year(),r.value.year.next=r.value.next.year()}))}}}}),Q=oe(!1);setTimeout(()=>{Q.value=!0},250);const X=()=>u().localeData().firstDayOfWeek(),M=c=>{const p=[...c],y=p.shift();return[...p,y]},h=()=>Array.isArray(e.modelValue),m=()=>typeof e.modelValue=="object",P=()=>!e.useRange&&!e.asSingle?!0:!e.useRange&&e.asSingle?!1:e.useRange&&!e.asSingle?!0:!!(e.useRange&&e.asSingle),w=c=>{if(e.disableInRange||v.value==="")return!1;let p,y;if(h()){const[o,f]=e.modelValue;p=o,y=f}else if(m()){if(e.modelValue){const[o,f]=Object.values(e.modelValue);p=o,y=f}}else{const[o,f]=e.modelValue.split(e.separator);p=o,y=f}return c.isBetween(u(p,e.formatter.date,!0),u(y,e.formatter.date,!0),"date","[]")},Y=()=>{S.value=null,$.value=null,_.value=[],L.value=null},I=()=>{if(v.value="",h())a("update:modelValue",[]);else if(m()){const c={},[p,y]=Object.keys(e.modelValue);c[p]="",c[y]="",a("update:modelValue",c)}else a("update:modelValue","");D.value=[],N.value&&N.value.focus()};l({clearPicker:I});const W=()=>{if(P()){const[c,p]=v.value.split(e.separator),[y,o]=[u(c,e.formatter.date,!0),u(p,e.formatter.date,!0)];if(y.isValid()&&o.isValid())if(Z(y),Z(o),h())a("update:modelValue",[c,p]);else if(m()){const f={},[C,H]=Object.keys(e.modelValue);f[C]=c,f[H]=p,a("update:modelValue",f)}else a("update:modelValue",x({previous:y,next:o},e))}else{const c=u(v.value,e.formatter.date,!0);if(c.isValid())if(Z(c),h())a("update:modelValue",[v.value]);else if(m()){const p={},[y]=Object.keys(e.modelValue);p[y]=v.value,a("update:modelValue",p)}else a("update:modelValue",v.value)}},Z=(c,p,y)=>{if(P())if(S.value)if($.value=c,e.autoApply){c.isBefore(S.value)?v.value=x({previous:c,next:S.value},e):v.value=x({previous:S.value,next:c},e);const[o,f]=v.value.split(e.separator);if(h())a("update:modelValue",[u(o,e.formatter.date,!0).format(e.formatter.date),u(f,e.formatter.date,!0).format(e.formatter.date)]);else if(m()){const C={},[H,te]=Object.keys(e.modelValue);C[H]=o,C[te]=f,a("update:modelValue",C)}else a("update:modelValue",x({previous:u(o,e.formatter.date,!0),next:u(f,e.formatter.date,!0)},e));y&&y(),D.value=[],u(o,e.formatter.date,!0).isSame(u(f,e.formatter.date,!0),"month")||(r.value.previous=u(o,e.formatter.date,!0),r.value.next=u(f,e.formatter.date,!0)),Y()}else{S.value.isAfter(c,"month")?D.value=[c,S.value]:D.value=[S.value,c];const[o,f]=D.value;o.isSame(f,"month")||(r.value.previous=o,r.value.next=f),Y()}else D.value=[],S.value=c,L.value=c,_.value.push(c),D.value.push(c),p?(r.value.next=c,r.value.previous.isSame(c,"month")&&(r.value.next=c.add(1,"month"))):(r.value.previous=c,r.value.next.isSame(c,"month")&&(r.value.previous=r.value.next,r.value.next=c.add(1,"month")));else if(e.autoApply){if(v.value=j(c,e),h())a("update:modelValue",[v.value]);else if(m()){const o={},[f]=Object.keys(e.modelValue);o[f]=v.value,a("update:modelValue",o)}else a("update:modelValue",v.value);y&&y(),D.value=[],Y()}else D.value=[c],Y()},q=c=>{if(D.value.length<1)return!1;let p;if(P()){const[y,o]=D.value;o.isBefore(y)?p=x({previous:o,next:y},e):p=x({previous:y,next:o},e)}else{const[y]=D.value;p=y}if(P()){const[y,o]=p.split(e.separator);if(h())a("update:modelValue",[u(y,e.formatter.date,!0).format(e.formatter.date),u(o,e.formatter.date,!0).format(e.formatter.date)]);else if(m()){const f={},[C,H]=Object.keys(e.modelValue);f[C]=y,f[H]=o,a("update:modelValue",f)}else a("update:modelValue",x({previous:u(y,e.formatter.date,!0),next:u(o,e.formatter.date,!0)},e));v.value=p}else if(v.value=p.format(e.formatter.date),h())a("update:modelValue",[v.value]);else if(m()){const y={},[o]=Object.keys(e.modelValue);y[o]=v.value,a("update:modelValue",y)}else a("update:modelValue",v.value);c&&c()},ae=c=>{if(!P())return!1;if(S.value)_.value=[S.value,c];else return _.value=[],!1},me=c=>{if(S.value&&e.autoApply)return!1;let p,y;if(_.value.length>1){const[o,f]=_.value;p=u(o,e.formatter.date,!0),y=u(f,e.formatter.date,!0)}else if(h())if(e.autoApply){const[o,f]=e.modelValue;p=o&&u(o,e.formatter.date,!0),y=f&&u(f,e.formatter.date,!0)}else{const[o,f]=D.value;p=u(o,e.formatter.date,!0),y=u(f,e.formatter.date,!0)}else if(m())if(e.autoApply){if(e.modelValue){const[o,f]=Object.values(e.modelValue);p=o&&u(o,e.formatter.date,!0),y=f&&u(f,e.formatter.date,!0)}}else{const[o,f]=D.value;p=u(o,e.formatter.date,!0),y=u(f,e.formatter.date,!0)}else if(e.autoApply){const[o,f]=e.modelValue?e.modelValue.split(e.separator):[!1,!1];p=o&&u(o,e.formatter.date,!0),y=f&&u(f,e.formatter.date,!0)}else{const[o,f]=D.value;p=u(o,e.formatter.date,!0),y=u(f,e.formatter.date,!0)}return p&&y?A(c,{previous:p,next:y}):!1},ve=c=>{const{today:p,active:y,off:o,disabled:f}=c;let C,H,te;if(P())if(h())if(L.value){const[U,ee]=_.value;H=U&&u(U,e.formatter.date,!0),te=ee&&u(ee,e.formatter.date,!0)}else if(e.autoApply){const[U,ee]=e.modelValue;H=U&&u(U,e.formatter.date,!0),te=ee&&u(ee,e.formatter.date,!0)}else{const[U,ee]=D.value;H=U&&u(U,e.formatter.date,!0),te=ee&&u(ee,e.formatter.date,!0)}else if(m())if(L.value){const[U,ee]=_.value;H=U&&u(U,e.formatter.date,!0),te=ee&&u(ee,e.formatter.date,!0)}else if(e.autoApply){const[U,ee]=e.modelValue?Object.values(e.modelValue):[!1,!1];H=U&&u(U,e.formatter.date,!0),te=ee&&u(ee,e.formatter.date,!0)}else{const[U,ee]=D.value;H=U&&u(U,e.formatter.date,!0),te=ee&&u(ee,e.formatter.date,!0)}else if(L.value){const[U,ee]=_.value;H=U&&u(U,e.formatter.date,!0),te=ee&&u(ee,e.formatter.date,!0)}else if(e.autoApply){const[U,ee]=e.modelValue?e.modelValue.split(e.separator):[!1,!1];H=U&&u(U,e.formatter.date,!0),te=ee&&u(ee,e.formatter.date,!0)}else{const[U,ee]=D.value;H=U&&u(U,e.formatter.date,!0),te=ee&&u(ee,e.formatter.date,!0)}else if(h())if(e.autoApply){if(e.modelValue.length>0){const[U]=e.modelValue;H=u(U,e.formatter.date,!0)}}else{const[U]=D.value;H=U&&u(U,e.formatter.date,!0)}else if(m())if(e.autoApply){if(e.modelValue){const[U]=Object.values(e.modelValue);H=u(U,e.formatter.date,!0)}}else{const[U]=D.value;H=U&&u(U,e.formatter.date,!0)}else if(e.autoApply){if(e.modelValue){const[U]=e.modelValue.split(e.separator);H=u(U,e.formatter.date,!0)}}else{const[U]=D.value;H=U&&u(U,e.formatter.date,!0)}return y&&(C=p?"text-vtd-primary-500 font-semibold dark:text-vtd-primary-400 rounded-full focus:bg-vtd-primary-50 focus:text-vtd-secondary-900 focus:border-vtd-primary-300 focus:ring focus:ring-vtd-primary-500 focus:ring-opacity-10 focus:outline-none dark:bg-vtd-secondary-800 dark:text-vtd-secondary-300 dark:hover:bg-vtd-secondary-700 dark:hover:text-vtd-secondary-300 dark:focus:bg-vtd-secondary-600 dark:focus:text-vtd-secondary-100 dark:focus:border-vtd-primary-500 dark:focus:ring-opacity-25 dark:focus:bg-opacity-50":f?"text-vtd-secondary-600 font-normal disabled:text-vtd-secondary-500 disabled:cursor-not-allowed rounded-full":c.isBetween(H,te,"date","()")?"text-vtd-secondary-700 font-medium dark:text-vtd-secondary-100 rounded-full":"text-vtd-secondary-600 font-medium dark:text-vtd-secondary-200 rounded-full"),o&&(C="text-vtd-secondary-400 font-light disabled:cursor-not-allowed"),H&&te&&!o?(c.isSame(H,"date")&&(C=te.isAfter(H,"date")?"bg-vtd-primary-500 text-white font-bold rounded-l-full disabled:cursor-not-allowed":"bg-vtd-primary-500 text-white font-bold rounded-r-full disabled:cursor-not-allowed",H.isSame(te,"date")&&(C="bg-vtd-primary-500 text-white font-bold rounded-full disabled:cursor-not-allowed")),c.isSame(te,"date")&&(C=te.isAfter(H,"date")?"bg-vtd-primary-500 text-white font-bold rounded-r-full disabled:cursor-not-allowed":"bg-vtd-primary-500 text-white font-bold rounded-l-full disabled:cursor-not-allowed",H.isSame(te,"date")&&(C="bg-vtd-primary-500 text-white font-bold rounded-full disabled:cursor-not-allowed"))):H&&c.isSame(H,"date")&&!o&&(C="bg-vtd-primary-500 text-white font-bold rounded-full disabled:cursor-not-allowed"),C},ge=c=>{let p,y,o;if(p="",!P())return p;if(h())if(_.value.length>1){const[f,C]=_.value;y=f&&u(f,e.formatter.date,!0),o=C&&u(C,e.formatter.date,!0)}else if(e.autoApply){const[f,C]=e.modelValue;y=f&&u(f,e.formatter.date,!0),o=C&&u(C,e.formatter.date,!0)}else{const[f,C]=D.value;y=f&&u(f,e.formatter.date,!0),o=C&&u(C,e.formatter.date,!0)}else if(m())if(_.value.length>1){const[f,C]=_.value;y=f&&u(f,e.formatter.date,!0),o=C&&u(C,e.formatter.date,!0)}else if(e.autoApply){if(e.modelValue){const[f,C]=Object.values(e.modelValue);y=f&&u(f,e.formatter.date,!0),o=C&&u(C,e.formatter.date,!0)}}else{const[f,C]=D.value;y=f&&u(f,e.formatter.date,!0),o=C&&u(C,e.formatter.date,!0)}else if(_.value.length>1){const[f,C]=_.value;y=f&&u(f,e.formatter.date,!0),o=C&&u(C,e.formatter.date,!0)}else if(e.autoApply){const[f,C]=e.modelValue?e.modelValue.split(e.separator):[!1,!1];y=f&&u(f,e.formatter.date,!0),o=C&&u(C,e.formatter.date,!0)}else{const[f,C]=D.value;y=f&&u(f,e.formatter.date,!0),o=C&&u(C,e.formatter.date,!0)}return y&&o&&(c.isSame(y,"date")?(o.isBefore(y)&&(p+=" rounded-r-full inset-0"),y.isBefore(o)&&(p+=" rounded-l-full inset-0")):c.isSame(o,"date")?(o.isBefore(y)&&(p+=" rounded-l-full inset-0"),y.isBefore(o)&&(p+=" rounded-r-full inset-0")):p+=" inset-0"),p},ye=(c,p)=>{r.value.previous=u(c,e.formatter.date,!0),r.value.next=u(p,e.formatter.date,!0),(u.duration(r.value.next.diff(r.value.previous)).$d.months===2||u.duration(r.value.next.diff(r.value.previous)).$d.months===1&&u.duration(r.value.next.diff(r.value.previous)).$d.days===7)&&(r.value.next=r.value.next.subtract(1,"month")),(r.value.next.isSame(r.value.previous,"month")||r.value.next.isBefore(r.value.previous))&&(r.value.next=r.value.previous.add(1,"month"))},se=(c,p)=>{if(P())if(e.autoApply){if(h())a("update:modelValue",[c,p]);else if(m()){const y={},[o,f]=Object.keys(e.modelValue);y[o]=c,y[f]=p,a("update:modelValue",y)}else a("update:modelValue",x({previous:u(c,e.formatter.date,!0),next:u(p,e.formatter.date,!0)},e));v.value=`${c}${e.separator}${p}`}else D.value=[u(c,e.formatter.date,!0),u(p,e.formatter.date,!0)];else if(e.autoApply){if(h())a("update:modelValue",[c]);else if(m()){const y={},[o]=Object.keys(e.modelValue);y[o]=c,a("update:modelValue",y)}else a("update:modelValue",c);v.value=c}else D.value=[u(c,e.formatter.date,!0),u(p,e.formatter.date,!0)];ye(c,p)},ne=c=>{const p=u().format(e.formatter.date),y=u().format(e.formatter.date);se(p,y),c&&c()},ue=c=>{const p=u().subtract(1,"day").format(e.formatter.date),y=u().subtract(1,"day").format(e.formatter.date);se(p,y),c&&c()},_e=(c,p)=>{const y=u().subtract(c-1,"day").format(e.formatter.date),o=u().format(e.formatter.date);se(y,o),p&&p()},be=c=>{const p=u().date(1).format(e.formatter.date),y=u().date(u().daysInMonth()).format(e.formatter.date);se(p,y),c&&c()},$e=c=>{const p=u().date(1).subtract(1,"month").format(e.formatter.date),y=u().date(0).format(e.formatter.date);se(p,y),c&&c()},Me=(c,p)=>{let y,o;const[f,C]=c.atClick();y=u(f).format(e.formatter.date),o=u(C).format(e.formatter.date),se(y,o),p&&p()};Pt(()=>D.value,c=>{c.length>0&&(n.previous.calendar=!0,n.previous.month=!1,n.previous.year=!1,n.next.calendar=!0,n.next.month=!1,n.next.year=!1)}),xe(()=>{e.placeholder?V.value=e.placeholder:P()?V.value=`${e.formatter.date}${e.separator}${e.formatter.date}`:V.value=e.formatter.date}),xe(()=>{const c=e.i18n;Re(()=>{const p=Object.assign({"./locale/af.js":()=>s(()=>import("./af.78b9c933-0a4acccb.js"),["assets/af.78b9c933-0a4acccb.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/am.js":()=>s(()=>import("./am.1d8f1477-beb2c256.js"),["assets/am.1d8f1477-beb2c256.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ar-dz.js":()=>s(()=>import("./ar-dz.42346512-32fb0fb5.js"),["assets/ar-dz.42346512-32fb0fb5.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ar-iq.js":()=>s(()=>import("./ar-iq.457ddb59-2ac7b07c.js"),["assets/ar-iq.457ddb59-2ac7b07c.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ar-kw.js":()=>s(()=>import("./ar-kw.1cf9cf9c-fc8da3ff.js"),["assets/ar-kw.1cf9cf9c-fc8da3ff.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ar-ly.js":()=>s(()=>import("./ar-ly.3d4ab74a-e25576c6.js"),["assets/ar-ly.3d4ab74a-e25576c6.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ar-ma.js":()=>s(()=>import("./ar-ma.2649ce4c-aca4eb3c.js"),["assets/ar-ma.2649ce4c-aca4eb3c.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ar-sa.js":()=>s(()=>import("./ar-sa.3924e169-8cca09cb.js"),["assets/ar-sa.3924e169-8cca09cb.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ar-tn.js":()=>s(()=>import("./ar-tn.6d7d9860-ae764488.js"),["assets/ar-tn.6d7d9860-ae764488.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ar.js":()=>s(()=>import("./ar.a8232bae-f9675e29.js"),["assets/ar.a8232bae-f9675e29.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/az.js":()=>s(()=>import("./az.7d7f4a08-51af771c.js"),["assets/az.7d7f4a08-51af771c.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/be.js":()=>s(()=>import("./be.36888435-b27ddb7d.js"),["assets/be.36888435-b27ddb7d.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/bg.js":()=>s(()=>import("./bg.9f42555f-57ed9a7f.js"),["assets/bg.9f42555f-57ed9a7f.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/bi.js":()=>s(()=>import("./bi.eb4985db-c9969b89.js"),["assets/bi.eb4985db-c9969b89.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/bm.js":()=>s(()=>import("./bm.fc6c02b0-65c2bc39.js"),["assets/bm.fc6c02b0-65c2bc39.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/bn-bd.js":()=>s(()=>import("./bn-bd.54903b23-e4660f9b.js"),["assets/bn-bd.54903b23-e4660f9b.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/bn.js":()=>s(()=>import("./bn.5ccaeabb-40ad4965.js"),["assets/bn.5ccaeabb-40ad4965.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/bo.js":()=>s(()=>import("./bo.f62543f4-6a765607.js"),["assets/bo.f62543f4-6a765607.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/br.js":()=>s(()=>import("./br.d8150366-48ec074a.js"),["assets/br.d8150366-48ec074a.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/bs.js":()=>s(()=>import("./bs.36836819-45d8ce5a.js"),["assets/bs.36836819-45d8ce5a.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ca.js":()=>s(()=>import("./ca.072fd974-c01eff58.js"),["assets/ca.072fd974-c01eff58.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/cs.js":()=>s(()=>import("./cs.c487664b-12c14fc0.js"),["assets/cs.c487664b-12c14fc0.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/cv.js":()=>s(()=>import("./cv.dd61033c-5cbf8124.js"),["assets/cv.dd61033c-5cbf8124.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/cy.js":()=>s(()=>import("./cy.961b82f0-a3d695f7.js"),["assets/cy.961b82f0-a3d695f7.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/da.js":()=>s(()=>import("./da.5f65840b-762e1dad.js"),["assets/da.5f65840b-762e1dad.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/de-at.js":()=>s(()=>import("./de-at.d2039119-a27303f1.js"),["assets/de-at.d2039119-a27303f1.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/de-ch.js":()=>s(()=>import("./de-ch.c2c590f0-a22a868e.js"),["assets/de-ch.c2c590f0-a22a868e.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/de.js":()=>s(()=>import("./de.e59fd8ef-c7ce9601.js"),["assets/de.e59fd8ef-c7ce9601.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/dv.js":()=>s(()=>import("./dv.7bde5145-a7c8ce20.js"),["assets/dv.7bde5145-a7c8ce20.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/el.js":()=>s(()=>import("./el.5081a6aa-6c646c72.js"),["assets/el.5081a6aa-6c646c72.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/en-au.js":()=>s(()=>import("./en-au.ad1bb4e7-231e802d.js"),["assets/en-au.ad1bb4e7-231e802d.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/en-ca.js":()=>s(()=>import("./en-ca.21ddbca0-1ac498f5.js"),["assets/en-ca.21ddbca0-1ac498f5.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/en-gb.js":()=>s(()=>import("./en-gb.08a92534-58776e42.js"),["assets/en-gb.08a92534-58776e42.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/en-ie.js":()=>s(()=>import("./en-ie.5fdaa7a9-7bb2e808.js"),["assets/en-ie.5fdaa7a9-7bb2e808.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/en-il.js":()=>s(()=>import("./en-il.0ed4a502-8e8a2464.js"),["assets/en-il.0ed4a502-8e8a2464.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/en-in.js":()=>s(()=>import("./en-in.c9d56fc3-023683b8.js"),["assets/en-in.c9d56fc3-023683b8.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/en-nz.js":()=>s(()=>import("./en-nz.11b9f1e2-52dc5289.js"),["assets/en-nz.11b9f1e2-52dc5289.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/en-sg.js":()=>s(()=>import("./en-sg.db434006-ad04077e.js"),["assets/en-sg.db434006-ad04077e.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/en-tt.js":()=>s(()=>import("./en-tt.1955abe1-2c504718.js"),["assets/en-tt.1955abe1-2c504718.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/en.js":()=>s(()=>import("./en.c289298e-aaf0afdb.js"),[]),"./locale/eo.js":()=>s(()=>import("./eo.9dd27be9-c9916582.js"),["assets/eo.9dd27be9-c9916582.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/es-do.js":()=>s(()=>import("./es-do.0d938f68-db29ee0c.js"),["assets/es-do.0d938f68-db29ee0c.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/es-mx.js":()=>s(()=>import("./es-mx.d46c7f89-fee1ee9d.js"),["assets/es-mx.d46c7f89-fee1ee9d.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/es-pr.js":()=>s(()=>import("./es-pr.7ba6c2af-8196c5a1.js"),["assets/es-pr.7ba6c2af-8196c5a1.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/es-us.js":()=>s(()=>import("./es-us.76724c6d-c9249ab1.js"),["assets/es-us.76724c6d-c9249ab1.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/es.js":()=>s(()=>import("./es.df7fd3d9-04c701cf.js"),["assets/es.df7fd3d9-04c701cf.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/et.js":()=>s(()=>import("./et.777d6fee-68de13b6.js"),["assets/et.777d6fee-68de13b6.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/eu.js":()=>s(()=>import("./eu.ace54020-4a36a109.js"),["assets/eu.ace54020-4a36a109.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/fa.js":()=>s(()=>import("./fa.fd258ea1-1c80fa5e.js"),["assets/fa.fd258ea1-1c80fa5e.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/fi.js":()=>s(()=>import("./fi.0f01b077-5f0b717d.js"),["assets/fi.0f01b077-5f0b717d.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/fo.js":()=>s(()=>import("./fo.c7a2f5b1-8f927699.js"),["assets/fo.c7a2f5b1-8f927699.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/fr-ca.js":()=>s(()=>import("./fr-ca.563e4fce-a0553c96.js"),["assets/fr-ca.563e4fce-a0553c96.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/fr-ch.js":()=>s(()=>import("./fr-ch.b03dbf82-6005df68.js"),["assets/fr-ch.b03dbf82-6005df68.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/fr.js":()=>s(()=>import("./fr.04ee9d68-5e6f01e2.js"),["assets/fr.04ee9d68-5e6f01e2.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/fy.js":()=>s(()=>import("./fy.28222af1-337db3ba.js"),["assets/fy.28222af1-337db3ba.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ga.js":()=>s(()=>import("./ga.7661ff1f-30a7ad99.js"),["assets/ga.7661ff1f-30a7ad99.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/gd.js":()=>s(()=>import("./gd.140449ba-faf349a6.js"),["assets/gd.140449ba-faf349a6.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/gl.js":()=>s(()=>import("./gl.25bd5a97-0c153280.js"),["assets/gl.25bd5a97-0c153280.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/gom-latn.js":()=>s(()=>import("./gom-latn.3ee46935-74a13dfc.js"),["assets/gom-latn.3ee46935-74a13dfc.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/gu.js":()=>s(()=>import("./gu.024a987c-0f3699f2.js"),["assets/gu.024a987c-0f3699f2.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/he.js":()=>s(()=>import("./he.5d94292f-11344674.js"),["assets/he.5d94292f-11344674.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/hi.js":()=>s(()=>import("./hi.9036dcd4-2dc253af.js"),["assets/hi.9036dcd4-2dc253af.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/hr.js":()=>s(()=>import("./hr.5062be1d-b2ca1c01.js"),["assets/hr.5062be1d-b2ca1c01.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ht.js":()=>s(()=>import("./ht.69059849-ece9e8e4.js"),["assets/ht.69059849-ece9e8e4.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/hu.js":()=>s(()=>import("./hu.33a15367-4edeba07.js"),["assets/hu.33a15367-4edeba07.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/hy-am.js":()=>s(()=>import("./hy-am.79001de4-436e59a8.js"),["assets/hy-am.79001de4-436e59a8.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/id.js":()=>s(()=>import("./id.66cfe616-2687f699.js"),["assets/id.66cfe616-2687f699.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/is.js":()=>s(()=>import("./is.c7a21122-9c897e91.js"),["assets/is.c7a21122-9c897e91.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/it-ch.js":()=>s(()=>import("./it-ch.3b126b32-06d1c9dc.js"),["assets/it-ch.3b126b32-06d1c9dc.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/it.js":()=>s(()=>import("./it.c151e274-b1c6fda3.js"),["assets/it.c151e274-b1c6fda3.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ja.js":()=>s(()=>import("./ja.c78cf541-74464494.js"),["assets/ja.c78cf541-74464494.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/jv.js":()=>s(()=>import("./jv.a747a1a3-62d1e773.js"),["assets/jv.a747a1a3-62d1e773.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ka.js":()=>s(()=>import("./ka.41d428db-c44bcabf.js"),["assets/ka.41d428db-c44bcabf.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/kk.js":()=>s(()=>import("./kk.3b7b4220-446dbf05.js"),["assets/kk.3b7b4220-446dbf05.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/km.js":()=>s(()=>import("./km.c842e4e6-d1beec49.js"),["assets/km.c842e4e6-d1beec49.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/kn.js":()=>s(()=>import("./kn.ee6d6468-3b9f35a6.js"),["assets/kn.ee6d6468-3b9f35a6.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ko.js":()=>s(()=>import("./ko.0d60f7bc-05053c94.js"),["assets/ko.0d60f7bc-05053c94.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ku.js":()=>s(()=>import("./ku.9f8e49b8-c4fe9315.js"),["assets/ku.9f8e49b8-c4fe9315.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ky.js":()=>s(()=>import("./ky.93291eaa-12cf6c07.js"),["assets/ky.93291eaa-12cf6c07.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/lb.js":()=>s(()=>import("./lb.d1834c83-c55bd3ba.js"),["assets/lb.d1834c83-c55bd3ba.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/lo.js":()=>s(()=>import("./lo.31c82cd3-2881b7e5.js"),["assets/lo.31c82cd3-2881b7e5.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/lt.js":()=>s(()=>import("./lt.5798cdca-7ceb5b51.js"),["assets/lt.5798cdca-7ceb5b51.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/lv.js":()=>s(()=>import("./lv.7222c068-de927215.js"),["assets/lv.7222c068-de927215.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/me.js":()=>s(()=>import("./me.5ebeffe5-3f2675af.js"),["assets/me.5ebeffe5-3f2675af.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/mi.js":()=>s(()=>import("./mi.1c2375d0-072c8aef.js"),["assets/mi.1c2375d0-072c8aef.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/mk.js":()=>s(()=>import("./mk.3746980f-c1f1e964.js"),["assets/mk.3746980f-c1f1e964.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ml.js":()=>s(()=>import("./ml.09f4db49-bdce8e63.js"),["assets/ml.09f4db49-bdce8e63.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/mn.js":()=>s(()=>import("./mn.3937f421-de15f0fe.js"),["assets/mn.3937f421-de15f0fe.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/mr.js":()=>s(()=>import("./mr.764d0b33-8dccb2c1.js"),["assets/mr.764d0b33-8dccb2c1.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ms-my.js":()=>s(()=>import("./ms-my.70947be7-722352a5.js"),["assets/ms-my.70947be7-722352a5.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ms.js":()=>s(()=>import("./ms.62cce7b5-596493a1.js"),["assets/ms.62cce7b5-596493a1.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/mt.js":()=>s(()=>import("./mt.628e2b0e-55c47eaf.js"),["assets/mt.628e2b0e-55c47eaf.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/my.js":()=>s(()=>import("./my.b8e9dc5f-5255de3a.js"),["assets/my.b8e9dc5f-5255de3a.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/nb.js":()=>s(()=>import("./nb.959a2d1a-bf744d25.js"),["assets/nb.959a2d1a-bf744d25.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ne.js":()=>s(()=>import("./ne.bc92b43e-052f3e06.js"),["assets/ne.bc92b43e-052f3e06.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/nl-be.js":()=>s(()=>import("./nl-be.67b82913-d904709c.js"),["assets/nl-be.67b82913-d904709c.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/nl.js":()=>s(()=>import("./nl.c9873428-0681877e.js"),["assets/nl.c9873428-0681877e.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/nn.js":()=>s(()=>import("./nn.4610d068-e187913d.js"),["assets/nn.4610d068-e187913d.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/oc-lnc.js":()=>s(()=>import("./oc-lnc.8945d1ce-2d24ac9c.js"),["assets/oc-lnc.8945d1ce-2d24ac9c.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/pa-in.js":()=>s(()=>import("./pa-in.52b608bf-386d3c38.js"),["assets/pa-in.52b608bf-386d3c38.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/pl.js":()=>s(()=>import("./pl.4c1086f0-be9bbaee.js"),["assets/pl.4c1086f0-be9bbaee.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/pt-br.js":()=>s(()=>import("./pt-br.6b29a398-2490c102.js"),["assets/pt-br.6b29a398-2490c102.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/pt.js":()=>s(()=>import("./pt.08903cb0-676b3820.js"),["assets/pt.08903cb0-676b3820.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/rn.js":()=>s(()=>import("./rn.b311783d-9830f7bc.js"),["assets/rn.b311783d-9830f7bc.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ro.js":()=>s(()=>import("./ro.5ebccc9f-12a2fb6d.js"),["assets/ro.5ebccc9f-12a2fb6d.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ru.js":()=>s(()=>import("./ru.d91e83d3-101ff5c0.js"),["assets/ru.d91e83d3-101ff5c0.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/rw.js":()=>s(()=>import("./rw.a9406a95-a91e6b71.js"),["assets/rw.a9406a95-a91e6b71.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/sd.js":()=>s(()=>import("./sd.f2aeb928-e0a6c320.js"),["assets/sd.f2aeb928-e0a6c320.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/se.js":()=>s(()=>import("./se.32bf71de-0da7e823.js"),["assets/se.32bf71de-0da7e823.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/si.js":()=>s(()=>import("./si.c99551c6-d149aea8.js"),["assets/si.c99551c6-d149aea8.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/sk.js":()=>s(()=>import("./sk.de35eedb-3c10dc2c.js"),["assets/sk.de35eedb-3c10dc2c.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/sl.js":()=>s(()=>import("./sl.22c4ba87-fd0ed388.js"),["assets/sl.22c4ba87-fd0ed388.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/sq.js":()=>s(()=>import("./sq.b30cbaca-aeab5db9.js"),["assets/sq.b30cbaca-aeab5db9.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/sr-cyrl.js":()=>s(()=>import("./sr-cyrl.0fd1daac-4c04ed65.js"),["assets/sr-cyrl.0fd1daac-4c04ed65.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/sr.js":()=>s(()=>import("./sr.2707a1bd-2abd676d.js"),["assets/sr.2707a1bd-2abd676d.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ss.js":()=>s(()=>import("./ss.9ac39d7a-9502a3bf.js"),["assets/ss.9ac39d7a-9502a3bf.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/sv-fi.js":()=>s(()=>import("./sv-fi.6bac7f9a-be1bcf49.js"),["assets/sv-fi.6bac7f9a-be1bcf49.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/sv.js":()=>s(()=>import("./sv.caf7bb40-8fcbdcee.js"),["assets/sv.caf7bb40-8fcbdcee.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/sw.js":()=>s(()=>import("./sw.59b71c0d-f43184d9.js"),["assets/sw.59b71c0d-f43184d9.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ta.js":()=>s(()=>import("./ta.4227d78a-49404871.js"),["assets/ta.4227d78a-49404871.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/te.js":()=>s(()=>import("./te.176edbc1-7c584cfe.js"),["assets/te.176edbc1-7c584cfe.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/tet.js":()=>s(()=>import("./tet.caf866ff-ca2cf035.js"),["assets/tet.caf866ff-ca2cf035.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/tg.js":()=>s(()=>import("./tg.a99bbdc2-21727a3a.js"),["assets/tg.a99bbdc2-21727a3a.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/th.js":()=>s(()=>import("./th.8c157ad1-7eea1059.js"),["assets/th.8c157ad1-7eea1059.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/tk.js":()=>s(()=>import("./tk.882ec3cc-6a5857ec.js"),["assets/tk.882ec3cc-6a5857ec.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/tl-ph.js":()=>s(()=>import("./tl-ph.1f9806a1-4e90ee53.js"),["assets/tl-ph.1f9806a1-4e90ee53.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/tlh.js":()=>s(()=>import("./tlh.52616aff-36a8873d.js"),["assets/tlh.52616aff-36a8873d.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/tr.js":()=>s(()=>import("./tr.5000c4a3-11fc2280.js"),["assets/tr.5000c4a3-11fc2280.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/tzl.js":()=>s(()=>import("./tzl.4084c20a-d15391b2.js"),["assets/tzl.4084c20a-d15391b2.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/tzm-latn.js":()=>s(()=>import("./tzm-latn.6fc428fb-45b23b3f.js"),["assets/tzm-latn.6fc428fb-45b23b3f.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/tzm.js":()=>s(()=>import("./tzm.277f8193-60e0ddc2.js"),["assets/tzm.277f8193-60e0ddc2.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ug-cn.js":()=>s(()=>import("./ug-cn.17ee2d0d-4aef4e73.js"),["assets/ug-cn.17ee2d0d-4aef4e73.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/uk.js":()=>s(()=>import("./uk.78979c97-bc91e17d.js"),["assets/uk.78979c97-bc91e17d.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/ur.js":()=>s(()=>import("./ur.002c5392-02fa1c40.js"),["assets/ur.002c5392-02fa1c40.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/uz-latn.js":()=>s(()=>import("./uz-latn.7fa3ceb8-5bb9a74d.js"),["assets/uz-latn.7fa3ceb8-5bb9a74d.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/uz.js":()=>s(()=>import("./uz.12fffa69-f6ed574d.js"),["assets/uz.12fffa69-f6ed574d.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/vi.js":()=>s(()=>import("./vi.b41f7d47-f7b6691b.js"),["assets/vi.b41f7d47-f7b6691b.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/x-pseudo.js":()=>s(()=>import("./x-pseudo.2a72f78d-181a1894.js"),["assets/x-pseudo.2a72f78d-181a1894.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/yo.js":()=>s(()=>import("./yo.13a0bc5a-1914d302.js"),["assets/yo.13a0bc5a-1914d302.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/zh-cn.js":()=>s(()=>import("./zh-cn.41c9e979-d39a49c5.js"),["assets/zh-cn.41c9e979-d39a49c5.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/zh-hk.js":()=>s(()=>import("./zh-hk.f63ec35c-d38faf25.js"),["assets/zh-hk.f63ec35c-d38faf25.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/zh-tw.js":()=>s(()=>import("./zh-tw.5843df61-1a996858.js"),["assets/zh-tw.5843df61-1a996858.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"]),"./locale/zh.js":()=>s(()=>import("./zh.3d7de479-7faf55c6.js"),["assets/zh.3d7de479-7faf55c6.js","assets/index-9d9f1067.js","assets/index-aeb4b3b6.css"])});for(const y in p)p[y]().then(()=>{u.locale(c);let o,f;if(P()){if(h()){if(e.modelValue.length>0){const[H,te]=e.modelValue;o=u(H,e.formatter.date,!0),f=u(te,e.formatter.date,!0)}}else if(m()){if(!jt(e.modelValue))try{Object.keys(e.modelValue)}catch{console.warn("[Vue Tailwind Datepicker]: It looks like you want to use Object as the argument %cv-model","font-style: italic; color: #42b883;",", but you pass it undefined or null."),console.warn("[Vue Tailwind Datepicker]: We has replace with %c{ startDate: '', endDate: '' }","font-style: italic; color: #42b883;",", but you can replace manually."),a("update:modelValue",{startDate:"",endDate:""})}if(e.modelValue){const[H,te]=Object.values(e.modelValue);o=H&&u(H,e.formatter.date,!0),f=te&&u(te,e.formatter.date,!0)}}else if(e.modelValue){const[H,te]=e.modelValue.split(e.separator);o=u(H,e.formatter.date,!0),f=u(te,e.formatter.date,!0)}o&&f?(v.value=x({previous:o,next:f},e),f.isBefore(o,"month")?(r.value.previous=f,r.value.next=o,r.value.year.previous=f.year(),r.value.year.next=o.year()):f.isSame(o,"month")?(r.value.previous=o,r.value.next=f.add(1,"month"),r.value.year.previous=o.year(),r.value.year.next=o.add(1,"year").year()):(r.value.previous=o,r.value.next=f,r.value.year.previous=o.year(),r.value.year.next=f.year()),e.autoApply||(D.value=[o,f])):(r.value.previous=u(e.startFrom),r.value.next=u(e.startFrom).add(1,"month"),r.value.year.previous=r.value.previous.year(),r.value.year.next=r.value.next.year())}else{if(h()){if(e.modelValue.length>0){const[H]=e.modelValue;o=u(H,e.formatter.date,!0)}}else if(m()){if(e.modelValue){const[H]=Object.values(e.modelValue);o=u(H,e.formatter.date,!0)}}else if(e.modelValue.length){const[H]=e.modelValue.split(e.separator);o=u(H,e.formatter.date,!0)}o&&o.isValid()?(v.value=j(o,e),r.value.previous=o,r.value.next=o.add(1,"month"),r.value.year.previous=o.year(),r.value.year.next=o.add(1,"year").year(),e.autoApply||(D.value=[o])):(r.value.previous=u(e.startFrom),r.value.next=u(e.startFrom).add(1,"month"),r.value.year.previous=r.value.previous.year(),r.value.year.next=r.value.next.year())}const C=e.weekdaysSize==="min"?u.weekdaysMin():u.weekdaysShort();r.value.weeks=X()?M(C):C,r.value.months=e.formatter.month==="MMM"?u.monthsShort():u.months()}).catch(o=>{console.warn(o.message)})})});const Ie=c=>(c&&K.value===null&&(K.value=k(O.value)),c&&K.value?"place-right":"place-left"),Vt=c=>(c&&K.value===null&&(K.value=k(O.value)),K.value?"left-auto right-0":"left-0 right-auto");return pe("isBetweenRange",me),pe("betweenRangeClasses",ge),pe("datepickerClasses",ve),pe("atMouseOver",ae),pe("setToToday",ne),pe("setToYesterday",ue),pe("setToLastDay",_e),pe("setToThisMonth",be),pe("setToLastMonth",$e),pe("setToCustomShortcut",Me),(c,p)=>e.noInput?Q.value?(G(),J("div",uo,[T("div",co,[T("div",vo,[e.shortcuts?(G(),Ye(at,{key:0,shortcuts:e.shortcuts,"as-range":P(),"as-single":e.asSingle,i18n:e.options.shortcuts},null,8,["shortcuts","as-range","as-single","i18n"])):he("",!0),T("div",po,[P()&&!e.asSingle?(G(),J("div",fo,ho)):he("",!0),T("div",{class:we(["relative w-full lg:w-80",{"mb-3 sm:mb-0 sm:mr-2 md:w-1/2":P()&&!e.asSingle}])},[re(Ue,{panel:n.previous,calendar:F(b).previous},null,8,["panel","calendar"]),T("div",yo,[ie(re(ze,{months:F(R),"onUpdate:month":F(b).previous.setMount},null,8,["months","onUpdate:month"]),[[de,n.previous.month]]),ie(re(Ge,{years:F(b).previous.years(),"onUpdate:year":F(b).previous.setYear},null,8,["years","onUpdate:year"]),[[de,n.previous.year]]),ie(T("div",null,[re(We,{weeks:F(B)},null,8,["weeks"]),re(Qe,{calendar:F(b).previous,weeks:F(B),"as-range":P(),"onUpdate:date":p[2]||(p[2]=(y,o)=>Z(y,o))},null,8,["calendar","weeks","as-range"])],512),[[de,n.previous.calendar]])])],2),P()&&!e.asSingle?(G(),J("div",_o,[re(Ue,{"as-prev-or-next":"",panel:n.next,calendar:F(b).next},null,8,["panel","calendar"]),T("div",bo,[ie(re(ze,{months:F(R),"onUpdate:month":F(b).next.setMount},null,8,["months","onUpdate:month"]),[[de,n.next.month]]),ie(re(Ge,{"as-prev-or-next":"",years:F(b).next.years(),"onUpdate:year":F(b).next.setYear},null,8,["years","onUpdate:year"]),[[de,n.next.year]]),ie(T("div",null,[re(We,{weeks:F(B)},null,8,["weeks"]),re(Qe,{"as-prev-or-next":"",calendar:F(b).next,weeks:F(B),"as-range":P(),"onUpdate:date":p[3]||(p[3]=(y,o)=>Z(y,o))},null,8,["calendar","weeks","as-range"])],512),[[de,n.next.calendar]])])])):he("",!0)])]),e.autoApply?he("",!0):(G(),J("div",go,[T("div",xo,[T("div",ko,[T("button",{type:"button",class:"away-apply-picker w-full transition ease-out duration-300 inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-vtd-primary-600 text-base font-medium text-white hover:bg-vtd-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-vtd-primary-500 sm:ml-3 sm:w-auto sm:text-sm dark:ring-offset-vtd-secondary-800 disabled:cursor-not-allowed",disabled:e.asSingle?D.value.length<1:D.value.length<2,onClick:p[4]||(p[4]=y=>q()),textContent:le(e.options.footer.apply)},null,8,wo)])])]))])])):he("",!0):(G(),Ye(F(yt),{key:0,as:"div",id:"vtd",class:"relative w-full"},{default:Le(({open:y})=>[e.overlay&&!e.disabled?(G(),Ye(F(Lr),{key:0,class:"fixed inset-0 bg-black opacity-30"})):he("",!0),re(F(jr),{as:"label",class:"relative block"},{default:Le(()=>[Lt(c.$slots,"default",{value:v.value,placeholder:V.value,clear:I},()=>[ie(T("input",At({ref_key:"VtdInputRef",ref:N,type:"text",class:["relative block w-full",[e.disabled?"cursor-default opacity-50":"opacity-100",t.inputClasses||"pl-3 pr-12 py-2.5 rounded-lg overflow-hidden border-solid text-sm text-vtd-secondary-700 placeholder-vtd-secondary-400 transition-colors bg-white border border-vtd-secondary-300 focus:border-vtd-primary-300 focus:ring focus:ring-vtd-primary-500 focus:ring-opacity-10 focus:outline-none dark:bg-vtd-secondary-800 dark:border-vtd-secondary-700 dark:text-vtd-secondary-100 dark:placeholder-vtd-secondary-500 dark:focus:border-vtd-primary-500 dark:focus:ring-opacity-20"]],disabled:e.disabled,autocomplete:"off","data-lpignore":"true","data-form-type":"other"},c.$attrs,{"onUpdate:modelValue":p[0]||(p[0]=o=>v.value=o),placeholder:V.value,onKeyup:W}),null,16,Br),[[Tt,v.value]]),T("div",Fr,[T("button",{type:"button",disabled:e.disabled,class:we([e.disabled?"cursor-default opacity-50":"opacity-100","px-2 py-1 mr-1 focus:outline-none text-vtd-secondary-400 dark:text-opacity-70 rounded-md"]),onClick:p[1]||(p[1]=o=>e.disabled?!1:v.value?I():c.$refs.VtdInputRef.focus())},[(G(),J("svg",Hr,[v.value?(G(),J("path",Ur)):(G(),J("path",zr))]))],10,Nr)])])]),_:3}),re(st,{"enter-from-class":"opacity-0 translate-y-3","enter-to-class":"opacity-100 translate-y-0","enter-active-class":"transform transition ease-out duration-200","leave-active-class":"transform transition ease-in duration-150","leave-from-class":"opacity-100 translate-y-0","leave-to-class":"opacity-0 translate-y-3"},{default:Le(()=>[e.disabled?he("",!0):(G(),Ye(F(Ar),{key:0,as:"div",class:"relative z-50"},{default:Le(({close:o})=>[T("div",{class:we(["absolute z-50 top-full sm:mt-2.5",Vt(y)])},[T("div",{ref_key:"VtdRef",ref:O,class:"fixed inset-0 z-50 overflow-y-auto sm:overflow-visible sm:static sm:z-auto bg-white dark:bg-vtd-secondary-800 sm:rounded-lg shadow-sm"},[T("div",{class:we(["vtd-datepicker static sm:relative w-full bg-white sm:rounded-lg sm:shadow-sm border-0 sm:border border-black/[.1] px-3 py-3 sm:px-4 sm:py-4 dark:bg-vtd-secondary-800 dark:border-vtd-secondary-700/[1]",Ie(y)])},[T("div",Wr,[e.shortcuts?(G(),Ye(at,{key:0,shortcuts:e.shortcuts,"as-range":P(),"as-single":e.asSingle,i18n:e.options.shortcuts,close:o},null,8,["shortcuts","as-range","as-single","i18n","close"])):he("",!0),T("div",Gr,[P()&&!e.asSingle?(G(),J("div",Qr,Kr)):he("",!0),T("div",{class:we(["relative",{"mb-3 sm:mb-0 sm:mr-2 w-full md:w-1/2 lg:w-80":P()&&!e.asSingle,"w-full":!P()&&e.asSingle}])},[re(Ue,{panel:n.previous,calendar:F(b).previous},null,8,["panel","calendar"]),T("div",Jr,[ie(re(ze,{months:F(R),"onUpdate:month":F(b).previous.setMount},null,8,["months","onUpdate:month"]),[[de,n.previous.month]]),ie(re(Ge,{years:F(b).previous.years(),"onUpdate:year":F(b).previous.setYear},null,8,["years","onUpdate:year"]),[[de,n.previous.year]]),ie(T("div",null,[re(We,{weeks:F(B)},null,8,["weeks"]),re(Qe,{calendar:F(b).previous,weeks:F(B),"as-range":P(),"onUpdate:date":(f,C)=>Z(f,C,o)},null,8,["calendar","weeks","as-range","onUpdate:date"])],512),[[de,n.previous.calendar]])])],2),P()&&!e.asSingle?(G(),J("div",Xr,[re(Ue,{"as-prev-or-next":"",panel:n.next,calendar:F(b).next},null,8,["panel","calendar"]),T("div",qr,[ie(re(ze,{months:F(R),"onUpdate:month":F(b).next.setMount},null,8,["months","onUpdate:month"]),[[de,n.next.month]]),ie(re(Ge,{"as-prev-or-next":"",years:F(b).next.years(),"onUpdate:year":F(b).next.setYear},null,8,["years","onUpdate:year"]),[[de,n.next.year]]),ie(T("div",null,[re(We,{weeks:F(B)},null,8,["weeks"]),re(Qe,{"as-prev-or-next":"",calendar:F(b).next,weeks:F(B),"as-range":P(),"onUpdate:date":(f,C)=>Z(f,C,o)},null,8,["calendar","weeks","as-range","onUpdate:date"])],512),[[de,n.next.calendar]])])])):he("",!0)])]),e.autoApply?(G(),J("div",no,[T("div",so,[T("div",lo,[T("button",{type:"button",onClick:f=>o(),class:"away-cancel-picker w-full transition ease-out duration-300 inline-flex justify-center rounded-md border border-vtd-secondary-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-vtd-secondary-700 hover:bg-vtd-secondary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-vtd-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:ring-offset-vtd-secondary-800",textContent:le(e.options.footer.cancel)},null,8,io)])])])):(G(),J("div",eo,[T("div",to,[T("div",ro,[T("button",{type:"button",class:"away-apply-picker w-full transition ease-out duration-300 inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-vtd-primary-600 text-base font-medium text-white hover:bg-vtd-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-vtd-primary-500 sm:ml-3 sm:w-auto sm:text-sm dark:ring-offset-vtd-secondary-800 disabled:cursor-not-allowed",disabled:e.asSingle?D.value.length<1:D.value.length<2,onClick:f=>q(o),textContent:le(e.options.footer.apply)},null,8,oo),T("button",{type:"button",onClick:f=>o(),class:"mt-3 away-cancel-picker w-full transition ease-out duration-300 inline-flex justify-center rounded-md border border-vtd-secondary-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-vtd-secondary-700 hover:bg-vtd-secondary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-vtd-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:ring-offset-vtd-secondary-800",textContent:le(e.options.footer.cancel)},null,8,ao)])])]))],2)],512)],2)]),_:2},1024))]),_:2},1024)]),_:3}))}},Dt=(()=>{const t=Eo;return t.install=l=>{l.component("VueTailwindDatepicker",t)},t})(),Do=Object.freeze(Object.defineProperty({__proto__:null,default:Dt},Symbol.toStringTag,{value:"Module"}));Object.entries(Do).forEach(([t,l])=>{t!=="default"&&(Dt[t]=l)});(function(){try{if(typeof document<"u"){var t=document.createElement("style");t.appendChild(document.createTextNode('.vtd-datepicker-overlay.open:before{display:block;opacity:.5}.vtd-datepicker:before{--vtd-datepicker: 0px;content:"";position:absolute;top:0px;height:1rem;width:1rem;border-width:1px;border-color:#0000001a;--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity));--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.dark .vtd-datepicker:before{--tw-border-opacity: 1;border-color:rgb(55 65 81 / var(--tw-border-opacity));--tw-bg-opacity: 1;background-color:rgb(31 41 55 / var(--tw-bg-opacity))}.vtd-datepicker:before{transform:translate(50%,-50%) rotate(-45deg);-webkit-clip-path:polygon(calc(var(--vtd-datepicker) * -1) calc(var(--vtd-datepicker) * -1),calc(100% + var(--vtd-datepicker)) calc(var(--vtd-datepicker) * -1),calc(100% + var(--vtd-datepicker)) calc(100% + var(--vtd-datepicker)));clip-path:polygon(calc(var(--vtd-datepicker) * -1) calc(var(--vtd-datepicker) * -1),calc(100% + var(--vtd-datepicker)) calc(var(--vtd-datepicker) * -1),calc(100% + var(--vtd-datepicker)) calc(100% + var(--vtd-datepicker)))}.vtd-datepicker.place-left:before{left:.25rem}.dark .vtd-datepicker.place-left:before{--tw-border-opacity: 1;border-color:rgb(55 65 81 / var(--tw-border-opacity));--tw-bg-opacity: 1;background-color:rgb(31 41 55 / var(--tw-bg-opacity))}.vtd-datepicker.place-right:before{right:1.25rem}.dark .vtd-datepicker.place-right:before{--tw-border-opacity: 1;border-color:rgb(55 65 81 / var(--tw-border-opacity));--tw-bg-opacity: 1;background-color:rgb(31 41 55 / var(--tw-bg-opacity))}')),document.head.appendChild(t)}}catch(l){console.error("vite-plugin-css-injected-by-js",l)}})();export{Dt as S,u as l};
