import{a as c,B as A,k as b,o,b as l,e as a,w,d as e,c as g,u as s,v as _,F as C,q as k,s as d,t as f,E as u,x as U,U as Z,h as E}from"./index-bfe6943f.js";import{h as L,q as F,i as I}from"./index-5b4677b6.js";import{A as q}from"./auth.service-a9039ceb.js";import{_ as D}from"./AppTopbar-1fff46f6.js";import{c as m}from"./checkPermission.service-4ccc1117.js";const O={class:"space-y-12"},T={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},M={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},H={class:"flex justify-between items-center"},J={class:"flex items-center"},$=e("p",{class:"ml-4 text-lg text-gray-900"},"Základní informace",-1),G={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},K={class:"mt-8 grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-6"},Q={class:"sm:col-span-3"},R=e("label",{for:"degree-before",class:"block text-sm font-normal leading-6 text-gray-900"},"Titul před jménem:",-1),W={class:"mt-2"},X={key:1,class:"text-gray-900"},Y={key:0},ee={key:1},se={class:"sm:col-span-3"},te=e("label",{for:"degree-after",class:"block text-sm font-normal leading-6 text-gray-900"},"Titul za jménem:",-1),oe={class:"mt-2"},le={key:1,class:"text-gray-900"},ae={key:0},ne={key:1},re={class:"sm:col-span-3"},ie=e("label",{for:"first-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Jméno:",-1),de={class:"mt-2"},ce={key:1,class:"text-gray-900"},me={key:0},ue={key:1},ge={class:"sm:col-span-3"},pe=e("label",{for:"last-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Příjmení:",-1),_e={class:"mt-2"},fe={key:1,class:"text-gray-900"},he={key:0},ve={key:1},xe={key:1,class:"h-40 flex items-center"},ye={class:"bg-white border border-zinc-200/70 rounded-md"},be={key:0,class:"flex justify-between items-center p-5 pb-3"},we={class:"flex items-center"},ke=e("p",{class:"ml-4 text-lg text-gray-900"},"Změna emailové adresy",-1),Ve={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},je={class:"p-5 border-y border-zinc-200/70 flex items-center"},Ue=e("p",{class:"mr-4 font-medium text-sm"},"Aktuální email:",-1),Ze={class:"font-medium text-sm text-green-500"},qe={key:1,class:"grid grid-cols-1 gap-y-6 p-5"},ze={class:"sm:col-span-2"},Ne=e("label",{for:"password",class:"block text-sm font-normal leading-6 text-gray-900"},"Aktuální heslo:",-1),Se={class:"mt-2"},Be={class:"sm:col-span-2"},Pe=e("label",{for:"new-email",class:"block text-sm font-normal leading-6 text-gray-900"},"Zadejte novou emailovou adresu:",-1),Ae={class:"mt-2"},Ce={class:"sm:col-span-2"},Ee=e("label",{for:"new-email-confirmation",class:"block text-sm font-normal leading-6 text-gray-900"},"Opakujte novou emailovou adresu:",-1),Le={class:"mt-2"},Fe={key:1,class:"h-40 flex items-center"},Ie={key:0},De={class:"bg-white border border-zinc-200/70 rounded-md"},Oe={class:"flex justify-between items-center p-5 pb-3"},Te={class:"flex items-center"},Me=e("p",{class:"ml-4 text-lg text-gray-900"},"Změna hesla",-1),He=e("div",null,[e("button",{type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},"Uložit")],-1),Je={class:"mt-8 grid grid-cols-1 gap-y-6 p-5 pt-0"},$e={class:"sm:col-span-2"},Ge=e("label",{for:"old-password",class:"block text-sm font-normal leading-6 text-gray-900"},"Aktuální heslo",-1),Ke={class:"mt-2"},Qe={class:"sm:col-span-2"},Re=e("label",{for:"new-password",class:"block text-sm font-normal leading-6 text-gray-900"},"Nové heslo",-1),We={class:"mt-2"},Xe={class:"sm:col-span-2"},Ye=e("label",{for:"new-password-confirm",class:"block text-sm font-normal leading-6 text-gray-900"},"Nové heslo (znovu)",-1),es={class:"mt-2"},ss=e("div",{class:"p-5 border-t border-zinc-200/70"},[e("p",{class:"text-sm font-semibold"},"Požadavky na nové heslo:"),e("ul",{class:"list-disc text-sm pl-6 pt-4"},[e("li",{class:"pb-1"},[E("Heslo musí mít alespoň "),e("span",{class:"font-semibold"},"8 znaků")]),e("li",{class:"pb-1"},"Doporučujeme využít kombinaci malých a velkých písmen"),e("li",null,"Doporučujeme využít v hesle i číslici")])],-1),ts={key:1,class:"h-40 flex items-center"},ds={__name:"Profile",setup(os){const t=c({}),h=c(""),V=c(""),v=c(""),x=c(""),j=c(""),y=c(""),z=c(["profile"]),p=c(!0);A(()=>{p.value=!0,N(),p.value=!1});function N(){b.get("/api/users/profil").then(i=>{t.value=i.data}).catch(i=>{console.log(i)})}function S(){b.post("/api/users/profil",{first_name:t.value.first_name,middle_name:t.value.middle_name,last_name:t.value.last_name,degree_before:t.value.degree_before,degree_after:t.value.degree_after}).then(i=>{t.value=i.data.data.user,k.success(i.data.message)}).catch(i=>{k.error("Změny se nepovedlo uložit")})}function B(){b.post("/api/users/email-update",{new_email:h.value,new_email_confirmation:V.value,password:v.value}).then(i=>{q.logout(),k.success(i.data.message)}).catch(i=>{v.value=""})}function P(){b.post("/api/users/password-update",{new_password:x.value,new_password_confirmation:j.value,password:y.value}).then(i=>{q.logout(),k.success(i.data.message)}).catch(i=>{y.value=""})}return(i,n)=>(o(),l(C,null,[a(D,{breadCrumbs:z.value},{topbarButtons:w(()=>[]),_:1},8,["breadCrumbs"]),e("div",O,[e("div",T,[e("div",null,[e("div",M,[p.value?(o(),l("div",xe,[a(s(Z),{class:"mx-auto text-spinner-color",size:"40"})])):(o(),g(s(U),{key:0,onSubmit:S,class:"p-5 pb-8"},{default:w(()=>[e("div",H,[e("div",J,[a(s(L),{class:"w-7"}),$]),e("div",null,[s(m).check("profil.edit")?(o(),l("button",G,"Uložit")):_("",!0)])]),e("div",K,[e("div",Q,[R,e("div",W,[s(m).check("profil.edit")?(o(),g(s(d),{key:0,modelValue:t.value.degree_before,"onUpdate:modelValue":n[0]||(n[0]=r=>t.value.degree_before=r),id:"degree-before",name:"degree-before",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte titul před jménem..."},null,8,["modelValue"])):(o(),l("div",X,[t.value.degree_before&&t.value.degree_before.length?(o(),l("p",Y,f(t.value.degree_before),1)):(o(),l("p",ee,"-"))]))])]),e("div",se,[te,e("div",oe,[s(m).check("profil.edit")?(o(),g(s(d),{key:0,modelValue:t.value.degree_after,"onUpdate:modelValue":n[1]||(n[1]=r=>t.value.degree_after=r),id:"degree-after",name:"degree-after",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte titul za jménem..."},null,8,["modelValue"])):(o(),l("div",le,[t.value.degree_after&&t.value.degree_after.length?(o(),l("p",ae,f(t.value.degree_after),1)):(o(),l("p",ne,"-"))]))])]),e("div",re,[ie,e("div",de,[s(m).check("profil.edit")?(o(),g(s(d),{key:0,rules:"required|minLength:2|maxLength:50",modelValue:t.value.first_name,"onUpdate:modelValue":n[2]||(n[2]=r=>t.value.first_name=r),type:"text",name:"first-name",id:"first-name",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte jméno.."},null,8,["modelValue"])):(o(),l("div",ce,[t.value.first_name&&t.value.first_name.length?(o(),l("p",me,f(t.value.first_name),1)):(o(),l("p",ue,"-"))])),a(s(u),{name:"first-name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",ge,[pe,e("div",_e,[s(m).check("profil.edit")?(o(),g(s(d),{key:0,rules:"required|minLength:2|maxLength:50",modelValue:t.value.last_name,"onUpdate:modelValue":n[3]||(n[3]=r=>t.value.last_name=r),type:"text",name:"last-name",id:"last-name",autocomplete:"family-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte Příjmení.."},null,8,["modelValue"])):(o(),l("div",fe,[t.value.last_name&&t.value.last_name.length?(o(),l("p",he,f(t.value.last_name),1)):(o(),l("p",ve,"-"))])),a(s(u),{name:"last-name",class:"text-rose-400 text-sm block pt-1"})])])])]),_:1}))]),e("div",ye,[p.value?(o(),l("div",Fe,[a(s(Z),{class:"mx-auto text-spinner-color",size:"40"})])):(o(),g(s(U),{key:0,onSubmit:B},{default:w(()=>[s(m).check("profil.change_email")?(o(),l("div",be,[e("div",we,[a(s(F),{class:"w-7"}),ke]),e("div",null,[s(m).check("profil.change_email")?(o(),l("button",Ve,"Uložit")):_("",!0)])])):_("",!0),e("div",je,[Ue,e("p",Ze,f(t.value.email),1)]),s(m).check("profil.change_email")?(o(),l("div",qe,[e("div",ze,[Ne,e("div",Se,[a(s(d),{rules:"required",modelValue:v.value,"onUpdate:modelValue":n[4]||(n[4]=r=>v.value=r),id:"password",name:"password",type:"password",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte aktuální heslo..."},null,8,["modelValue"]),a(s(u),{name:"password",class:"text-rose-400 text-sm block pt-1"})])]),e("div",Be,[Pe,e("div",Ae,[a(s(d),{rules:"required|email",modelValue:h.value,"onUpdate:modelValue":n[5]||(n[5]=r=>h.value=r),id:"new-email",name:"new-email",type:"email",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte novou emailovou adresu..."},null,8,["modelValue"]),a(s(u),{name:"new-email",class:"text-rose-400 text-sm block pt-1"})])]),e("div",Ce,[Ee,e("div",Le,[a(s(d),{rules:"required|email|isEqual:"+h.value,modelValue:V.value,"onUpdate:modelValue":n[6]||(n[6]=r=>V.value=r),id:"new-email-confirmation",name:"new-email-confirmation",type:"email",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Opakujte novou emailovou adresu..."},null,8,["rules","modelValue"]),a(s(u),{name:"new-email-confirmation",class:"text-rose-400 text-sm block pt-1"})])])])):_("",!0)]),_:1}))])]),s(m).check("profil.change_password")?(o(),l("div",Ie,[e("div",De,[p.value?(o(),l("div",ts,[a(s(Z),{class:"mx-auto text-spinner-color",size:"40"})])):(o(),g(s(U),{key:0,onSubmit:P},{default:w(()=>[e("div",Oe,[e("div",Te,[a(s(I),{class:"w-7"}),Me]),He]),e("div",Je,[e("div",$e,[Ge,e("div",Ke,[a(s(d),{rules:"required",modelValue:y.value,"onUpdate:modelValue":n[7]||(n[7]=r=>y.value=r),id:"old-password",name:"old-password",type:"password",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte aktuální heslo..."},null,8,["modelValue"]),a(s(u),{name:"old-password",class:"text-rose-400 text-sm block pt-1"})])]),e("div",Qe,[Re,e("div",We,[a(s(d),{rules:"required|password",modelValue:x.value,"onUpdate:modelValue":n[8]||(n[8]=r=>x.value=r),id:"new-password",name:"new-password",type:"password",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte nové heslo..."},null,8,["modelValue"]),a(s(u),{name:"new-password",class:"text-rose-400 text-sm block pt-1"})])]),e("div",Xe,[Ye,e("div",es,[a(s(d),{rules:"required|password|isEqual:"+x.value,modelValue:j.value,"onUpdate:modelValue":n[9]||(n[9]=r=>j.value=r),id:"new-password-confirm",name:"new-password-confirm",type:"password",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Opakujte nové heslo..."},null,8,["rules","modelValue"]),a(s(u),{name:"new-password-confirm",class:"text-rose-400 text-sm block pt-1"})])])]),ss]),_:1}))])])):_("",!0)])])],64))}};export{ds as default};
