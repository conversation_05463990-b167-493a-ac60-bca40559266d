import{y as O,S as L,F as N,o as d,c as B,u as K,l as h,H as P,t as w,p as j,N as D,a as v,D as U,d as E,v as C,I as V}from"./hidden-7b234e84.js";import{u as H,x as _,a as b}from"./use-tracked-pointer-ed292bdb.js";import{p as q}from"./use-tree-walker-b9bc1be5.js";import{b as J}from"./use-resolve-button-type-8b834d9a.js";import{H as T,a as M,I as R,B as A,J as Q,K as Z,Z as y,j as W,L as X}from"./index-3de9bee7.js";var Y=(n=>(n[n.Open=0]="Open",n[n.Closed=1]="Closed",n))(Y||{}),z=(n=>(n[n.Pointer=0]="Pointer",n[n.Other=1]="Other",n))(z||{});function G(n){requestAnimationFrame(()=>requestAnimationFrame(n))}let F=Symbol("MenuContext");function k(n){let g=W(F,null);if(g===null){let S=new Error(`<${n} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(S,k),S}return g}let le=T({name:"Menu",props:{as:{type:[Object,String],default:"template"}},setup(n,{slots:g,attrs:S}){let I=M(1),e=M(null),c=M(null),i=M([]),m=M(""),s=M(null),u=M(1);function t(l=r=>r){let r=s.value!==null?i.value[s.value]:null,a=V(l(i.value.slice()),f=>d(f.dataRef.domRef)),o=r?a.indexOf(r):null;return o===-1&&(o=null),{items:a,activeItemIndex:o}}let p={menuState:I,buttonRef:e,itemsRef:c,items:i,searchQuery:m,activeItemIndex:s,activationTrigger:u,closeMenu:()=>{I.value=1,s.value=null},openMenu:()=>I.value=0,goToItem(l,r,a){let o=t(),f=_(l===b.Specific?{focus:b.Specific,id:r}:{focus:l},{resolveItems:()=>o.items,resolveActiveIndex:()=>o.activeItemIndex,resolveId:x=>x.id,resolveDisabled:x=>x.dataRef.disabled});m.value="",s.value=f,u.value=a??1,i.value=o.items},search(l){let r=m.value!==""?0:1;m.value+=l.toLowerCase();let a=(s.value!==null?i.value.slice(s.value+r).concat(i.value.slice(0,s.value+r)):i.value).find(f=>f.dataRef.textValue.startsWith(m.value)&&!f.dataRef.disabled),o=a?i.value.indexOf(a):-1;o===-1||o===s.value||(s.value=o,u.value=1)},clearSearch(){m.value=""},registerItem(l,r){let a=t(o=>[...o,{id:l,dataRef:r}]);i.value=a.items,s.value=a.activeItemIndex,u.value=1},unregisterItem(l){let r=t(a=>{let o=a.findIndex(f=>f.id===l);return o!==-1&&a.splice(o,1),a});i.value=r.items,s.value=r.activeItemIndex,u.value=1}};return O([e,c],(l,r)=>{var a;p.closeMenu(),L(r,N.Loose)||(l.preventDefault(),(a=d(e))==null||a.focus())},R(()=>I.value===0)),X(F,p),B(R(()=>K(I.value,{[0]:h.Open,[1]:h.Closed}))),()=>{let l={open:I.value===0,close:p.closeMenu};return P({ourProps:{},theirProps:n,slot:l,slots:g,attrs:S,name:"Menu"})}}}),oe=T({name:"MenuButton",props:{disabled:{type:Boolean,default:!1},as:{type:[Object,String],default:"button"},id:{type:String,default:()=>`headlessui-menu-button-${w()}`}},setup(n,{attrs:g,slots:S,expose:I}){let e=k("MenuButton");I({el:e.buttonRef,$el:e.buttonRef});function c(u){switch(u.key){case v.Space:case v.Enter:case v.ArrowDown:u.preventDefault(),u.stopPropagation(),e.openMenu(),y(()=>{var t;(t=d(e.itemsRef))==null||t.focus({preventScroll:!0}),e.goToItem(b.First)});break;case v.ArrowUp:u.preventDefault(),u.stopPropagation(),e.openMenu(),y(()=>{var t;(t=d(e.itemsRef))==null||t.focus({preventScroll:!0}),e.goToItem(b.Last)});break}}function i(u){switch(u.key){case v.Space:u.preventDefault();break}}function m(u){n.disabled||(e.menuState.value===0?(e.closeMenu(),y(()=>{var t;return(t=d(e.buttonRef))==null?void 0:t.focus({preventScroll:!0})})):(u.preventDefault(),e.openMenu(),G(()=>{var t;return(t=d(e.itemsRef))==null?void 0:t.focus({preventScroll:!0})})))}let s=J(R(()=>({as:n.as,type:g.type})),e.buttonRef);return()=>{var u;let t={open:e.menuState.value===0},{id:p,...l}=n,r={ref:e.buttonRef,id:p,type:s.value,"aria-haspopup":"menu","aria-controls":(u=d(e.itemsRef))==null?void 0:u.id,"aria-expanded":n.disabled?void 0:e.menuState.value===0,onKeydown:c,onKeyup:i,onClick:m};return P({ourProps:r,theirProps:l,slot:t,attrs:g,slots:S,name:"MenuButton"})}}}),ue=T({name:"MenuItems",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:()=>`headlessui-menu-items-${w()}`}},setup(n,{attrs:g,slots:S,expose:I}){let e=k("MenuItems"),c=M(null);I({el:e.itemsRef,$el:e.itemsRef}),q({container:R(()=>d(e.itemsRef)),enabled:R(()=>e.menuState.value===0),accept(t){return t.getAttribute("role")==="menuitem"?NodeFilter.FILTER_REJECT:t.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(t){t.setAttribute("role","none")}});function i(t){var p;switch(c.value&&clearTimeout(c.value),t.key){case v.Space:if(e.searchQuery.value!=="")return t.preventDefault(),t.stopPropagation(),e.search(t.key);case v.Enter:if(t.preventDefault(),t.stopPropagation(),e.activeItemIndex.value!==null){let l=e.items.value[e.activeItemIndex.value];(p=d(l.dataRef.domRef))==null||p.click()}e.closeMenu(),C(d(e.buttonRef));break;case v.ArrowDown:return t.preventDefault(),t.stopPropagation(),e.goToItem(b.Next);case v.ArrowUp:return t.preventDefault(),t.stopPropagation(),e.goToItem(b.Previous);case v.Home:case v.PageUp:return t.preventDefault(),t.stopPropagation(),e.goToItem(b.First);case v.End:case v.PageDown:return t.preventDefault(),t.stopPropagation(),e.goToItem(b.Last);case v.Escape:t.preventDefault(),t.stopPropagation(),e.closeMenu(),y(()=>{var l;return(l=d(e.buttonRef))==null?void 0:l.focus({preventScroll:!0})});break;case v.Tab:t.preventDefault(),t.stopPropagation(),e.closeMenu(),y(()=>U(d(e.buttonRef),t.shiftKey?E.Previous:E.Next));break;default:t.key.length===1&&(e.search(t.key),c.value=setTimeout(()=>e.clearSearch(),350));break}}function m(t){switch(t.key){case v.Space:t.preventDefault();break}}let s=j(),u=R(()=>s!==null?(s.value&h.Open)===h.Open:e.menuState.value===0);return()=>{var t,p;let l={open:e.menuState.value===0},{id:r,...a}=n,o={"aria-activedescendant":e.activeItemIndex.value===null||(t=e.items.value[e.activeItemIndex.value])==null?void 0:t.id,"aria-labelledby":(p=d(e.buttonRef))==null?void 0:p.id,id:r,onKeydown:i,onKeyup:m,role:"menu",tabIndex:0,ref:e.itemsRef};return P({ourProps:o,theirProps:a,slot:l,attrs:g,slots:S,features:D.RenderStrategy|D.Static,visible:u.value,name:"MenuItems"})}}}),re=T({name:"MenuItem",inheritAttrs:!1,props:{as:{type:[Object,String],default:"template"},disabled:{type:Boolean,default:!1},id:{type:String,default:()=>`headlessui-menu-item-${w()}`}},setup(n,{slots:g,attrs:S,expose:I}){let e=k("MenuItem"),c=M(null);I({el:c,$el:c});let i=R(()=>e.activeItemIndex.value!==null?e.items.value[e.activeItemIndex.value].id===n.id:!1),m=R(()=>({disabled:n.disabled,textValue:"",domRef:c}));A(()=>{var a,o;let f=(o=(a=d(c))==null?void 0:a.textContent)==null?void 0:o.toLowerCase().trim();f!==void 0&&(m.value.textValue=f)}),A(()=>e.registerItem(n.id,m)),Q(()=>e.unregisterItem(n.id)),Z(()=>{e.menuState.value===0&&i.value&&e.activationTrigger.value!==0&&y(()=>{var a,o;return(o=(a=d(c))==null?void 0:a.scrollIntoView)==null?void 0:o.call(a,{block:"nearest"})})});function s(a){if(n.disabled)return a.preventDefault();e.closeMenu(),C(d(e.buttonRef))}function u(){if(n.disabled)return e.goToItem(b.Nothing);e.goToItem(b.Specific,n.id)}let t=H();function p(a){t.update(a)}function l(a){t.wasMoved(a)&&(n.disabled||i.value||e.goToItem(b.Specific,n.id,0))}function r(a){t.wasMoved(a)&&(n.disabled||i.value&&e.goToItem(b.Nothing))}return()=>{let{disabled:a}=n,o={active:i.value,disabled:a,close:e.closeMenu},{id:f,...x}=n;return P({ourProps:{id:f,ref:c,role:"menuitem",tabIndex:a===!0?void 0:-1,"aria-disabled":a===!0?!0:void 0,disabled:void 0,onClick:s,onFocus:u,onPointerenter:p,onMouseenter:p,onPointermove:l,onMousemove:l,onPointerleave:r,onMouseleave:r},theirProps:{...S,...x},slot:o,attrs:S,slots:g,name:"MenuItem"})}}});export{re as M,oe as S,ue as b,le as g};
