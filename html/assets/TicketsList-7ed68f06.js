import{a as u,o,c as Y,w as i,e as n,h as le,d as e,u as a,x as ne,s as E,E as q,C as O,k as z,q as R,m as re,O as ie,B as de,M as ce,r as I,b as r,z as ue,R as me,S as pe,F as D,f as P,v as $,t as p,T as K,n as U,P as S}from"./index-bfe6943f.js";import{_ as ve}from"./AppTopbar-1fff46f6.js";import{_ as ge}from"./pagination-7270df9d.js";import{X,k as L,J as he,$ as xe}from"./index-5b4677b6.js";import{S as _e}from"./vue-tailwind-datepicker-2621e104.js";import{c as h}from"./checkPermission.service-4ccc1117.js";import{_ as ye}from"./basicModal-efb13c60.js";import{S as fe}from"./transition-78618ab0.js";import{S as be,b as ke,M as we,g as Me}from"./menu-40a0975a.js";import{A as Ve,F as $e,B as Ce,E as Se}from"./listbox-d7028c3f.js";import"./dialog-abb0eee4.js";import"./hidden-bb6f5784.js";import"./use-tracked-pointer-c6b2df1d.js";import"./use-tree-walker-d2a209d4.js";import"./use-resolve-button-type-122dc2ec.js";import"./use-controllable-21042f24.js";const Ye={class:"p-6 space-y-6"},Te=e("label",{for:"ticket_name",class:"block text-sm leading-6 text-gray-900"},"Název požadavku:",-1),De={class:"mt-2"},ze=e("label",{for:"ticket_desc",class:"block text-sm leading-6 text-gray-900"},"Text požadavku:",-1),Ne={class:"mt-2"},je={class:"border-t p-5"},Be={class:"text-right space-x-3"},Fe=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Vytvořit ",-1),Pe={__name:"createTicketModal",emits:["reloadTickets"],setup(G,{expose:N,emit:j}){const w=u(!1),M=u(""),x=u("");function g(){w.value=!1}function _(){M.value="",x.value="",w.value=!0}function d(){z.post("/api/tickets",{subject:M.value,text:x.value}).then(V=>{R.success(V.data.message),j("reloadTickets",!0),g()}).catch(V=>{console.log(V)})}return N({openModal:_}),(V,c)=>(o(),Y(a(fe),{appear:"",show:w.value,as:"template",onClose:c[4]||(c[4]=y=>g())},{default:i(()=>[n(ye,null,{"modal-title":i(()=>[le("Založení nového požadavku")]),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:c[0]||(c[0]=y=>g())},[n(a(X),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[n(a(ne),{onSubmit:d},{default:i(({values:y})=>[e("div",Ye,[e("div",null,[Te,e("div",De,[n(a(E),{rules:"required",type:"text",name:"ticket_name",id:"ticket_name",modelValue:M.value,"onUpdate:modelValue":c[1]||(c[1]=v=>M.value=v),class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte název nového požadavku..."},null,8,["modelValue"]),n(a(q),{name:"ticket_name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[ze,e("div",Ne,[n(a(E),{as:"textarea",rules:"required",rows:"4",name:"ticket_desc",id:"ticket_desc",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 resize-none",placeholder:"Zadejte text Vašeho požadavku...",modelValue:x.value,"onUpdate:modelValue":c[2]||(c[2]=v=>x.value=v)},null,8,["modelValue"]),n(a(q),{name:"ticket_desc",class:"text-rose-400 text-sm block pt-1"})])])]),e("div",je,[e("div",Be,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:c[3]||(c[3]=O(v=>g(),["prevent"]))}," Zavřít "),Fe])])]),_:1})]),_:1})]),_:1},8,["show"]))}},Ue={class:"space-y-6"},Re={class:"px-0"},Ze={class:"bg-white border border-zinc-200/70 rounded-md p-5"},Ae={class:"sm:flex justify-between items-center gap-4"},Ee={class:"flex items-center gap-4"},qe={class:"w-72"},Ie={class:"w-44 text-left"},Ke={key:0,class:"text-gray-900"},Le={key:1,class:"text-gray-400"},Oe=["onClick"],Xe={class:"rounded-l-full w-60"},Ge={type:"button",class:"rounded-md border border-gray-300 block font-normal px-2.5 py-1.5 w-full text-sm leading-6 items-center justify-center space-x-2 sm:space-x-4 transition ease-out duration-300"},Je={class:"flex items-center w-full"},He={class:"w-6 h-6"},Qe={class:"flex-1"},We={key:0,class:"text-gray-900"},et={key:1,class:"text-gray-400"},tt={class:"flex items-center gap-4"},st=e("span",null,"Resetovat",-1),at=[st],ot={class:"flow-root bg-white border border-zinc-200/70 rounded-md"},lt={class:"sm:-mx-6 lg:-mx-8"},nt={class:"inline-block min-w-full align-middle sm:px-6 lg:px-8"},rt={key:0,class:"min-w-full divide-y divide-gray-200"},it={scope:"col",class:"py-4 pl-5 pr-3 text-center text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"},dt=["checked"],ct=e("th",{scope:"col",class:"py-4 pl-5 pr-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"}," Název požadavku ",-1),ut=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"},"Datum vytvoření ",-1),mt=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Zadavatel ",-1),pt=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"},"Stav ",-1),vt=e("th",{scope:"col",class:"py-4 pl-3 pr-5 text-left text-sm font-semibold text-gray-900 rounded-tr-md bg-gray-100/70"},null,-1),gt={key:0,class:"divide-y divide-gray-200"},ht={class:"whitespace-nowrap py-4 pl-5 pr-3 text-center"},xt=["onClick","value","checked"],_t={class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600"},yt={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},ft={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},bt={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},kt={key:0,class:"bg-red-200/75 text-red-700 rounded-lg px-3 py-1 font-semibold"},wt={key:1,class:"bg-amber-200/75 text-amber-700 rounded-lg px-3 py-1 font-semibold"},Mt={key:2,class:"space-x-4 space-y-4"},Vt={class:"bg-green-200/75 text-green-700 rounded-lg px-3 py-1 font-semibold"},$t={class:"text-green-700"},Ct={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},St={key:1},Yt=e("tr",null,[e("td",{colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},"Nebyly nalezeny žádné požadavky.")],-1),Tt=[Yt],Dt={class:"bg-gray-100/70"},zt={colspan:"7",class:"py-4 pl-5 px-3 text-sm text-gray-900 bg-gray-100/70"},Nt={class:"flex items-center gap-2"},jt=e("span",null,"Označené:",-1),Bt={class:"relative"},Ft={key:0,class:"block truncate"},Pt={key:1,class:"block h-6 text-gray-400"},Ut={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},Rt={key:1,class:"rounded-lg bg-main-color-300 px-4 py-2 text-sm text-white shadow-sm cursor-not-allowed"},ss={__name:"TicketsList",setup(G){const N=u(),j=re(),w=ie(),M=u(["tickets-parent","tickets-list"]),x=u({}),g=u({id:"",name:""}),_=u([]),d=u([]),V=[{id:1,name:"Vyřešit",permission:"tickets.close"}],c=u(),y=u(""),v=u(1),C=u({}),B=u({}),f=u(!1),m=u([]),J=u({date:"YYYY-MM-DD",month:"MM"});de(()=>{f.value=!0,Q(),b()}),ce(()=>w.perPage,(l,s)=>{f.value=!0,v.value=1,b()});function H(l){g.value=l}function Q(){z.get("/api/ticket-states").then(l=>{x.value=l.data.data}).catch(l=>{console.log(l)})}function b(){if(h.check("tickets.read")||h.check("tickets.master")){if(m.value[0])var l=[m.value[0],m.value[1]];else var l=["",""];if(h.check("tickets.master"))var s="";else var s=j.user.id;z.get("/api/tickets?page="+v.value+"&perpage="+w.perPage+"10&state_id="+g.value.id+"&user_id="+s+"&search="+y.value+"&date_from="+l[0]+"&date_to="+l[1]).then(k=>{C.value=k.data.data,B.value=k.data.meta,f.value=!1,_.value=[],k.data.data.filter((F,t)=>{_.value.push(F.id)})}).catch(k=>{console.log(k)})}else f.value=!1}function W(){m.value=[]}function ee(l){d.value.includes(l)?d.value=d.value.filter(s=>s!==l):d.value.push(l)}function te(){_.value.length!==d.value.length?(d.value=[],C.value.filter((l,s)=>{d.value.push(l.id)})):d.value=[]}function se(l){v.value=l,d.value=[],b()}function ae(){f.value=!0,v.value=1,y.value="",g.value={id:"",name:""},b()}function Z(){f.value=!0,v.value=1,d.value=[],b()}function oe(){d.value.length&&(h.check("tickets.close")||h.check("tickets.master"))?z.post("/api/tickets/close",{tickets:d.value}).then(l=>{R.success(l.data.message),d.value=[],b()}).catch(l=>{console.log(l)}):R.error("Nebyly vybrány žádné tikety")}return(l,s)=>{const k=I("router-link"),F=I("VueSpinner");return o(),r(D,null,[n(ve,{breadCrumbs:M.value},{topbarButtons:i(()=>[a(h).check("tickets.create")||a(h).check("tickets.master")?(o(),r("button",{key:0,onClick:s[0]||(s[0]=O(t=>l.$refs.createTicketRef.openModal(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},"Nový požadavek ")):$("",!0)]),_:1},8,["breadCrumbs"]),e("div",Ue,[e("div",Re,[e("div",Ze,[e("div",Ae,[e("div",Ee,[e("div",qe,[ue(e("input",{type:"text",name:"search",id:"search","onUpdate:modelValue":s[1]||(s[1]=t=>y.value=t),onKeyup:s[2]||(s[2]=pe(t=>Z(),["enter"])),class:"block w-full rounded-md py-1.5 text-gray-900 border border-gray-300 placeholder:text-gray-400 focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte hledaný výraz..."},null,544),[[me,y.value]])]),e("div",null,[n(a(Me),{as:"div",class:"relative inline-block text-left"},{default:i(()=>[e("div",null,[n(a(be),{class:"inline-flex w-full justify-center rounded-md bg-white px-4 py-2 text-sm font-medium text-white border border-gray-300"},{default:i(()=>[e("div",Ie,[g.value&&g.value.name?(o(),r("span",Ke,p(g.value.name),1)):(o(),r("span",Le,"Stav vyřešení..."))]),n(a(L),{class:"ml-2 -mr-1 h-5 w-5 text-violet-200 hover:text-violet-100","aria-hidden":"true"})]),_:1})]),n(K,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:i(()=>[n(a(ke),{class:"absolute right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:i(()=>[(o(!0),r(D,null,P(x.value,t=>(o(),r("div",{key:t.id,class:"px-1 py-1"},[n(a(we),null,{default:i(({active:T})=>[e("button",{onClick:A=>H(t),class:U([T?"bg-main-color-600 text-white":"text-gray-900","group flex w-full rounded-md px-2 py-2 text-sm text-left"])},p(t.name),11,Oe)]),_:2},1024)]))),128))]),_:1})]),_:1})]),_:1})]),e("div",null,[n(a(_e),{i18n:"cs","use-range":"",shortcuts:!1,modelValue:m.value,"onUpdate:modelValue":s[3]||(s[3]=t=>m.value=t),formatter:J.value},{default:i(({clear:t})=>[e("div",null,[e("div",Xe,[e("button",Ge,[e("div",Je,[e("div",He,[m.value&&m.value[0]&&m.value[1]?(o(),Y(a(X),{key:0,onClick:T=>W(),class:"h-6 w-6 text-gray-900","aria-hidden":"true"},null,8,["onClick"])):(o(),Y(a(he),{key:1,class:"h-6 w-6 text-gray-900","aria-hidden":"true"}))]),e("div",Qe,[m.value&&m.value[0]&&m.value[1]?(o(),r("span",We,[e("span",null,p(a(S)(m.value[0]).format("DD.MM.YYYY"))+" - "+p(a(S)(m.value[1]).format("DD.MM.YYYY")),1)])):(o(),r("span",et,[e("span",null,p(a(S)(m.value).format("DD.MM.YYYY")),1)]))])])])])])]),_:1},8,["modelValue","formatter"])])]),e("div",tt,[e("button",{onClick:s[4]||(s[4]=t=>ae()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},at),e("button",{onClick:s[5]||(s[5]=t=>Z()),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700"}," Filtrovat ")])])])]),e("div",null,[e("div",ot,[e("div",lt,[e("div",nt,[f.value==!1?(o(),r("table",rt,[e("thead",null,[e("tr",null,[e("th",it,[e("input",{id:"comments","aria-describedby":"comments-description",name:"comments",type:"checkbox",onClick:s[6]||(s[6]=t=>te()),checked:_.value.length&&_.value.length==d.value.length,class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent ring-transparent cursor-pointer"},null,8,dt)]),ct,ut,mt,pt,vt])]),C.value&&C.value.length?(o(),r("tbody",gt,[(o(!0),r(D,null,P(C.value,t=>(o(),r("tr",{key:t.id},[e("td",ht,[e("input",{id:"comments","aria-describedby":"comments-description",name:"comments",type:"checkbox",onClick:()=>{ee(t.id)},value:t.id,class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",checked:d.value.includes(t.id)},null,8,xt)]),e("td",_t,p(t.subject),1),e("td",yt,p(a(S)(t.created_at).format("DD.MM.YYYY")),1),e("td",ft,p(t.user.full_name),1),e("td",bt,[t.state.id==1?(o(),r("span",kt,p(t.state.name),1)):$("",!0),t.state.id==2||t.state.id==3?(o(),r("span",wt,p(t.state.name),1)):$("",!0),t.state.id==4?(o(),r("span",Mt,[e("span",Vt,p(t.state.name),1),e("span",$t,p(a(S)(t.updated_at).format("DD.MM.YYYY"))+" - "+p(t.user.full_name),1)])):$("",!0)]),e("td",Ct,[n(k,{to:{name:"ticket-detail",params:{id:t.id}}},{default:i(()=>[n(a(xe),{class:"ml-2 -mr-1 h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 p-2 rounded-lg","aria-hidden":"true"})]),_:2},1032,["to"])])]))),128))])):(o(),r("tbody",St,Tt)),e("tfoot",Dt,[e("tr",null,[e("td",zt,[e("div",Nt,[jt,n(a(Se),{as:"div",modelValue:c.value,"onUpdate:modelValue":s[7]||(s[7]=t=>c.value=t)},{default:i(()=>[e("div",Bt,[n(a(Ve),{class:"relative cursor-pointer rounded-lg bg-white w-48 py-1.5 pl-3 pr-10 text-left text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-main-color-600 sm:text-sm sm:leading-6"},{default:i(()=>[c.value&&c.value.name?(o(),r("span",Ft,p(c.value.name),1)):(o(),r("span",Pt,"vyberte akci...")),e("span",Ut,[n(a(L),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]),_:1}),n(K,{"leave-active-class":"transition ease-in duration-100","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:i(()=>[n(a($e),{class:"absolute bottom-11 z-10 max-h-60 w-48 overflow-auto rounded-md bg-white divide-y divide-gray-100 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:i(()=>[(o(),r(D,null,P(V,t=>n(a(Ce),{as:"template",key:t.id,value:t},{default:i(({active:T,selectedAction:A})=>[e("li",null,[a(h).check(t.permission)?(o(),r("span",{key:0,class:U([T?"bg-main-color-600 text-white":"text-gray-900","relative cursor-pointer select-none p-2 m-1 rounded-md block"])},[e("span",{class:U([A?"font-semibold":"font-normal","block truncate"])},p(t.name),3)],2)):$("",!0)])]),_:2},1032,["value"])),64))]),_:1})]),_:1})])]),_:1},8,["modelValue"]),c.value?(o(),r("button",{key:0,onClick:s[8]||(s[8]=t=>oe()),class:"rounded-lg bg-main-color-600 px-4 py-2 text-sm text-white shadow-sm hover:bg-main-color-700"}," Potvrdit ")):(o(),r("button",Rt," Potvrdit "))])])])])])):(o(),Y(F,{key:1,class:"mx-auto text-spinner-color",size:"40"}))])])]),B.value!==null?(o(),Y(ge,{key:0,meta:B.value,onSetPage:se,modelValue:v.value,"onUpdate:modelValue":s[9]||(s[9]=t=>v.value=t)},null,8,["meta","modelValue"])):$("",!0)])]),n(Pe,{ref_key:"createTicketRef",ref:N,onReloadTickets:s[10]||(s[10]=t=>b())},null,512)],64)}}};export{ss as default};
