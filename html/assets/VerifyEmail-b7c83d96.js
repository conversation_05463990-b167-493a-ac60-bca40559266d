import{A as _,a as u,l as h,v as x,r as V,o as j,b as E,B as a,C as l,d as e,e as o,w as r,u as d,t as q,F as C,x as w,E as z,z as k,h as g}from"./index-04cb9130.js";const B={class:"flex min-h-full flex-col justify-center py-12 sm:px-6 lg:px-8"},F={class:"mt-8 sm:mx-auto sm:w-full sm:max-w-md"},S={class:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10"},Z={class:"mt-2"},N={class:"mt-4 text-center text-sm text-gray-600"},P={class:"flex min-h-full flex-col justify-center py-12 sm:px-6 lg:px-8"},D={class:"mt-8 sm:mx-auto sm:w-full sm:max-w-md"},K={class:"mt-2 text-center text-sm text-gray-600"},O={class:"mt-8 text-center text-sm text-gray-600"},T={class:"text-left mt-8 sm:mx-auto sm:w-full sm:max-w-md"},U={class:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10"},Y={class:"mt-2"},$={class:"mt-4 text-center text-sm text-gray-600"},R={__name:"VerifyEmail",setup(A){const y=_(),i=u(""),c=u(!1),m=u(!1),v=u(!1),p=u("");y.query.token?(m.value=!0,c.value=!0,h.get("/api/auth/verify-email?token="+y.query.token).then(s=>{p.value="Emailová adresa byla ověřena, nyní se můžete přihlásit do systému.",v.value=!0,x.success(s.data.message)}).catch(s=>{p.value="Emailovou adresu se nepodařilo ověřit. Zkuste tuto akci opakovat zasláním nového ověřovacího emailu. Chyba: "+s.response.data.message,v.value=!1,x.error("Email se nepodařilo ověřit")}),m.value=!1):c.value=!1;function b(){h.post("/api/auth/verify-email",{email:i.value}).then(s=>{x.success(s.data.message)}).catch(s=>{x.error(s.response.data.message)})}return(s,t)=>{const f=V("router-link");return j(),E(C,null,[a(e("div",B,[t[7]||(t[7]=e("div",{class:"sm:mx-auto sm:w-full sm:max-w-md"},[e("img",{class:"mx-auto h-12 w-auto",src:"https://tailwindui.com/img/logos/mark.svg?color=main-color&shade=600",alt:"Your Company"}),e("h2",{class:"mt-6 text-center text-3xl font-bold tracking-tight text-gray-900"},"Ověření emailové adresy"),e("p",{class:"mt-2 text-center text-sm text-gray-600"}," Pro přístup do systému je nutné ověřit emailovou adresu. Zkontrolujte Vaši emailovou schránku (také složku spam) a email verifikujte. V případě, že Vám email k ověření nepřišel do pár minut od založení účtu, zažádejte si níže o nový. ")],-1)),e("div",F,[e("div",S,[o(d(k),{onSubmit:t[1]||(t[1]=n=>b()),class:"space-y-6",action:"#"},{default:r(()=>[e("div",null,[t[4]||(t[4]=e("label",{for:"email",class:"block text-sm font-medium leading-6 text-gray-900"},"Email",-1)),e("div",Z,[o(d(w),{rules:"required|email",modelValue:i.value,"onUpdate:modelValue":t[0]||(t[0]=n=>i.value=n),id:"email",name:"email",type:"email",autocomplete:"email",required:"",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},null,8,["modelValue"]),o(d(z),{name:"email",class:"text-rose-400 text-sm block pt-1"})])]),t[5]||(t[5]=e("div",null,[e("button",{type:"submit",class:"flex w-full justify-center rounded-md bg-main-color-600 py-2 px-3 text-sm font-semibold text-white shadow-sm hover:bg-main-color-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-main-color-600"},"Zaslat nový ověřovací email")],-1))]),_:1})])]),e("p",N,[o(f,{to:{name:"contact-support"},class:"text-sm font-semibold text-gray-900"},{default:r(()=>t[6]||(t[6]=[g(" Kontaktovat podporu "),e("span",{"aria-hidden":"true"},"→",-1)])),_:1})])],512),[[l,!c.value]]),a(e("div",P,[t[12]||(t[12]=e("div",{class:"sm:mx-auto sm:w-full sm:max-w-md"},[e("img",{class:"mx-auto h-12 w-auto",src:"https://tailwindui.com/img/logos/mark.svg?color=main-color&shade=600",alt:"Your Company"}),e("h2",{class:"mt-6 text-center text-3xl font-bold tracking-tight text-gray-900"},"Ověřování emailové adresy")],-1)),e("div",D,[a(e("p",K," Prosíme vyčkejte, právě oveřujeme Vaši emailovou adresu. ",512),[[l,m.value]]),a(e("p",{class:"mt-2 text-center text-sm text-gray-600"},q(p.value),513),[[l,!m.value]])]),a(e("p",O,[a(o(f,{to:{name:"login"},class:"font-medium text-main-color-600 hover:text-main-color-500"},{default:r(()=>t[8]||(t[8]=[g(" Přihlásit se ")])),_:1},512),[[l,v.value]]),a(e("div",T,[e("div",U,[o(d(k),{onSubmit:t[3]||(t[3]=n=>b()),class:"space-y-6"},{default:r(()=>[e("div",null,[t[9]||(t[9]=e("label",{for:"email",class:"block text-sm font-medium leading-6 text-gray-900"},"Email",-1)),e("div",Y,[o(d(w),{rules:"required|email",modelValue:i.value,"onUpdate:modelValue":t[2]||(t[2]=n=>i.value=n),id:"email",name:"email",type:"email",autocomplete:"email",required:"",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},null,8,["modelValue"])])]),t[10]||(t[10]=e("div",null,[e("button",{type:"submit",class:"flex w-full justify-center rounded-md bg-main-color-600 py-2 px-3 text-sm font-semibold text-white shadow-sm hover:bg-main-color-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-main-color-600"},"Zaslat nový ověřovací email")],-1))]),_:1})])],512),[[l,!v.value]])],512),[[l,!m.value]]),e("p",$,[o(f,{to:{name:"contact-support"},class:"text-sm font-semibold text-gray-900"},{default:r(()=>t[11]||(t[11]=[g(" Kontaktovat podporu "),e("span",{"aria-hidden":"true"},"→",-1)])),_:1})])],512),[[l,c.value]])],64)}}};export{R as default};
