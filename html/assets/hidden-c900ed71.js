import{ab as I,a9 as D,F as L,j as H,I as k,p as M,L as C,a as U,J as R,H as _}from"./index-c2402147.js";function y(e,t,...n){if(e in t){let o=t[e];return typeof o=="function"?o(...n):o}let r=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(o=>`"${o}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,y),r}var W=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(W||{}),G=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(G||{});function B({visible:e=!0,features:t=0,ourProps:n,theirProps:r,...o}){var l;let a=N(r,n),i=Object.assign(o,{props:a});if(e||t&2&&a.static)return b(i);if(t&1){let m=(l=a.unmount)==null||l?0:1;return y(m,{[0](){return null},[1](){return b({...o,props:{...a,hidden:!0,style:{display:"none"}}})}})}return b(i)}function b({props:e,attrs:t,slots:n,slot:r,name:o}){var l,a;let{as:i,...m}=X(e,["unmount","static"]),u=(l=n.default)==null?void 0:l.call(n,r),s={};if(r){let p=!1,h=[];for(let[d,c]of Object.entries(r))typeof c=="boolean"&&(p=!0),c===!0&&h.push(d);p&&(s["data-headlessui-state"]=h.join(" "))}if(i==="template"){if(u=O(u??[]),Object.keys(m).length>0||Object.keys(t).length>0){let[p,...h]=u??[];if(!q(p)||h.length>0)throw new Error(['Passing props on "template"!',"",`The current component <${o} /> is rendering a "template".`,"However we need to passthrough the following props:",Object.keys(m).concat(Object.keys(t)).map(f=>f.trim()).filter((f,v,T)=>T.indexOf(f)===v).sort((f,v)=>f.localeCompare(v)).map(f=>`  - ${f}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "template".',"Render a single element as the child so that we can forward the props onto that element."].map(f=>`  - ${f}`).join(`
`)].join(`
`));let d=N((a=p.props)!=null?a:{},m),c=I(p,d);for(let f in d)f.startsWith("on")&&(c.props||(c.props={}),c.props[f]=d[f]);return c}return Array.isArray(u)&&u.length===1?u[0]:u}return D(i,Object.assign({},m,s),{default:()=>u})}function O(e){return e.flatMap(t=>t.type===L?O(t.children):[t])}function N(...e){if(e.length===0)return{};if(e.length===1)return e[0];let t={},n={};for(let r of e)for(let o in r)o.startsWith("on")&&typeof r[o]=="function"?(n[o]!=null||(n[o]=[]),n[o].push(r[o])):t[o]=r[o];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(n).map(r=>[r,void 0])));for(let r in n)Object.assign(t,{[r](o,...l){let a=n[r];for(let i of a){if(o instanceof Event&&o.defaultPrevented)return;i(o,...l)}}});return t}function de(e){let t=Object.assign({},e);for(let n in t)t[n]===void 0&&delete t[n];return t}function X(e,t=[]){let n=Object.assign({},e);for(let r of t)r in n&&delete n[r];return n}function q(e){return e==null?!1:typeof e.type=="string"||typeof e.type=="object"||typeof e.type=="function"}let J=0;function K(){return++J}function fe(){return K()}var V=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(V||{});function S(e){var t;return e==null||e.value==null?null:(t=e.value.$el)!=null?t:e.value}let A=Symbol("Context");var Y=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(Y||{});function pe(){return z()!==null}function z(){return H(A,null)}function me(e){k(A,e)}var Q=Object.defineProperty,Z=(e,t,n)=>t in e?Q(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,E=(e,t,n)=>(Z(e,typeof t!="symbol"?t+"":t,n),n);class ee{constructor(){E(this,"current",this.detect()),E(this,"currentId",0)}set(t){this.current!==t&&(this.currentId=0,this.current=t)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window>"u"||typeof document>"u"?"server":"client"}}let j=new ee;function x(e){if(j.isServer)return null;if(e instanceof Node)return e.ownerDocument;if(e!=null&&e.hasOwnProperty("value")){let t=S(e);if(t)return t.ownerDocument}return document}let g=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var te=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e))(te||{}),ne=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(ne||{}),re=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(re||{});function P(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(g)).sort((t,n)=>Math.sign((t.tabIndex||Number.MAX_SAFE_INTEGER)-(n.tabIndex||Number.MAX_SAFE_INTEGER)))}var $=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))($||{});function F(e,t=0){var n;return e===((n=x(e))==null?void 0:n.body)?!1:y(t,{[0](){return e.matches(g)},[1](){let r=e;for(;r!==null;){if(r.matches(g))return!0;r=r.parentElement}return!1}})}function he(e){let t=x(e);M(()=>{t&&!F(t.activeElement,0)&&oe(e)})}function oe(e){e==null||e.focus({preventScroll:!0})}let le=["textarea","input"].join(",");function ie(e){var t,n;return(n=(t=e==null?void 0:e.matches)==null?void 0:t.call(e,le))!=null?n:!1}function ae(e,t=n=>n){return e.slice().sort((n,r)=>{let o=t(n),l=t(r);if(o===null||l===null)return 0;let a=o.compareDocumentPosition(l);return a&Node.DOCUMENT_POSITION_FOLLOWING?-1:a&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function ve(e,t){return ue(P(),t,{relativeTo:e})}function ue(e,t,{sorted:n=!0,relativeTo:r=null,skipElements:o=[]}={}){var l;let a=(l=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e==null?void 0:e.ownerDocument)!=null?l:document,i=Array.isArray(e)?n?ae(e):e:P(e);o.length>0&&i.length>1&&(i=i.filter(c=>!o.includes(c))),r=r??a.activeElement;let m=(()=>{if(t&5)return 1;if(t&10)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),u=(()=>{if(t&1)return 0;if(t&2)return Math.max(0,i.indexOf(r))-1;if(t&4)return Math.max(0,i.indexOf(r))+1;if(t&8)return i.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),s=t&32?{preventScroll:!0}:{},p=0,h=i.length,d;do{if(p>=h||p+h<=0)return 0;let c=u+p;if(t&16)c=(c+h)%h;else{if(c<0)return 3;if(c>=h)return 1}d=i[c],d==null||d.focus(s),p+=m}while(d!==a.activeElement);return t&6&&ie(d)&&d.select(),d.hasAttribute("tabindex")||d.setAttribute("tabindex","0"),2}function w(e,t,n){j.isServer||C(r=>{document.addEventListener(e,t,n),r(()=>document.removeEventListener(e,t,n))})}function be(e,t,n=R(()=>!0)){function r(l,a){if(!n.value||l.defaultPrevented)return;let i=a(l);if(i===null||!i.getRootNode().contains(i))return;let m=function u(s){return typeof s=="function"?u(s()):Array.isArray(s)||s instanceof Set?s:[s]}(e);for(let u of m){if(u===null)continue;let s=u instanceof HTMLElement?u:S(u);if(s!=null&&s.contains(i)||l.composed&&l.composedPath().includes(s))return}return!F(i,$.Loose)&&i.tabIndex!==-1&&l.preventDefault(),t(l,i)}let o=U(null);w("mousedown",l=>{var a,i;n.value&&(o.value=((i=(a=l.composedPath)==null?void 0:a.call(l))==null?void 0:i[0])||l.target)},!0),w("click",l=>{o.value&&(r(l,()=>o.value),o.value=null)},!0),w("blur",l=>r(l,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}var se=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(se||{});let we=_({name:"Hidden",props:{as:{type:[Object,String],default:"div"},features:{type:Number,default:1}},setup(e,{slots:t,attrs:n}){return()=>{let{features:r,...o}=e,l={"aria-hidden":(r&2)===2?!0:void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(r&4)===4&&(r&2)!==2&&{display:"none"}}};return B({ourProps:l,theirProps:o,slot:{},attrs:n,slots:t,name:"Hidden"})}}});export{pe as C,ve as D,$ as F,B as H,ae as I,de as K,W as N,ue as O,F as S,X as T,V as a,se as b,me as c,te as d,G as e,we as f,j as g,oe as h,ne as i,Y as l,x as m,S as o,z as p,fe as t,y as u,he as v,be as y};
