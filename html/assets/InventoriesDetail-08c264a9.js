import{D as re,a as m,j as T,C as X,o as s,c as q,w as h,e as l,h as S,d as e,u as r,G as w,n as U,k as V,s as E,r as Z,b as o,F as D,f as F,t as M,x as y,v as ae,E as le,z as ie,M as de}from"./index-c2402147.js";import{_ as ue}from"./AppTopbar-cd1b5f57.js";import{c as p}from"./checkPermission.service-94919896.js";import{$ as A,i as G,X as ne,g as me,A as ce,o as ve,s as pe}from"./index-a54879fc.js";import{_ as H}from"./basicModal-cef5a3ee.js";import{X as j,E as L}from"./index-945c46cd.js";import{S as Y}from"./transition-b398339f.js";import"./dialog-b3ac1cb1.js";import"./hidden-c900ed71.js";const xe={class:"p-6 gap-4"},ye={class:"col-span-2"},be={class:"grid grid-cols-3 gap-4 pt-2"},ge={class:"flex items-center gap-1"},he={class:"flex items-center gap-1"},fe={class:"flex items-center gap-1"},ke={class:"border-t p-5"},_e={class:"text-right space-x-3"},we={__name:"editItemStateModal",props:{inventoryItem:{type:Object,required:!0}},emits:["reloadItems"],setup(O,{expose:z,emit:N}){const C=O;re(()=>C.inventoryItem,(a,t)=>{b.value=C.inventoryItem.pivot.state});const b=m(""),$=N,f=m(!1);T("debugModeGlobalVar");const n=m(!1);X(()=>{});function g(){f.value=!1}function u(){f.value=!0,b.value=""}async function c(){p.check("inventories.items")||p.check("property.master")?await V.post("api/inventories/items/"+C.inventoryItem.pivot.id+"/update",{state:b.value}).then(a=>{E.success(a.data.message)}).catch(a=>{E.error(a.message)}):n.value=!1,g(),$("reloadInventory",!0)}return z({openModal:u}),(a,t)=>(s(),q(r(Y),{appear:"",show:f.value,as:"template",onClose:t[6]||(t[6]=I=>g())},{default:h(()=>[l(H,{size:"sm"},{"modal-title":h(()=>t[7]||(t[7]=[S("Změna stavu položky")])),"modal-close-button":h(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:t[0]||(t[0]=I=>g())},[l(r(j),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":h(()=>[e("div",xe,[e("div",ye,[t[11]||(t[11]=e("label",{for:"item-state",class:"block text-sm font-normal leading-6 text-gray-900"},"Nový stav položky:",-1)),e("div",be,[e("button",{onClick:t[1]||(t[1]=w(I=>b.value="UNPROCESSED",["prevent"])),class:U([{"bg-amber-600 text-white":b.value==="UNPROCESSED"},"border border-amber-600 px-3 py-2 rounded-md text-sm text-amber-600 inline-block hover:bg-amber-600 hover:text-white duration-150"])},[e("div",ge,[l(r(A),{class:"h-4 w-4 mr-1","aria-hidden":"true"}),t[8]||(t[8]=e("span",null,"Položka nepotvrzena",-1))])],2),e("button",{onClick:t[2]||(t[2]=w(I=>b.value="CONFIRMED",["prevent"])),class:U([{"bg-green-600 text-white":b.value==="CONFIRMED"},"border border-green-600 px-3 py-2 rounded-md text-sm text-green-600 inline-block hover:bg-green-600 hover:text-white duration-150"])},[e("div",he,[l(r(G),{class:"h-4 w-4 mr-1","aria-hidden":"true"}),t[9]||(t[9]=e("span",null,"Položka potvrzena",-1))])],2),e("button",{onClick:t[3]||(t[3]=w(I=>b.value="NOT_FOUND",["prevent"])),class:U([{"bg-red-600 text-white":b.value==="NOT_FOUND"},"border border-red-600 px-3 py-2 rounded-md text-sm text-red-600 inline-block hover:bg-red-600 hover:text-white duration-150"])},[e("div",fe,[l(r(j),{class:"h-4 w-4 mr-1","aria-hidden":"true"}),t[10]||(t[10]=e("span",null,"Položka nenalezena",-1))])],2)])])]),e("div",ke,[e("div",_e,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:t[4]||(t[4]=w(I=>g(),["prevent"]))}," Zavřít "),e("button",{onClick:t[5]||(t[5]=w(I=>c(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-regreend-600",type:"submit"}," Změnit ")])])]),_:1})]),_:1},8,["show"]))}},$e={class:"border-t p-5"},Ce={class:"text-right space-x-3"},Ie={__name:"processInventoryModal",props:{inventory:{type:Object,required:!0}},emits:["reloadItems"],setup(O,{expose:z,emit:N}){const C=O,b=N,$=m(!1);T("debugModeGlobalVar");const f=m(!1);X(()=>{});function n(){$.value=!1}function g(){$.value=!0}async function u(){p.check("inventories.process")||p.check("property.master")?await V.post("api/inventories/"+C.inventory.id+"/process").then(c=>{E.success(c.data.message)}).catch(c=>{E.error(c.message)}):f.value=!1,n(),b("reloadInventory",!0)}return z({openModal:g}),(c,a)=>(s(),q(r(Y),{appear:"",show:$.value,as:"template",onClose:a[3]||(a[3]=t=>n())},{default:h(()=>[l(H,{size:"sm"},{"modal-title":h(()=>a[4]||(a[4]=[S("Uzavřít inventuru")])),"modal-close-button":h(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:a[0]||(a[0]=t=>n())},[l(r(j),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":h(()=>[a[5]||(a[5]=e("div",{class:"p-6 gap-4"},[e("p",null,"Opravdu si přejete inventuru uzavřít?")],-1)),e("div",$e,[e("div",Ce,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:a[1]||(a[1]=w(t=>n(),["prevent"]))}," Zavřít "),e("button",{onClick:a[2]||(a[2]=w(t=>u(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-regreend-600",type:"submit"}," Uzavřít ")])])]),_:1})]),_:1},8,["show"]))}},Me={key:0,class:"px-6 pt-6"},Se={key:0,class:"space-y-3"},Ee={key:1,class:"p-6"},ze={class:"mt-2"},Ne={key:2,class:"p-6"},Oe={class:"px-6 pb-6"},Pe={key:0,class:"space-y-3 mb-3"},Re=["onClick"],De={key:0,class:"mb-2"},Ve={class:"text-sm font-medium"},je={class:"mt-1"},Ue={key:1,class:"space-y-3"},Fe={class:"border-t p-5"},qe={class:"text-right space-x-3"},Be={__name:"scanInventoryModal",props:{inventory:{type:Object,required:!0}},emits:["reloadGroups"],setup(O,{expose:z,emit:N}){const C=O,b=N,$=m(!1),f=m(!1),n=m(""),g=m(!1),u=m(null),c=m(null);m(null);function a(){$.value=!1}async function t(){$.value=!0,n.value=null,c.value=null,g.value=!1,u.value=null}re(n,()=>{g.value=!1});async function I(){g.value=!1,f.value=!0,await V.post("api/inventories/items/multiple/check",{inventory_id:C.inventory.id,evidence_numbers:n.value}).then(k=>{E.success(k.data.message);const i=Array.from(new Map(k.data.data.items.map(_=>[_.evidence_number,_])).values());u.value={errors:k.data.data.errors,items:i},B()}).catch(k=>{console.log(k)}),f.value=!1}async function B(){var i,_,d;if((i=u.value)!=null&&i.errors&&((d=(_=u.value)==null?void 0:_.items)==null?void 0:d.length)==0){g.value=!1;return}const k=u.value.items.some(P=>P.errors!==null);g.value=!k}function v(k,i){var _;if((_=u.value)!=null&&_.items&&(u.value.items=u.value.items.filter(d=>d.id!==k)),n.value){let d=n.value.split(";");d=d.filter(P=>P!==i.toString()),n.value=d.join(";")}}async function J(){var _;if(!((_=u.value)!=null&&_.items))return;const k=u.value.items.filter(d=>d.errors===null).map(d=>d.id);if(k.length===0){E.warning("Žádné položky bez chyb k odeslání.");return}const i={inventory_id:C.inventory.id,items:k};await V.post("api/inventories/items/multiple/update",i).then(d=>{c.value=d.data,d.data.success?(E.success(d.data.message),a(),b("reloadInventory",!0)):(E.error(d.data.message),console.log(d.data.data))}).catch(d=>{console.log(d),c.value=d.response.data.data.errors})}return z({openModal:t}),(k,i)=>{const _=Z("VueSpinner");return s(),q(r(Y),{appear:"",show:$.value,as:"template",onClose:i[5]||(i[5]=d=>a())},{default:h(()=>[l(H,{size:"sm"},{"modal-title":h(()=>i[6]||(i[6]=[S("Hromadné potvrzení položek")])),"modal-close-button":h(()=>[e("button",{type:"button",tabindex:"-1",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:i[0]||(i[0]=d=>a())},[l(r(j),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":h(()=>{var d,P,K,Q,W,ee,te,se,oe;return[e("div",null,[c.value?(s(),o("div",Me,[c.value&&((d=c.value)==null?void 0:d.length)>0?(s(),o("div",Se,[(s(!0),o(D,null,F(c.value,(x,R)=>(s(),o("div",null,[(s(),o("div",{key:R,class:"text-sm border-2 rounded-md border-red-600 p-4 relative"},[e("p",null,[l(r(L),{class:"h-5 w-5 text-red-600 inline mr-2","aria-hidden":"true"}),S(M(x),1)])]))]))),256))])):y("",!0)])):y("",!0),f.value?(s(),o("div",Ne,[l(_,{class:"mx-auto text-spinner-color",size:"40"})])):(s(),o("div",Ee,[e("div",null,[i[8]||(i[8]=e("label",{for:"itemsCodes",class:"block text-sm font-normal leading-6 text-gray-900"},"Evidenční čísla:",-1)),e("div",ze,[l(r(ae),{modelValue:n.value,"onUpdate:modelValue":i[1]||(i[1]=x=>n.value=x),as:"textarea",name:"itemsCodes",id:"itemsCodes",cols:"30",autofocus:"",rows:"3",class:"resize-none block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Načtěte položky, nebo vložte evidenční čísla..."},null,8,["modelValue"]),l(r(le),{name:"itemsCodes",class:"text-rose-400 text-sm block pt-1"}),i[7]||(i[7]=e("p",{class:"text-xs text-gray-600 mt-2"},[S("Vložte evidenční čísla ve formátu se středníkem na konci: "),e("strong",null,"100002;100003;100004;")],-1))])])])),e("div",Oe,[(P=u.value)!=null&&P.items&&((Q=(K=u.value)==null?void 0:K.items)==null?void 0:Q.length)>0?(s(),o("div",Pe,[(s(!0),o(D,null,F((W=u.value)==null?void 0:W.items,(x,R)=>(s(),o("div",null,[(s(),o("div",{key:R,class:U(["text-sm rounded-md p-4 relative border-2",x.errors?"border-orange-500":"border-green-600"])},[e("button",{type:"button",class:"flex justify-center items-center rounded-md border border-transparent bg-neutral-200 shadow-sm hover:bg-neutral-300 absolute top-2 right-2",onClick:Nt=>v(x.id,x.evidence_number)},[l(r(j),{class:"mx-auto h-6 w-6 p-1 text-neutral-600","aria-hidden":"true"})],8,Re),x.errors?(s(),o("p",De,[l(r(L),{class:"h-5 w-5 text-orange-500 inline mr-2","aria-hidden":"true"}),S(M(x.errors),1)])):y("",!0),e("p",Ve,M(x.name),1),e("p",je,[i[9]||(i[9]=e("span",{class:"font-semibold"},"EČ:",-1)),S(" "+M(x.evidence_number),1)])],2))]))),256))])):y("",!0),(ee=u.value)!=null&&ee.errors&&((se=(te=u.value)==null?void 0:te.errors)==null?void 0:se.length)>0?(s(),o("div",Ue,[(s(!0),o(D,null,F((oe=u.value)==null?void 0:oe.errors,(x,R)=>(s(),o("div",null,[(s(),o("div",{key:R,class:"text-sm rounded-md"},[e("p",null,[l(r(L),{class:"h-5 w-5 text-red-600 inline mr-2","aria-hidden":"true"}),S(M(x.message),1)])]))]))),256))])):y("",!0)]),e("div",Fe,[e("div",qe,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:i[2]||(i[2]=w(x=>a(),["prevent"]))}," Zavřít "),g.value?(s(),o("button",{key:0,onClick:i[3]||(i[3]=w(x=>J(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Potvrdit ")):(s(),o("button",{key:1,onClick:i[4]||(i[4]=w(x=>I(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Ověřit položky "))])])])]}),_:1})]),_:1},8,["show"])}}},Le={class:"space-y-6"},Ze={class:"px-0"},Ae={class:"bg-white border border-zinc-200/70 rounded-md p-5 flex justify-between items-center"},Ge={class:"sm:flex items-center gap-10"},Te={key:0},Xe={key:0,class:"border border-amber-600 px-2 py-1 rounded-md text-xs text-amber-600 inline-block"},He={class:"flex"},Ye={key:1,class:"border border-green-600 px-2 py-1 rounded-md text-xs text-green-600 inline-block"},Je={class:"flex"},Ke={key:2,class:"border border-red-600 px-2 py-1 rounded-md text-xs text-red-600 inline-block"},Qe={class:"flex"},We={key:0,class:"flex items-center text-sm"},et={key:0},tt={key:1,class:"text-amber-600"},st={key:1,class:"flex items-center"},ot={key:0},nt={key:1,class:"text-amber-600"},rt={key:0},at={class:"flow-root bg-white border border-zinc-200/70 rounded-md"},lt={class:"sm:-mx-6 lg:-mx-8"},it={class:"inline-block min-w-full align-middle sm:px-6 lg:px-8"},dt={key:0,class:"min-w-full divide-y divide-gray-200"},ut={key:0,scope:"col",class:"py-4 pl-5 pr-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"},mt={key:0,class:"divide-y divide-gray-200"},ct={class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600 w-72"},vt={key:0},pt={key:1},xt={class:"whitespace-nowrap py-4 pl-3 pr-3 text-sm text-gray-600"},yt={key:0},bt={key:1},gt={class:"whitespace-nowrap py-4 pl-3 pr-3 text-sm text-gray-600"},ht={key:0},ft={key:0,class:"border border-amber-600 px-2 py-1 rounded-md text-xs text-amber-600 inline-block"},kt={class:"flex"},_t={key:1,class:"border border-green-600 px-2 py-1 rounded-md text-xs text-green-600 inline-block"},wt={class:"flex"},$t={key:2,class:"border border-red-600 px-2 py-1 rounded-md text-xs text-red-600 inline-block"},Ct={class:"flex"},It={key:1},Mt={key:0,class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600 text-end w-10"},St=["onClick"],Et={key:1},zt={class:"flex justify-center"},Bt={__name:"InventoriesDetail",setup(O){const z=m(""),N=m(""),C=m(""),b=ie();de(),T("debugModeGlobalVar");const $=m(["inventories","inventories-detail"]),f=m(!1),n=m({});m(1);const g=m(""),u=m();X(()=>{c()});async function c(){f.value=!0,await V.get("/api/inventories/"+b.params.id).then(a=>{g.value=a.data.meta,n.value=a.data.data}).catch(a=>{console.log(a)}),f.value=!1}return(a,t)=>{const I=Z("router-link"),B=Z("VueSpinner");return s(),o(D,null,[l(ue,{breadCrumbs:$.value},{topbarButtons:h(()=>[l(I,{to:{name:"inventories"},class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},{default:h(()=>t[5]||(t[5]=[e("div",{class:"flex"},[e("span",null,"Zpět")],-1)])),_:1})]),_:1},8,["breadCrumbs"]),e("div",Le,[e("div",Ze,[e("div",Ae,[e("div",Ge,[e("div",null,[n.value.state?(s(),o("div",Te,[n.value.state=="OPEN"?(s(),o("div",Xe,[e("div",He,[l(r(A),{class:"h-4 w-4 text-amber-600 mr-1","aria-hidden":"true"}),t[6]||(t[6]=e("span",null,"Čeká na potvrzení",-1))])])):n.value.state=="SUCCESSFULLY_CLOSED"?(s(),o("div",Ye,[e("div",Je,[l(r(G),{class:"h-4 w-4 text-green-600 mr-1","aria-hidden":"true"}),t[7]||(t[7]=e("span",null,"Úspěšně uzavřeno",-1))])])):n.value.state=="FAILED_CLOSED"?(s(),o("div",Ke,[e("div",Qe,[l(r(ne),{class:"h-4 w-4 text-red-600 mr-1","aria-hidden":"true"}),t[8]||(t[8]=e("span",null,"Neúspěšně uzavřeno",-1))])])):y("",!0)])):y("",!0)]),e("div",null,[e("div",null,[n.value.user?(s(),o("span",We,[l(r(me),{class:"h-5 w-5 mr-2","aria-hidden":"true"}),n.value.user.full_name?(s(),o("span",et,M(n.value.user.full_name),1)):(s(),o("span",tt,M(n.value.user.email)+" - Uživateli chybí jméno ",1))])):y("",!0),n.value.room?(s(),o("span",st,[l(r(ce),{class:"h-5 w-5 mr-2","aria-hidden":"true"}),n.value.room.name?(s(),o("span",ot,M(n.value.room.name),1)):(s(),o("span",nt," Místnosti chybí jméno "))])):y("",!0)])])]),e("div",null,[(r(p).check("inventories.edit")||r(p).check("property.master"))&&n.value.state=="OPEN"?(s(),o("div",rt,[(r(p).check("inventories.edit")||r(p).check("property.master"))&&n.value.state=="OPEN"?(s(),o("button",{key:0,onClick:t[0]||(t[0]=w(v=>a.$refs.scanInventoryRef.openModal(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},"Potvrzení čtečkou ")):y("",!0)])):y("",!0)])])]),e("div",null,[e("div",at,[e("div",lt,[e("div",it,[f.value==!1?(s(),o("table",dt,[e("thead",null,[e("tr",null,[t[9]||(t[9]=e("th",{scope:"col",class:"py-4 pl-5 pr-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"}," Ev. číslo ",-1)),t[10]||(t[10]=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Název ",-1)),t[11]||(t[11]=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Stav ",-1)),r(p).check("inventories.edit")||r(p).check("inventories.delete")||r(p).check("property.master")?(s(),o("th",ut)):y("",!0)])]),n.value&&n.value.items&&n.value.items.length?(s(),o("tbody",mt,[(s(!0),o(D,null,F(n.value.items,v=>(s(),o("tr",{key:v.id},[e("td",ct,[v.evidence_number?(s(),o("span",vt,M(v.evidence_number),1)):(s(),o("span",pt,"-"))]),e("td",xt,[v.name?(s(),o("span",yt,M(v.name),1)):(s(),o("span",bt,"-"))]),e("td",gt,[v.pivot.state?(s(),o("div",ht,[v.pivot.state=="UNPROCESSED"?(s(),o("div",ft,[e("div",kt,[l(r(A),{class:"h-4 w-4 text-amber-600 mr-1","aria-hidden":"true"}),t[12]||(t[12]=e("span",null,"Položka nepotvrzena",-1))])])):v.pivot.state=="CONFIRMED"?(s(),o("div",_t,[e("div",wt,[l(r(G),{class:"h-4 w-4 text-green-600 mr-1","aria-hidden":"true"}),t[13]||(t[13]=e("span",null,"Položka potvrzena",-1))])])):v.pivot.state=="NOT_FOUND"?(s(),o("div",$t,[e("div",Ct,[l(r(ne),{class:"h-4 w-4 text-red-600 mr-1","aria-hidden":"true"}),t[14]||(t[14]=e("span",null,"Položka nenalezena",-1))])])):y("",!0)])):(s(),o("span",It,"-"))]),r(p).check("inventories.items")||r(p).check("property.master")?(s(),o("td",Mt,[(r(p).check("inventories.items")||r(p).check("property.master"))&&n.value.state=="OPEN"?(s(),o("button",{key:0,onClick:w(J=>(u.value=v,a.$refs.editItemStateRef.openModal()),["prevent"])},[l(r(ve),{class:"h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 p-2 rounded-lg mr-2","aria-hidden":"true"})],8,St)):y("",!0)])):y("",!0)]))),128))])):(s(),o("tbody",Et,t[15]||(t[15]=[e("tr",null,[e("td",{colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},"Nebyly nalezeny žádné položky.")],-1)])))])):(s(),q(B,{key:1,class:"mx-auto text-spinner-color",size:"40"}))])])])]),e("div",zt,[(r(p).check("inventories.process")||r(p).check("property.master"))&&n.value.state=="OPEN"?(s(),o("button",{key:0,onClick:t[1]||(t[1]=w(v=>a.$refs.processInventoryRef.openModal(),["prevent"])),class:"flex items-center gap-3 rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},[l(r(pe),{class:"h-6 w-6 text-white","aria-hidden":"true"}),t[16]||(t[16]=e("span",null,"Uzavřít inventuru",-1))])):y("",!0)])]),l(we,{ref_key:"editItemStateRef",ref:z,inventoryItem:u.value,onReloadInventory:t[2]||(t[2]=v=>c())},null,8,["inventoryItem"]),l(Ie,{ref_key:"processInventoryRef",ref:N,inventory:n.value,onReloadInventory:t[3]||(t[3]=v=>c())},null,8,["inventory"]),l(Be,{ref_key:"scanInventoryRef",ref:C,inventory:n.value,onReloadInventory:t[4]||(t[4]=v=>c())},null,8,["inventory"])],64)}}};export{Bt as default};
