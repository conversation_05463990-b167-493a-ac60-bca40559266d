import{g as ie,m as G,f as X,b as Z,H as D,h as A,o as h,u as Y,O as V,d as F,i as Pe,t as ne,p as Ae,l as W,y as Oe,N as oe,a as De}from"./hidden-c900ed71.js";import{L as M,a as m,H as R,J as c,C as O,K as k,a9 as L,F as Re,D as _,I as B,j as N,X as ke,N as Ce,ad as Me,p as He}from"./index-c2402147.js";function se(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function xe(){return/Android/gi.test(window.navigator.userAgent)}function ut(){return se()||xe()}function je(e,n,t){ie.isServer||M(l=>{window.addEventListener(e,n,t),l(()=>window.removeEventListener(e,n,t))})}var j=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(j||{});function Be(){let e=m(0);return je("keydown",n=>{n.key==="Tab"&&(e.value=n.shiftKey?1:0)}),e}function de(e,n,t,l){ie.isServer||M(a=>{e=e??window,e.addEventListener(n,t,l),a(()=>e.removeEventListener(n,t,l))})}function ce(e){typeof queueMicrotask=="function"?queueMicrotask(e):Promise.resolve().then(e).catch(n=>setTimeout(()=>{throw n}))}function fe(e){if(!e)return new Set;if(typeof e=="function")return new Set(e());let n=new Set;for(let t of e.value){let l=h(t);l instanceof HTMLElement&&n.add(l)}return n}var pe=(e=>(e[e.None=1]="None",e[e.InitialFocus=2]="InitialFocus",e[e.TabLock=4]="TabLock",e[e.FocusLock=8]="FocusLock",e[e.RestoreFocus=16]="RestoreFocus",e[e.All=30]="All",e))(pe||{});let H=Object.assign(R({name:"FocusTrap",props:{as:{type:[Object,String],default:"div"},initialFocus:{type:Object,default:null},features:{type:Number,default:30},containers:{type:[Object,Function],default:m(new Set)}},inheritAttrs:!1,setup(e,{attrs:n,slots:t,expose:l}){let a=m(null);l({el:a,$el:a});let o=c(()=>G(a)),r=m(!1);O(()=>r.value=!0),k(()=>r.value=!1),qe({ownerDocument:o},c(()=>r.value&&!!(e.features&16)));let u=Ie({ownerDocument:o,container:a,initialFocus:c(()=>e.initialFocus)},c(()=>r.value&&!!(e.features&2)));We({ownerDocument:o,container:a,containers:e.containers,previousActiveElement:u},c(()=>r.value&&!!(e.features&8)));let i=Be();function s(f){let w=h(a);w&&(y=>y())(()=>{Y(i.value,{[j.Forwards]:()=>{V(w,F.First,{skipElements:[f.relatedTarget]})},[j.Backwards]:()=>{V(w,F.Last,{skipElements:[f.relatedTarget]})}})})}let p=m(!1);function T(f){f.key==="Tab"&&(p.value=!0,requestAnimationFrame(()=>{p.value=!1}))}function E(f){if(!r.value)return;let w=fe(e.containers);h(a)instanceof HTMLElement&&w.add(h(a));let y=f.relatedTarget;y instanceof HTMLElement&&y.dataset.headlessuiFocusGuard!=="true"&&(ve(w,y)||(p.value?V(h(a),Y(i.value,{[j.Forwards]:()=>F.Next,[j.Backwards]:()=>F.Previous})|F.WrapAround,{relativeTo:f.target}):f.target instanceof HTMLElement&&A(f.target)))}return()=>{let f={},w={ref:a,onKeydown:T,onFocusout:E},{features:y,initialFocus:J,containers:ae,...q}=e;return L(Re,[!!(y&4)&&L(X,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:s,features:Z.Focusable}),D({ourProps:w,theirProps:{...n,...q},slot:f,attrs:n,slots:t,name:"FocusTrap"}),!!(y&4)&&L(X,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:s,features:Z.Focusable})])}}}),{features:pe}),$=[];if(typeof window<"u"&&typeof document<"u"){let e=function(n){n.target instanceof HTMLElement&&n.target!==document.body&&$[0]!==n.target&&($.unshift(n.target),$=$.filter(t=>t!=null&&t.isConnected),$.splice(10))};window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})}function Ne(e){let n=m($.slice());return _([e],([t],[l])=>{l===!0&&t===!1?ce(()=>{n.value.splice(0)}):l===!1&&t===!0&&(n.value=$.slice())},{flush:"post"}),()=>{var t;return(t=n.value.find(l=>l!=null&&l.isConnected))!=null?t:null}}function qe({ownerDocument:e},n){let t=Ne(n);O(()=>{M(()=>{var l,a;n.value||((l=e.value)==null?void 0:l.activeElement)===((a=e.value)==null?void 0:a.body)&&A(t())},{flush:"post"})}),k(()=>{A(t())})}function Ie({ownerDocument:e,container:n,initialFocus:t},l){let a=m(null),o=m(!1);return O(()=>o.value=!0),k(()=>o.value=!1),O(()=>{_([n,t,l],(r,u)=>{if(r.every((s,p)=>(u==null?void 0:u[p])===s)||!l.value)return;let i=h(n);i&&ce(()=>{var s,p;if(!o.value)return;let T=h(t),E=(s=e.value)==null?void 0:s.activeElement;if(T){if(T===E){a.value=E;return}}else if(i.contains(E)){a.value=E;return}T?A(T):V(i,F.First|F.NoScroll)===Pe.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),a.value=(p=e.value)==null?void 0:p.activeElement})},{immediate:!0,flush:"post"})}),a}function We({ownerDocument:e,container:n,containers:t,previousActiveElement:l},a){var o;de((o=e.value)==null?void 0:o.defaultView,"focus",r=>{if(!a.value)return;let u=fe(t);h(n)instanceof HTMLElement&&u.add(h(n));let i=l.value;if(!i)return;let s=r.target;s&&s instanceof HTMLElement?ve(u,s)?(l.value=s,A(s)):(r.preventDefault(),r.stopPropagation(),A(i)):A(l.value)},!0)}function ve(e,n){for(let t of e)if(t.contains(n))return!0;return!1}let Q=new Map,x=new Map;function re(e,n=m(!0)){M(t=>{var l;if(!n.value)return;let a=h(e);if(!a)return;t(function(){var r;if(!a)return;let u=(r=x.get(a))!=null?r:1;if(u===1?x.delete(a):x.set(a,u-1),u!==1)return;let i=Q.get(a);i&&(i["aria-hidden"]===null?a.removeAttribute("aria-hidden"):a.setAttribute("aria-hidden",i["aria-hidden"]),a.inert=i.inert,Q.delete(a))});let o=(l=x.get(a))!=null?l:0;x.set(a,o+1),o===0&&(Q.set(a,{"aria-hidden":a.getAttribute("aria-hidden"),inert:a.inert}),a.setAttribute("aria-hidden","true"),a.inert=!0)})}let me=Symbol("ForcePortalRootContext");function Ue(){return N(me,!1)}let ue=R({name:"ForcePortalRoot",props:{as:{type:[Object,String],default:"template"},force:{type:Boolean,default:!1}},setup(e,{slots:n,attrs:t}){return B(me,e.force),()=>{let{force:l,...a}=e;return D({theirProps:a,ourProps:{},slot:{},slots:n,attrs:t,name:"ForcePortalRoot"})}}});function Ve(e){let n=G(e);if(!n){if(e===null)return null;throw new Error(`[Headless UI]: Cannot find ownerDocument for contextElement: ${e}`)}let t=n.getElementById("headlessui-portal-root");if(t)return t;let l=n.createElement("div");return l.setAttribute("id","headlessui-portal-root"),n.body.appendChild(l)}let Ye=R({name:"Portal",props:{as:{type:[Object,String],default:"div"}},setup(e,{slots:n,attrs:t}){let l=m(null),a=c(()=>G(l)),o=Ue(),r=N(ge,null),u=m(o===!0||r==null?Ve(l.value):r.resolveTarget());return M(()=>{o||r!=null&&(u.value=r.resolveTarget())}),k(()=>{var i,s;let p=(i=a.value)==null?void 0:i.getElementById("headlessui-portal-root");p&&u.value===p&&u.value.children.length<=0&&((s=u.value.parentElement)==null||s.removeChild(u.value))}),()=>{if(u.value===null)return null;let i={ref:l,"data-headlessui-portal":""};return L(ke,{to:u.value},D({ourProps:i,theirProps:e,slot:{},attrs:t,slots:n,name:"Portal"}))}}}),ge=Symbol("PortalGroupContext"),Ge=R({name:"PortalGroup",props:{as:{type:[Object,String],default:"template"},target:{type:Object,default:null}},setup(e,{attrs:n,slots:t}){let l=Ce({resolveTarget(){return e.target}});return B(ge,l),()=>{let{target:a,...o}=e;return D({theirProps:o,ourProps:{},slot:{},attrs:n,slots:t,name:"PortalGroup"})}}}),he=Symbol("StackContext");var ee=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))(ee||{});function _e(){return N(he,()=>{})}function Je({type:e,enabled:n,element:t,onUpdate:l}){let a=_e();function o(...r){l==null||l(...r),a(...r)}O(()=>{_(n,(r,u)=>{r?o(0,e,t):u===!0&&o(1,e,t)},{immediate:!0,flush:"sync"})}),k(()=>{n.value&&o(1,e,t)}),B(he,o)}let Ke=Symbol("DescriptionContext");function ze({slot:e=m({}),name:n="Description",props:t={}}={}){let l=m([]);function a(o){return l.value.push(o),()=>{let r=l.value.indexOf(o);r!==-1&&l.value.splice(r,1)}}return B(Ke,{register:a,slot:e,name:n,props:t}),c(()=>l.value.length>0?l.value.join(" "):void 0)}function Qe(e){let n=Me(e.getSnapshot());return k(e.subscribe(()=>{n.value=e.getSnapshot()})),n}function we(){let e=[],n={addEventListener(t,l,a,o){return t.addEventListener(l,a,o),n.add(()=>t.removeEventListener(l,a,o))},requestAnimationFrame(...t){let l=requestAnimationFrame(...t);n.add(()=>cancelAnimationFrame(l))},nextFrame(...t){n.requestAnimationFrame(()=>{n.requestAnimationFrame(...t)})},setTimeout(...t){let l=setTimeout(...t);n.add(()=>clearTimeout(l))},style(t,l,a){let o=t.style.getPropertyValue(l);return Object.assign(t.style,{[l]:a}),this.add(()=>{Object.assign(t.style,{[l]:o})})},group(t){let l=we();return t(l),this.add(()=>l.dispose())},add(t){return e.push(t),()=>{let l=e.indexOf(t);if(l>=0)for(let a of e.splice(l,1))a()}},dispose(){for(let t of e.splice(0))t()}};return n}function Xe(e,n){let t=e(),l=new Set;return{getSnapshot(){return t},subscribe(a){return l.add(a),()=>l.delete(a)},dispatch(a,...o){let r=n[a].call(t,...o);r&&(t=r,l.forEach(u=>u()))}}}function Ze(){let e;return{before({doc:n}){var t;let l=n.documentElement;e=((t=n.defaultView)!=null?t:window).innerWidth-l.clientWidth},after({doc:n,d:t}){let l=n.documentElement,a=l.clientWidth-l.offsetWidth,o=e-a;t.style(l,"paddingRight",`${o}px`)}}}function et(){if(!se())return{};let e;return{before(){e=window.pageYOffset},after({doc:n,d:t,meta:l}){function a(r){return l.containers.flatMap(u=>u()).some(u=>u.contains(r))}t.style(n.body,"marginTop",`-${e}px`),window.scrollTo(0,0);let o=null;t.addEventListener(n,"click",r=>{if(r.target instanceof HTMLElement)try{let u=r.target.closest("a");if(!u)return;let{hash:i}=new URL(u.href),s=n.querySelector(i);s&&!a(s)&&(o=s)}catch{}},!0),t.addEventListener(n,"touchmove",r=>{r.target instanceof HTMLElement&&!a(r.target)&&r.preventDefault()},{passive:!1}),t.add(()=>{window.scrollTo(0,window.pageYOffset+e),o&&o.isConnected&&(o.scrollIntoView({block:"nearest"}),o=null)})}}}function tt(){return{before({doc:e,d:n}){n.style(e.documentElement,"overflow","hidden")}}}function nt(e){let n={};for(let t of e)Object.assign(n,t(n));return n}let P=Xe(()=>new Map,{PUSH(e,n){var t;let l=(t=this.get(e))!=null?t:{doc:e,count:0,d:we(),meta:new Set};return l.count++,l.meta.add(n),this.set(e,l),this},POP(e,n){let t=this.get(e);return t&&(t.count--,t.meta.delete(n)),this},SCROLL_PREVENT({doc:e,d:n,meta:t}){let l={doc:e,d:n,meta:nt(t)},a=[et(),Ze(),tt()];a.forEach(({before:o})=>o==null?void 0:o(l)),a.forEach(({after:o})=>o==null?void 0:o(l))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});P.subscribe(()=>{let e=P.getSnapshot(),n=new Map;for(let[t]of e)n.set(t,t.documentElement.style.overflow);for(let t of e.values()){let l=n.get(t.doc)==="hidden",a=t.count!==0;(a&&!l||!a&&l)&&P.dispatch(t.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",t),t.count===0&&P.dispatch("TEARDOWN",t)}});function lt(e,n,t){let l=Qe(P),a=c(()=>{let o=e.value?l.value.get(e.value):void 0;return o?o.count>0:!1});return _([e,n],([o,r],[u],i)=>{if(!o||!r)return;P.dispatch("PUSH",o,t);let s=!1;i(()=>{s||(P.dispatch("POP",u??o,t),s=!0)})},{immediate:!0}),a}var at=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(at||{});let te=Symbol("DialogContext");function le(e){let n=N(te,null);if(n===null){let t=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,le),t}return n}let U="DC8F892D-2EBD-447C-A4C8-A03058436FF4",it=R({name:"Dialog",inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},open:{type:[Boolean,String],default:U},initialFocus:{type:Object,default:null},id:{type:String,default:()=>`headlessui-dialog-${ne()}`}},emits:{close:e=>!0},setup(e,{emit:n,attrs:t,slots:l,expose:a}){var o;let r=m(!1);O(()=>{r.value=!0});let u=m(0),i=Ae(),s=c(()=>e.open===U&&i!==null?(i.value&W.Open)===W.Open:e.open),p=m(null),T=m(null),E=c(()=>G(p));if(a({el:p,$el:p}),!(e.open!==U||i!==null))throw new Error("You forgot to provide an `open` prop to the `Dialog`.");if(typeof s.value!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${s.value===U?void 0:e.open}`);let f=c(()=>r.value&&s.value?0:1),w=c(()=>f.value===0),y=c(()=>u.value>1),J=N(te,null)!==null,ae=c(()=>y.value?"parent":"leaf"),q=c(()=>i!==null?(i.value&W.Closing)===W.Closing:!1),ye=c(()=>J||q.value?!1:w.value),be=c(()=>{var d,v,b;return(b=Array.from((v=(d=E.value)==null?void 0:d.querySelectorAll("body > *"))!=null?v:[]).find(g=>g.id==="headlessui-portal-root"?!1:g.contains(h(T))&&g instanceof HTMLElement))!=null?b:null});re(be,ye);let Ee=c(()=>y.value?!0:w.value),Te=c(()=>{var d,v,b;return(b=Array.from((v=(d=E.value)==null?void 0:d.querySelectorAll("[data-headlessui-portal]"))!=null?v:[]).find(g=>g.contains(h(T))&&g instanceof HTMLElement))!=null?b:null});re(Te,Ee),Je({type:"Dialog",enabled:c(()=>f.value===0),element:p,onUpdate:(d,v)=>{if(v==="Dialog")return Y(d,{[ee.Add]:()=>u.value+=1,[ee.Remove]:()=>u.value-=1})}});let Le=ze({name:"DialogDescription",slot:c(()=>({open:s.value}))}),I=m(null),S={titleId:I,panelRef:m(null),dialogState:f,setTitleId(d){I.value!==d&&(I.value=d)},close(){n("close",!1)}};B(te,S);function K(){var d,v,b;return[...Array.from((v=(d=E.value)==null?void 0:d.querySelectorAll("html > *, body > *, [data-headlessui-portal]"))!=null?v:[]).filter(g=>!(g===document.body||g===document.head||!(g instanceof HTMLElement)||g.contains(h(T))||S.panelRef.value&&g.contains(S.panelRef.value))),(b=S.panelRef.value)!=null?b:p.value]}let Se=c(()=>!(!w.value||y.value));Oe(()=>K(),(d,v)=>{S.close(),He(()=>v==null?void 0:v.focus())},Se);let Fe=c(()=>!(y.value||f.value!==0));de((o=E.value)==null?void 0:o.defaultView,"keydown",d=>{Fe.value&&(d.defaultPrevented||d.key===De.Escape&&(d.preventDefault(),d.stopPropagation(),S.close()))});let $e=c(()=>!(q.value||f.value!==0||J));return lt(E,$e,d=>{var v;return{containers:[...(v=d.containers)!=null?v:[],K]}}),M(d=>{if(f.value!==0)return;let v=h(p);if(!v)return;let b=new ResizeObserver(g=>{for(let z of g){let C=z.target.getBoundingClientRect();C.x===0&&C.y===0&&C.width===0&&C.height===0&&S.close()}});b.observe(v),d(()=>b.disconnect())}),()=>{let{id:d,open:v,initialFocus:b,...g}=e,z={...t,ref:p,id:d,role:"dialog","aria-modal":f.value===0?!0:void 0,"aria-labelledby":I.value,"aria-describedby":Le.value},C={open:f.value===0};return L(ue,{force:!0},()=>[L(Ye,()=>L(Ge,{target:p.value},()=>L(ue,{force:!1},()=>L(H,{initialFocus:b,containers:K,features:w.value?Y(ae.value,{parent:H.features.RestoreFocus,leaf:H.features.All&~H.features.FocusLock}):H.features.None},()=>D({ourProps:z,theirProps:g,slot:C,attrs:t,slots:l,visible:f.value===0,features:oe.RenderStrategy|oe.Static,name:"Dialog"}))))),L(X,{features:Z.Hidden,ref:T})])}}}),st=R({name:"DialogPanel",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:()=>`headlessui-dialog-panel-${ne()}`}},setup(e,{attrs:n,slots:t,expose:l}){let a=le("DialogPanel");l({el:a.panelRef,$el:a.panelRef});function o(r){r.stopPropagation()}return()=>{let{id:r,...u}=e,i={id:r,ref:a.panelRef,onClick:o};return D({ourProps:i,theirProps:u,slot:{open:a.dialogState.value===0},attrs:n,slots:t,name:"DialogPanel"})}}}),dt=R({name:"DialogTitle",props:{as:{type:[Object,String],default:"h2"},id:{type:String,default:()=>`headlessui-dialog-title-${ne()}`}},setup(e,{attrs:n,slots:t}){let l=le("DialogTitle");return O(()=>{l.setTitleId(e.id),k(()=>l.setTitleId(null))}),()=>{let{id:a,...o}=e;return D({ourProps:{id:a},theirProps:o,slot:{open:l.dialogState.value===0},attrs:n,slots:t,name:"DialogTitle"})}}});export{st as U,dt as Y,ut as n,it as q,we as r};
