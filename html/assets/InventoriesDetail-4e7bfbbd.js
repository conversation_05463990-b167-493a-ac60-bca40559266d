import{M as L,a as u,j as z,B as O,o as t,c as E,w as m,e as l,h as j,d as e,u as a,C as h,n as C,k as D,q as $,y as Z,O as q,r as R,b as s,v as _,t as w,F as P,f as T}from"./index-bfe6943f.js";import{_ as G}from"./AppTopbar-1fff46f6.js";import{c}from"./checkPermission.service-4ccc1117.js";import{l as S,a2 as M,m as N,X as U,h as X,g as A,s as Y,u as H}from"./index-5b4677b6.js";import{_ as V}from"./basicModal-efb13c60.js";import{S as F}from"./transition-78618ab0.js";import"./dialog-abb0eee4.js";import"./hidden-bb6f5784.js";const J={class:"p-6 gap-4"},K={class:"col-span-2"},Q=e("label",{for:"item-state",class:"block text-sm font-normal leading-6 text-gray-900"},"Nový stav položky:",-1),W={class:"grid grid-cols-3 gap-4 pt-2"},ee={class:"flex items-center gap-1"},te=e("span",null,"Položka nepotvrzena",-1),se={class:"flex items-center gap-1"},oe=e("span",null,"Položka potvrzena",-1),ne={class:"flex items-center gap-1"},ae=e("span",null,"Položka nenalezena",-1),re={class:"border-t p-5"},le={class:"text-right space-x-3"},ie={__name:"editItemStateModal",props:{inventoryItem:{type:Object,required:!0}},emits:["reloadItems"],setup(I,{expose:f,emit:k}){const y=I;L(()=>y.inventoryItem,(r,n)=>{i.value=y.inventoryItem.pivot.state});const i=u(""),v=u(!1);z("debugModeGlobalVar");const o=u(!1);O(()=>{});function x(){v.value=!1}function g(){v.value=!0,i.value=""}async function p(){c.check("inventories.items")||c.check("property.master")?await D.post("api/inventories/items/"+y.inventoryItem.pivot.id+"/update",{state:i.value}).then(r=>{$.success(r.data.message)}).catch(r=>{$.error(r.message)}):o.value=!1,x(),k("reloadInventory",!0)}return f({openModal:g}),(r,n)=>(t(),E(a(F),{appear:"",show:v.value,as:"template",onClose:n[6]||(n[6]=b=>x())},{default:m(()=>[l(V,{size:"sm"},{"modal-title":m(()=>[j("Změna stavu položky")]),"modal-close-button":m(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:n[0]||(n[0]=b=>x())},[l(a(S),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":m(()=>[e("div",J,[e("div",K,[Q,e("div",W,[e("button",{onClick:n[1]||(n[1]=h(b=>i.value="UNPROCESSED",["prevent"])),class:C([{"bg-amber-600 text-white":i.value==="UNPROCESSED"},"border border-amber-600 px-3 py-2 rounded-md text-sm text-amber-600 inline-block hover:bg-amber-600 hover:text-white duration-150"])},[e("div",ee,[l(a(M),{class:"h-4 w-4 mr-1","aria-hidden":"true"}),te])],2),e("button",{onClick:n[2]||(n[2]=h(b=>i.value="CONFIRMED",["prevent"])),class:C([{"bg-green-600 text-white":i.value==="CONFIRMED"},"border border-green-600 px-3 py-2 rounded-md text-sm text-green-600 inline-block hover:bg-green-600 hover:text-white duration-150"])},[e("div",se,[l(a(N),{class:"h-4 w-4 mr-1","aria-hidden":"true"}),oe])],2),e("button",{onClick:n[3]||(n[3]=h(b=>i.value="NOT_FOUND",["prevent"])),class:C([{"bg-red-600 text-white":i.value==="NOT_FOUND"},"border border-red-600 px-3 py-2 rounded-md text-sm text-red-600 inline-block hover:bg-red-600 hover:text-white duration-150"])},[e("div",ne,[l(a(S),{class:"h-4 w-4 mr-1","aria-hidden":"true"}),ae])],2)])])]),e("div",re,[e("div",le,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:n[4]||(n[4]=h(b=>x(),["prevent"]))}," Zavřít "),e("button",{onClick:n[5]||(n[5]=h(b=>p(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-regreend-600",type:"submit"}," Změnit ")])])]),_:1})]),_:1},8,["show"]))}},de=e("div",{class:"p-6 gap-4"},[e("p",null,"Opravdu si přejete inventuru uzavřít?")],-1),ce={class:"border-t p-5"},ue={class:"text-right space-x-3"},pe={__name:"processInventoryModal",props:{inventory:{type:Object,required:!0}},emits:["reloadItems"],setup(I,{expose:f,emit:k}){const y=I,i=u(!1);z("debugModeGlobalVar");const v=u(!1);O(()=>{});function o(){i.value=!1}function x(){i.value=!0}async function g(){c.check("inventories.process")||c.check("property.master")?await D.post("api/inventories/"+y.inventory.id+"/process").then(p=>{$.success(p.data.message)}).catch(p=>{$.error(p.message)}):v.value=!1,o(),k("reloadInventory",!0)}return f({openModal:x}),(p,r)=>(t(),E(a(F),{appear:"",show:i.value,as:"template",onClose:r[3]||(r[3]=n=>o())},{default:m(()=>[l(V,{size:"sm"},{"modal-title":m(()=>[j("Uzavřít inventuru")]),"modal-close-button":m(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:r[0]||(r[0]=n=>o())},[l(a(S),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":m(()=>[de,e("div",ce,[e("div",ue,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:r[1]||(r[1]=h(n=>o(),["prevent"]))}," Zavřít "),e("button",{onClick:r[2]||(r[2]=h(n=>g(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-regreend-600",type:"submit"}," Uzavřít ")])])]),_:1})]),_:1},8,["show"]))}},me=e("div",{class:"flex"},[e("span",null,"Zpět")],-1),ve={class:"space-y-6"},_e={class:"px-0"},he={class:"bg-white border border-zinc-200/70 rounded-md p-5 flex justify-between items-center"},xe={class:"sm:flex items-center gap-10"},be={key:0},ye={key:0,class:"border border-amber-600 px-2 py-1 rounded-md text-xs text-amber-600 inline-block"},ge={class:"flex"},fe=e("span",null,"Čeká na potvrzení",-1),ke={key:1,class:"border border-green-600 px-2 py-1 rounded-md text-xs text-green-600 inline-block"},we={class:"flex"},Ie=e("span",null,"Úspěšně uzavřeno",-1),$e={key:2,class:"border border-red-600 px-2 py-1 rounded-md text-xs text-red-600 inline-block"},Ce={class:"flex"},Se=e("span",null,"Neúspěšně uzavřeno",-1),Me={key:0,class:"flex items-center text-sm"},Ne={key:0},ze={key:1,class:"text-amber-600"},Oe={key:1,class:"flex items-center"},Ee={key:0},De={key:1,class:"text-amber-600"},Re={class:"flow-root bg-white border border-zinc-200/70 rounded-md"},Pe={class:"sm:-mx-6 lg:-mx-8"},Ue={class:"inline-block min-w-full align-middle sm:px-6 lg:px-8"},je={key:0,class:"min-w-full divide-y divide-gray-200"},Ve=e("th",{scope:"col",class:"py-4 pl-5 pr-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"}," Ev. číslo ",-1),Fe=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Název ",-1),Be=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Stav ",-1),Le={key:0,scope:"col",class:"py-4 pl-5 pr-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"},Ze={key:0,class:"divide-y divide-gray-200"},qe={class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600 w-72"},Te={key:0},Ge={key:1},Xe={class:"whitespace-nowrap py-4 pl-3 pr-3 text-sm text-gray-600"},Ae={key:0},Ye={key:1},He={class:"whitespace-nowrap py-4 pl-3 pr-3 text-sm text-gray-600"},Je={key:0},Ke={key:0,class:"border border-amber-600 px-2 py-1 rounded-md text-xs text-amber-600 inline-block"},Qe={class:"flex"},We=e("span",null,"Položka nepotvrzena",-1),et={key:1,class:"border border-green-600 px-2 py-1 rounded-md text-xs text-green-600 inline-block"},tt={class:"flex"},st=e("span",null,"Položka potvrzena",-1),ot={key:2,class:"border border-red-600 px-2 py-1 rounded-md text-xs text-red-600 inline-block"},nt={class:"flex"},at=e("span",null,"Položka nenalezena",-1),rt={key:1},lt={key:0,class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600 text-end w-10"},it=["onClick"],dt={key:1},ct=e("tr",null,[e("td",{colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},"Nebyly nalezeny žádné položky.")],-1),ut=[ct],pt={class:"flex justify-center"},mt=e("span",null,"Uzavřít inventuru",-1),wt={__name:"InventoriesDetail",setup(I){const f=u(""),k=u(""),y=Z();q(),z("debugModeGlobalVar");const i=u(["inventories","inventories-detail"]),v=u(!1),o=u({});u(1);const x=u(""),g=u();O(()=>{p()});async function p(){v.value=!0,await D.get("/api/inventories/"+y.params.id).then(r=>{x.value=r.data.meta,o.value=r.data.data}).catch(r=>{console.log(r)}),v.value=!1}return(r,n)=>{const b=R("router-link"),B=R("VueSpinner");return t(),s(P,null,[l(G,{breadCrumbs:i.value},{topbarButtons:m(()=>[l(b,{to:{name:"inventories"},class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},{default:m(()=>[me]),_:1})]),_:1},8,["breadCrumbs"]),e("div",ve,[e("div",_e,[e("div",he,[e("div",xe,[e("div",null,[o.value.state?(t(),s("div",be,[o.value.state=="OPEN"?(t(),s("div",ye,[e("div",ge,[l(a(M),{class:"h-4 w-4 text-amber-600 mr-1","aria-hidden":"true"}),fe])])):o.value.state=="SUCCESSFULLY_CLOSED"?(t(),s("div",ke,[e("div",we,[l(a(N),{class:"h-4 w-4 text-green-600 mr-1","aria-hidden":"true"}),Ie])])):o.value.state=="FAILED_CLOSED"?(t(),s("div",$e,[e("div",Ce,[l(a(U),{class:"h-4 w-4 text-red-600 mr-1","aria-hidden":"true"}),Se])])):_("",!0)])):_("",!0)]),e("div",null,[e("div",null,[o.value.user?(t(),s("span",Me,[l(a(X),{class:"h-5 w-5 mr-2","aria-hidden":"true"}),o.value.user.full_name?(t(),s("span",Ne,w(o.value.user.full_name),1)):(t(),s("span",ze,w(o.value.user.email)+" - Uživateli chybí jméno ",1))])):_("",!0),o.value.room?(t(),s("span",Oe,[l(a(A),{class:"h-5 w-5 mr-2","aria-hidden":"true"}),o.value.room.name?(t(),s("span",Ee,w(o.value.room.name),1)):(t(),s("span",De," Místnosti chybí jméno "))])):_("",!0)])])])])]),e("div",null,[e("div",Re,[e("div",Pe,[e("div",Ue,[v.value==!1?(t(),s("table",je,[e("thead",null,[e("tr",null,[Ve,Fe,Be,a(c).check("inventories.edit")||a(c).check("inventories.delete")||a(c).check("property.master")?(t(),s("th",Le)):_("",!0)])]),o.value&&o.value.items&&o.value.items.length?(t(),s("tbody",Ze,[(t(!0),s(P,null,T(o.value.items,d=>(t(),s("tr",{key:d.id},[e("td",qe,[d.evidence_number?(t(),s("span",Te,w(d.evidence_number),1)):(t(),s("span",Ge,"-"))]),e("td",Xe,[d.name?(t(),s("span",Ae,w(d.name),1)):(t(),s("span",Ye,"-"))]),e("td",He,[d.pivot.state?(t(),s("div",Je,[d.pivot.state=="UNPROCESSED"?(t(),s("div",Ke,[e("div",Qe,[l(a(M),{class:"h-4 w-4 text-amber-600 mr-1","aria-hidden":"true"}),We])])):d.pivot.state=="CONFIRMED"?(t(),s("div",et,[e("div",tt,[l(a(N),{class:"h-4 w-4 text-green-600 mr-1","aria-hidden":"true"}),st])])):d.pivot.state=="NOT_FOUND"?(t(),s("div",ot,[e("div",nt,[l(a(U),{class:"h-4 w-4 text-red-600 mr-1","aria-hidden":"true"}),at])])):_("",!0)])):(t(),s("span",rt,"-"))]),a(c).check("inventories.items")||a(c).check("property.master")?(t(),s("td",lt,[(a(c).check("inventories.items")||a(c).check("property.master"))&&o.value.state=="OPEN"?(t(),s("button",{key:0,onClick:h(vt=>(g.value=d,r.$refs.editItemStateRef.openModal()),["prevent"])},[l(a(Y),{class:"h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 p-2 rounded-lg mr-2","aria-hidden":"true"})],8,it)):_("",!0)])):_("",!0)]))),128))])):(t(),s("tbody",dt,ut))])):(t(),E(B,{key:1,class:"mx-auto text-spinner-color",size:"40"}))])])])]),e("div",pt,[(a(c).check("inventories.process")||a(c).check("property.master"))&&o.value.state=="OPEN"?(t(),s("button",{key:0,onClick:n[0]||(n[0]=h(d=>r.$refs.processInventoryRef.openModal(),["prevent"])),class:"flex items-center gap-3 rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},[l(a(H),{class:"h-6 w-6 text-white","aria-hidden":"true"}),mt])):_("",!0)])]),l(ie,{ref_key:"editItemStateRef",ref:f,inventoryItem:g.value,onReloadInventory:n[1]||(n[1]=d=>p())},null,8,["inventoryItem"]),l(pe,{ref_key:"processInventoryRef",ref:k,inventory:o.value,onReloadInventory:n[2]||(n[2]=d=>p())},null,8,["inventory"])],64)}}};export{wt as default};
