import{a as u,L as Te,I as ce,H as Ve,B as ve,J as qe,u as t,j as le,M as me,N as je,F as J,O as ie,o as s,b as a,c as L,v,e as o,w as i,h as Z,d as e,x as ue,t as p,s as T,E as X,C as D,k as I,q,r as fe,f as Q,n as N,P as ae,T as be,Q as $e,z as He,R as Fe,S as Ke}from"./index-3de9bee7.js";import{_ as Je}from"./AppTopbar-27ebc835.js";import{l as W,x as Xe,y as Qe,z as De,f as Ae,G as We,U as xe,h as et,J as we,K as pe,i as Ie,B as tt,k as ye,C as Ye,O as st,n as Ue,R as ot,V as at,W as lt,Y as nt,Z as it,N as Re,_ as rt,s as dt}from"./index-ddf5f523.js";import{_ as ee}from"./basicModal-d721522e.js";import{c as G}from"./checkPermission.service-9981644d.js";import{S as te}from"./transition-97ad7178.js";import{t as Ee,H as Me,o as ct,f as ut,K as mt,b as pt,T as vt,a as Ze}from"./hidden-7b234e84.js";import{M as _t}from"./dialog-ddee7d0d.js";import{b as ht}from"./use-resolve-button-type-8b834d9a.js";import{d as gt,p as xt}from"./use-controllable-4f60503e.js";import{_ as ft}from"./pagination-041319ed.js";/* empty css             */import{S as Ce}from"./vue-tailwind-datepicker-96876598.js";import{s as Be}from"./default.css_vue_type_style_index_1_src_true_lang-cb98c754.js";import{E as Oe,A as Se,F as ze,B as Pe}from"./listbox-3a5ebdfc.js";import{S as bt,b as yt,M as de,g as kt}from"./menu-bce0729d.js";import"./use-tracked-pointer-ed292bdb.js";import"./use-tree-walker-b9bc1be5.js";let Le=Symbol("LabelContext");function Ge(){let R=le(Le,null);if(R===null){let P=new Error("You used a <Label /> component, but it is not inside a parent.");throw Error.captureStackTrace&&Error.captureStackTrace(P,Ge),P}return R}function $t({slot:R={},name:P="Label",props:O={}}={}){let y=u([]);function _(g){return y.value.push(g),()=>{let n=y.value.indexOf(g);n!==-1&&y.value.splice(n,1)}}return Te(Le,{register:_,slot:R,name:P,props:O}),ce(()=>y.value.length>0?y.value.join(" "):void 0)}Ve({name:"Label",props:{as:{type:[Object,String],default:"label"},passive:{type:[Boolean],default:!1},id:{type:String,default:()=>`headlessui-label-${Ee()}`}},setup(R,{slots:P,attrs:O}){let y=Ge();return ve(()=>qe(y.register(R.id))),()=>{let{name:_="Label",slot:g={},props:n={}}=y,{id:U,passive:h,...l}=R,m={...Object.entries(n).reduce(($,[r,c])=>Object.assign($,{[r]:t(c)}),{}),id:U};return h&&(delete m.onClick,delete m.htmlFor,delete l.onClick),Me({ourProps:m,theirProps:l,slot:g,attrs:O,slots:P,name:_})}}});let Ne=Symbol("GroupContext");Ve({name:"SwitchGroup",props:{as:{type:[Object,String],default:"template"}},setup(R,{slots:P,attrs:O}){let y=u(null),_=$t({name:"SwitchLabel",props:{htmlFor:ce(()=>{var n;return(n=y.value)==null?void 0:n.id}),onClick(n){y.value&&(n.currentTarget.tagName==="LABEL"&&n.preventDefault(),y.value.click(),y.value.focus({preventScroll:!0}))}}}),g=_t({name:"SwitchDescription"});return Te(Ne,{switchRef:y,labelledby:_,describedby:g}),()=>Me({theirProps:R,ourProps:{},slot:{},slots:P,attrs:O,name:"SwitchGroup"})}});let wt=Ve({name:"Switch",emits:{"update:modelValue":R=>!0},props:{as:{type:[Object,String],default:"button"},modelValue:{type:Boolean,default:void 0},defaultChecked:{type:Boolean,optional:!0},name:{type:String,optional:!0},value:{type:String,optional:!0},id:{type:String,default:()=>`headlessui-switch-${Ee()}`}},inheritAttrs:!1,setup(R,{emit:P,attrs:O,slots:y,expose:_}){let g=le(Ne,null),[n,U]=gt(ce(()=>R.modelValue),w=>P("update:modelValue",w),ce(()=>R.defaultChecked));function h(){U(!n.value)}let l=u(null),m=g===null?l:g.switchRef,$=ht(ce(()=>({as:R.as,type:O.type})),m);_({el:m,$el:m});function r(w){w.preventDefault(),h()}function c(w){w.key===Ze.Space?(w.preventDefault(),h()):w.key===Ze.Enter&&xt(w.currentTarget)}function j(w){w.preventDefault()}let S=ce(()=>{var w,k;return(k=(w=ct(m))==null?void 0:w.closest)==null?void 0:k.call(w,"form")});return ve(()=>{me([S],()=>{if(!S.value||R.defaultChecked===void 0)return;function w(){U(R.defaultChecked)}return S.value.addEventListener("reset",w),()=>{var k;(k=S.value)==null||k.removeEventListener("reset",w)}},{immediate:!0})}),()=>{let{id:w,name:k,value:B,...A}=R,M={checked:n.value},H={id:w,ref:m,role:"switch",type:$.value,tabIndex:0,"aria-checked":n.value,"aria-labelledby":g==null?void 0:g.labelledby.value,"aria-describedby":g==null?void 0:g.describedby.value,onClick:r,onKeyup:c,onKeypress:j};return je(J,[k!=null&&n.value!=null?je(ut,mt({features:pt.Hidden,as:"input",type:"checkbox",hidden:!0,readOnly:!0,checked:n.value,name:k,value:B})):null,Me({ourProps:H,theirProps:{...O,...vt(A,["modelValue","defaultChecked"])},slot:M,attrs:O,slots:y,name:"Switch"})])}}});const Ut={class:"p-6"},Ct={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block"},Vt={class:"grid grid-cols-2"},Mt={class:"col-span-2 sm:col-span-1 space-y-6"},Ot=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolená OU:",-1),St={class:"uppercase text-2xl mt-1"},zt=e("span",{class:"text-lg text-gray-400 font-light"},"Povýšitelné OU:",-1),Pt={class:"flex items-center"},jt=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"promoteOu",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Lze povýšit ročník")],-1),Rt={class:"col-span-2 sm:col-span-1 space-y-4"},Zt=e("span",{class:"text-lg text-gray-400 font-light"},"Název nové OU:",-1),Tt=e("span",{class:"text-lg text-gray-400 font-light"},[Z("Alias třídy (kód) - "),e("span",{class:"font-semibold"},"Bakaláři")],-1),Dt=e("span",{class:"text-lg text-gray-400 font-light"},[Z("Alias třídy (kód) - "),e("span",{class:"font-semibold"},"Škola Online")],-1),At={class:"border-t p-5"},It={class:"text-right space-x-3"},Yt=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Vytvořit ",-1),Et={class:"p-6"},Bt={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block"},Lt={class:"grid grid-cols-2 gap-y-6"},Gt={class:"col-span-2 sm:col-span-1 space-y-6"},Nt=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolená OU:",-1),qt={class:"uppercase text-2xl mt-1"},Ht=e("span",{class:"text-lg text-gray-400 font-light"},"Povýšitelné OU:",-1),Ft={class:"flex items-center"},Kt=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"promoteOu",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Lze povýšit ročník")],-1),Jt={class:"col-span-2 sm:col-span-1 space-y-4"},Xt=e("span",{class:"text-lg text-gray-400 font-light"},"Název OU:",-1),Qt=e("span",{class:"text-lg text-gray-400 font-light"},[Z("Alias třídy (kód) - "),e("span",{class:"font-semibold"},"Bakaláři")],-1),Wt=e("span",{class:"text-lg text-gray-400 font-light"},[Z("Alias třídy (kód) - "),e("span",{class:"font-semibold"},"Škola Online")],-1),es={class:"border-t p-5"},ts={class:"text-right space-x-3"},ss=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Uložit ",-1),os={class:"p-6"},as={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block"},ls={class:"uppercase font-semibold px-2 underline"},ns={class:"border-t p-5"},is={class:"text-right space-x-3"},rs={__name:"EditModeActions",props:{item:Object},setup(R){const P=R,O=ie(),y=le("debugModeGlobalVar"),_=u({}),g=u(!1);function n(){g.value=!1}function U(){_.value={},_.value.promoteOu=0,g.value=!0}function h(){I.post("/api/organization-units",{name:_.value.name,parent_id:P.item.id,promotion:_.value.promoteOu,map_bakalari:_.value.map_bakalari,map_skola_online:_.value.map_skola_online}).then(A=>{O.reloadAdTree(!0),q.success(A.data.message),n()}).catch(A=>{console.log(A)})}const l=u({}),m=u(!1);function $(){l.value.promotion=!l.value.promotion}function r(){m.value=!1}function c(A){l.value=A,m.value=!0}function j(){I.post("/api/organization-units/"+l.value.id+"/update",{name:l.value.name,promotion:l.value.promotion,map_bakalari:l.value.map_bakalari,map_skola_online:l.value.map_skola_online}).then(A=>{O.reloadAdTree(!0),q.success(A.data.message),r()}).catch(A=>{console.log(A)})}const S=u(!1);function w(){S.value=!1}function k(A){l.value=A,S.value=!0}function B(){I.post("/api/organization-units/"+l.value.id+"/delete").then(A=>{O.reloadAdTree(!0),q.success(A.data.message),w()}).catch(A=>{console.log(A)})}return(A,M)=>(s(),a(J,null,[t(G).check("active_directory_ou.delete")?(s(),L(t(W),{key:0,class:"h-5 w-5 text-red-500 cursor-pointer","aria-hidden":"true",onClick:M[0]||(M[0]=H=>k(R.item))})):v("",!0),t(G).check("active_directory_ou.edit")?(s(),L(t(Xe),{key:1,class:"h-3 w-3 text-main-color-600 cursor-pointer","aria-hidden":"true",onClick:M[1]||(M[1]=H=>c(R.item))})):v("",!0),t(G).check("active_directory_ou.create")?(s(),L(t(Qe),{key:2,class:"h-5 w-5 text-green-600 cursor-pointer","aria-hidden":"true",onClick:U})):v("",!0),o(t(te),{appear:"",show:g.value,as:"template",onClose:M[8]||(M[8]=H=>n())},{default:i(()=>[o(ee,null,{"modal-title":i(()=>[Z("Založení nové OU")]),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:M[2]||(M[2]=H=>n())},[o(t(W),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[o(t(ue),{onSubmit:h},{default:i(({values:H})=>[e("div",Ut,[t(y)?(s(),a("div",Ct,p(_.value),1)):v("",!0),e("div",Vt,[e("div",Mt,[e("div",null,[Ot,e("h2",St,p(R.item.name),1)]),e("div",null,[zt,e("div",Pt,[o(t(T),{id:"promoteOu","aria-describedby":"promoteOu",name:"promoteOu",onClick:M[3]||(M[3]=F=>_.value.promoteOu=!_.value.promoteOu),type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",value:"false"}),jt])])]),e("div",Rt,[e("div",null,[Zt,o(t(T),{rules:"required",type:"text",name:"newOuName",id:"newOuName",modelValue:_.value.name,"onUpdate:modelValue":M[4]||(M[4]=F=>_.value.name=F),class:"block w-full rounded-md border-0 py-2 mt-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Zadejte název nové OU..."},null,8,["modelValue"]),o(t(X),{name:"newOuName",class:"text-rose-400 text-sm block pt-1"})]),e("div",null,[Tt,o(t(T),{type:"text",name:"newOuBakalari",id:"newOuBakalari",modelValue:_.value.map_bakalari,"onUpdate:modelValue":M[5]||(M[5]=F=>_.value.map_bakalari=F),class:"block w-full rounded-md border-0 py-2 mt-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Zadejte alias, který se shoduje s třídou v Bakalářích..."},null,8,["modelValue"])]),e("div",null,[Dt,o(t(T),{type:"text",name:"newOuSkolaOnline",id:"newOuSkolaOnline",modelValue:_.value.map_skola_online,"onUpdate:modelValue":M[6]||(M[6]=F=>_.value.map_skola_online=F),class:"block w-full rounded-md border-0 py-2 mt-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Zadejte alias, který se shoduje s třídou ve Škole Online..."},null,8,["modelValue"])])])])]),e("div",At,[e("div",It,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:M[7]||(M[7]=D(F=>n(),["prevent"]))}," Zavřít "),Yt])])]),_:1})]),_:1})]),_:1},8,["show"]),o(t(te),{appear:"",show:m.value,as:"template",onClose:M[15]||(M[15]=H=>r())},{default:i(()=>[o(ee,null,{"modal-title":i(()=>[Z("Úprava OU")]),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:M[9]||(M[9]=H=>r())},[o(t(W),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[o(t(ue),{onSubmit:j},{default:i(({values:H})=>[e("div",Et,[t(y)?(s(),a("div",Bt,p(l.value),1)):v("",!0),e("div",Lt,[e("div",Gt,[e("div",null,[Nt,e("h2",qt,p(R.item.name),1)]),e("div",null,[Ht,e("div",Ft,[o(t(T),{id:"promoteOu","aria-describedby":"promoteOu",name:"promoteOu",onClick:M[10]||(M[10]=F=>$()),type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",value:"false"}),Kt])])]),e("div",Jt,[e("div",null,[Xt,o(t(T),{rules:"required",type:"text",name:"editOuName",id:"editOuName",modelValue:l.value.name,"onUpdate:modelValue":M[11]||(M[11]=F=>l.value.name=F),class:"block w-full rounded-md border-0 py-2 mt-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Zadejte nový název OU..."},null,8,["modelValue"]),o(t(X),{name:"editOuName",class:"text-rose-400 text-sm block pt-1"})]),e("div",null,[Qt,o(t(T),{type:"text",name:"selectedOuBakalari",id:"selectedOuBakalari",modelValue:l.value.map_bakalari,"onUpdate:modelValue":M[12]||(M[12]=F=>l.value.map_bakalari=F),class:"block w-full rounded-md border-0 py-2 mt-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Zadejte alias, který se shoduje s třídou v Bakalářích..."},null,8,["modelValue"])]),e("div",null,[Wt,o(t(T),{type:"text",name:"newOuSkolaOnline",id:"newOuSkolaOnline",modelValue:l.value.map_skola_online,"onUpdate:modelValue":M[13]||(M[13]=F=>l.value.map_skola_online=F),class:"block w-full rounded-md border-0 py-2 mt-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Zadejte alias, který se shoduje s třídou ve Škole Online..."},null,8,["modelValue"])])])])]),e("div",es,[e("div",ts,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:M[14]||(M[14]=D(F=>r(),["prevent"]))}," Zavřít "),ss])])]),_:1})]),_:1})]),_:1},8,["show"]),o(t(te),{appear:"",show:S.value,as:"template",onClose:M[19]||(M[19]=H=>w())},{default:i(()=>[o(ee,null,{"modal-title":i(()=>[Z("Smazání OU")]),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:M[16]||(M[16]=H=>w())},[o(t(W),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[e("div",os,[t(y)?(s(),a("div",as,p(l.value),1)):v("",!0),e("div",null,[e("span",null,[Z("Opravdu si přejete zvolenou ou:"),e("span",ls,p(l.value.name),1),Z("smazat?")])])]),e("div",ns,[e("div",is,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:M[17]||(M[17]=D(H=>w(),["prevent"]))}," Zavřít "),e("button",{class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-red-600",onClick:M[18]||(M[18]=D(H=>B(),["prevent"]))}," Smazat ")])])]),_:1})]),_:1},8,["show"])],64))}};const ds={class:"flex justify-between items-center"},cs=["onClick"],us={class:"flex items-center gap-1"},ms={key:0,class:"flex items-center gap-0.5"},ps={__name:"RecursiveDropdownMenu",props:{items:{type:Array,required:!0},level:{type:Number,default:1},edit_mode:Boolean},setup(R){const P=ie();function O(y){P.setTreePosition(y)}return(y,_)=>{const g=fe("RecursiveDropdownMenu",!0);return s(),a("ul",{class:N(["tree_line",`level-${R.level}`])},[(s(!0),a(J,null,Q(R.items,n=>(s(),a("li",{key:n.id},[e("div",ds,[e("button",{class:N(["text-gray-900 block pr-4 py-2 text-sm cursor-pointer uppercase hover:text-green-600 duration-150",{"text-green-600":t(P).treePosition.id===n.id}]),onClick:U=>O({id:n.id,name:n.name})},[e("span",us,[n.promotion==1?(s(),L(t(De),{key:0,class:"h-3 w-3 text-main-color-600","aria-hidden":"true"})):v("",!0),e("span",null,p(n.name),1),t(P).treePosition.id&&t(P).treePosition.id===n.id?(s(),L(t(Ae),{key:1,class:"h-5 w-5 text-green-600","aria-hidden":"true"})):v("",!0)])],10,cs),R.edit_mode?(s(),a("div",ms,[o(rs,{item:{id:n.id,name:n.name,promotion:n.promotion,map_bakalari:n.map_bakalari,map_skola_online:n.map_skola_online}},null,8,["item"])])):v("",!0)]),n.childrens?(s(),L(g,{key:0,edit_mode:R.edit_mode,items:n.childrens,level:R.level+1},null,8,["edit_mode","items","level"])):v("",!0)]))),128))],2)}}},vs={class:"grid grid-cols-12 gap-x-6 gap-y-8"},_s={class:"relative block text-left col-span-12 md:col-span-8 xl:col-span-12"},hs={class:"w-full"},gs={class:"inline-flex items-center w-full gap-x-3 bg-main-color-600 px-3 py-3.5 text-sm font-semibold text-white rounded-t-md"},xs={class:"w-full origin-top divide-y divide-gray-100 rounded-md bg-white shadow-lg focus:outline-none"},fs={key:0,class:"px-4 py-3 flex justify-between items-center"},bs=e("p",{class:"text-xs text-main-color-600"},"Editační režim",-1),ys={class:"py-1 px-4"},ks={class:"py-4 px-4"},$s={key:0,class:"relative block text-left col-span-12 md:col-span-4 xl:col-span-12"},ws={class:"w-full"},Us={class:"inline-flex items-center w-full gap-x-3 bg-main-color-600 px-3 py-3.5 text-sm font-semibold text-white rounded-t-md"},Cs={class:"w-full origin-top divide-y divide-gray-100 rounded-md bg-white shadow-lg focus:outline-none"},Vs={class:"py-4 px-4"},Ms=e("span",null,"Zobrazit přehled skupin",-1),Os={__name:"AdStructure",setup(R){const P=ie(),O=u(!1),y=u([]),_=u(!1);me(()=>P.reloadAdTreeValue,(U,h)=>{g(),P.reloadAdTree(!1)}),ve(()=>{g()});function g(){I.get("/api/organization-units/tree").then(U=>{y.value=U.data.data}).catch(U=>{console.log(U)})}function n(){P.setAllUsersTreePosition()}return(U,h)=>{const l=fe("router-link");return s(),a("div",vs,[e("div",_s,[e("div",hs,[e("div",gs,[o(t(We),{class:"h-6 w-6 text-white","aria-hidden":"true"}),Z(" Struktura AD ")])]),e("div",xs,[t(G).check("active_directory_ou.create")||t(G).check("active_directory_ou.edit")&&t(G).check("active_directory_ou.delete")?(s(),a("div",fs,[bs,o(t(wt),{modelValue:O.value,"onUpdate:modelValue":h[0]||(h[0]=m=>O.value=m),class:N([O.value?"bg-indigo-600":"bg-gray-200","relative inline-flex h-5 w-9 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out"])},{default:i(()=>[e("span",{"aria-hidden":"true",class:N([O.value?"translate-x-4":"translate-x-0","pointer-events-none inline-block h-4 w-4 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"])},null,2)]),_:1},8,["modelValue","class"])])):v("",!0),e("div",ys,[o(ps,{items:y.value,edit_mode:O.value},null,8,["items","edit_mode"])]),e("div",ks,[e("button",{class:"text-sm hover:text-green-600 duration-150",onClick:h[1]||(h[1]=m=>n())},"Zobrazit vše")])])]),t(G).check("active_directory_group.read")?(s(),a("div",$s,[e("div",ws,[e("div",Us,[o(t(xe),{class:"h-6 w-6 text-white","aria-hidden":"true"}),Z(" Skupiny AD ")])]),e("div",Cs,[e("div",Vs,[o(l,{to:{name:"groups"},class:"text-gray-900 flex justify-between items-center text-sm",onMouseenter:h[2]||(h[2]=m=>_.value=!0),onMouseleave:h[3]||(h[3]=m=>_.value=!1)},{default:i(()=>[Ms,_.value?(s(),L(t(Ae),{key:0,class:"h-5 w-5 text-green-600","aria-hidden":"true"})):v("",!0)]),_:1})])])])):v("",!0)])}}};const Ss={class:"pb-4 px-6 border-b"},zs={class:"mt-4"},Ps={class:"space-y-4 sm:flex sm:items-center sm:space-x-10 sm:space-y-0"},js=e("span",{class:"text-sm text-green-500 font-semibold"},"Typ zakládaného účtu:",-1),Rs=["for"],Zs={class:"p-6"},Ts={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block mb-6"},Ds=e("br",null,null,-1),As=e("br",null,null,-1),Is=e("br",null,null,-1),Ys=e("br",null,null,-1),Es=e("br",null,null,-1),Bs=e("br",null,null,-1),Ls=e("br",null,null,-1),Gs=e("br",null,null,-1),Ns=e("br",null,null,-1),qs=e("br",null,null,-1),Hs={class:"grid grid-cols-2 gap-x-14"},Fs={class:"col-span-2 sm:col-span-1"},Ks={class:"space-y-4"},Js={class:"flex items-center"},Xs=e("p",{class:"ml-4 text-lg text-gray-900"},"Základní informace",-1),Qs={class:"grid grid-cols-1 sm:grid-cols-2 gap-6 gap-y-4"},Ws=e("label",{for:"first-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Jméno:",-1),eo={class:"mt-2"},to=e("label",{for:"last-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Příjmení:",-1),so={class:"mt-2"},oo=e("label",{for:"phone",class:"block text-sm font-normal leading-6 text-gray-900"},"Telefonní číslo:",-1),ao={class:"mt-2"},lo=e("label",{for:"new-email",class:"block text-sm font-normal leading-6 text-gray-900"},"Emailová adresa:",-1),no={class:"mt-2"},io={class:"grid grid-cols-12 items-end gap-4"},ro={class:"col-span-12 sm:col-span-8"},co=e("label",{for:"new-email-confirmation",class:"block text-sm font-normal leading-6 text-gray-900"},"Opakovat email:",-1),uo={class:"mt-2"},mo={class:"col-span-12 sm:col-span-4 mb-2"},po={class:"flex h-6 justify-end items-center"},vo=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"verifiedEmail",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Ověřený email")],-1),_o={key:0,class:"space-y-4 border-t pt-6 mt-6"},ho={class:"flex items-center"},go=e("p",{class:"ml-4 text-lg text-gray-900"},"Host / expirace účtu",-1),xo={class:"flex h-6 justify-start items-center"},fo=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"visitor",class:"text-gray-900 cursor-pointer text-sm"},[Z("Uživatelský účet je "),e("strong",null,"účet hosta")])],-1),bo={class:"rounded-l-full w-full"},yo={type:"button",class:"rounded-md border border-gray-300 block font-normal px-2.5 py-1.5 w-full text-sm leading-6 items-center justify-center space-x-2 sm:space-x-4 transition ease-out duration-300"},ko={class:"flex items-center w-full"},$o={class:"flex-1 text-left"},wo={key:0,class:"text-gray-900"},Uo={key:1,class:"text-gray-400 text-sm"},Co={class:"w-6 h-6"},Vo={class:"flex gap-2"},Mo=e("span",{class:"font-light text-sm"},"V případě nenastavení data, bude účet neomezeně platný.",-1),Oo={class:"col-span-2 sm:col-span-1"},So={class:"space-y-4"},zo={class:"flex items-center"},Po=e("p",{class:"ml-4 text-lg text-gray-900"},"Nastavení hesla",-1),jo=e("label",{for:"newUserPassword",class:"block text-sm font-normal leading-6 text-gray-900"},"Heslo:",-1),Ro={class:"mt-2"},Zo={class:"grid grid-cols-12 items-end gap-4 border-b pb-8 mb-6"},To={class:"col-span-12 sm:col-span-8"},Do=e("label",{for:"newUserPasswordConfirmation",class:"block text-sm font-normal leading-6 text-gray-900"},"Opakovat heslo:",-1),Ao={class:"mt-2"},Io={class:"col-span-12 sm:col-span-4 mb-2"},Yo={class:"flex h-6 justify-end items-center"},Eo=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"sendSms",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Zaslat do SMS")],-1),Bo={key:0,class:"col-span-12 sm:col-span-12 mb-2"},Lo={class:"flex h-6 justify-start items-center"},Go=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"mustChangePassword",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Vyžádat změnu hesla po přihlášení")],-1),No={key:0,class:"border-b pb-6"},qo={class:"flex items-center my-4"},Ho=e("p",{class:"ml-4 text-lg text-gray-900"},"Zařazení do OU",-1),Fo={class:"relative mt-1"},Ko={key:0,class:"block truncate"},Jo={key:1,class:"block truncate text-gray-400"},Xo={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},Qo={key:0,class:"absolute inset-y-0 left-0 flex items-center pl-3 text-main-color-600"},Wo={key:1},ea={class:"flex items-center my-4 text-sm"},ta=e("p",{class:"ml-4 text-lg text-gray-900"},"Zařazení do skupin",-1),sa={key:2},oa={class:"flex items-center my-4"},aa=e("p",{class:"ml-4 text-lg text-gray-900"},"Nastavení role",-1),la={class:"space-y-5"},na={class:"flex h-6 items-center"},ia={class:"ml-3 text-sm leading-6"},ra=["for"],da={key:3,class:"mt-6"},ca={class:"flex items-center my-4"},ua=e("p",{class:"ml-4 text-lg text-gray-900"},"Uzamčení účtu",-1),ma={class:"mt-4"},pa={class:"space-y-4"},va=["for"],_a={class:"border-t p-5"},ha={class:"text-right space-x-3"},ga=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Založit ",-1),xa={__name:"createUserModal",emits:["reloadUsersTable"],setup(R,{expose:P,emit:O}){const y=le("debugModeGlobalVar");ie();const _=u({}),g=u({}),n=u(""),U=u([]),h=[{id:"1",title:"Účet Active Directory"},{id:"0",title:"Lokální účet"}],l=u(null),m=u("email"),$=u(0),r=u(0),c=u(0),j=u(0),S=u(0),w=u([]),k=u({}),B=u({}),A=u([]),M=u(2);me(()=>l.value,(E,f)=>{H()});function H(){l.value==0?m.value="required|email":m.value="email"}function F(){I.get("/api/groups?perpage=9999").then(E=>{U.value=E.data.data}).catch(E=>{console.log(E)})}function K(){I.get("/api/organization-units?perpage=9999").then(E=>{_.value=E.data.data}).catch(E=>{console.log(E)})}function C(){I.get("/api/account-control-codes?listing=1").then(E=>{g.value=E.data.data}).catch(E=>{console.log(E)})}function z(){I.get("/api/roles").then(E=>{B.value=E.data.data}).catch(E=>{console.log(E)})}function d(E){A.value.includes(E)?A.value=A.value.filter(f=>f!==E):A.value.push(E)}const Y=u(!1);function se(){Y.value=!1}function _e(){K(),F(),z(),C(),n.value="",w.value=[],k.value={},$.value=0,r.value=0,c.value=0,j.value=0,S.value=0,M.value=2,Y.value=!0}function ke(){l.value==0?he():l.value==1&&ge()}function he(){I.post("/api/users",{first_name:k.value.first_name,last_name:k.value.last_name,email:k.value.email,email_confirmation:k.value.email_confirmation,email_verified:$.value,phone:k.value.phone,show_password:1,password:k.value.password,password_confirmation:k.value.password_confirmation,roles:A.value}).then(E=>{q.success(E.data.message),O("reloadUsersTable",!0),se()}).catch(E=>{console.log(E)})}function ge(){var E=[];w.value.forEach(b=>{E.push(b.id)});let f={first_name:k.value.first_name,last_name:k.value.last_name,email:k.value.email,phone:k.value.phone,organization_unit:n.value.id,password:k.value.password,password_confirmation:k.value.password_confirmation,send_sms:r.value,must_change_password:c.value,show_password:1,groups:E,visitor:j.value,account_control_code_id:M.value};k.value.expire_date&&k.value.expire_date[0]&&k.value.expire_date[0].length&&(f.expire=ae(k.value.expire_date[0]).format("YYYY-MM-DD HH:MM")),I.post("/api/users/ad",f).then(b=>{q.success(b.data.message),O("reloadUsersTable",!0),E=[],se()}).catch(b=>{console.log(b)})}return P({openModal:_e}),(E,f)=>(s(),L(t(te),{appear:"",show:Y.value,as:"template",onClose:f[19]||(f[19]=b=>se())},{default:i(()=>[o(ee,{size:"xl"},{"modal-title":i(()=>[Z("Vytvoření nového uživatele")]),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:f[0]||(f[0]=b=>se())},[o(t(W),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[o(t(ue),{onSubmit:ke},{default:i(({values:b})=>[e("div",Ss,[e("fieldset",zs,[e("div",Ps,[js,(s(),a(J,null,Q(h,V=>e("div",{key:V.id,class:"flex items-center"},[o(t(T),{id:V.id,name:"accountType",type:"radio",rules:"requiredRadio",value:V.id,modelValue:l.value,"onUpdate:modelValue":f[1]||(f[1]=ne=>l.value=ne),class:"h-4 w-4 border-gray-300 text-main-color-600 focus:ring-0 focus:ring-offset-0 cursor-pointer"},null,8,["id","value","modelValue"]),e("label",{for:V.id,class:"ml-3 block text-sm text-gray-900 cursor-pointer"},p(V.title),9,Rs)])),64)),o(t(X),{name:"accountType",class:"text-rose-400 text-sm block pt-1"})])])]),e("div",Zs,[t(y)?(s(),a("div",Ts,[e("span",null,"new user: "+p(k.value),1),Ds,e("span",null,"selected account type: "+p(l.value),1),As,e("span",null,"verified email: "+p($.value),1),Is,e("span",null,"send sms: "+p(r.value),1),Ys,e("span",null,"password change: "+p(c.value),1),Es,e("span",null,"selected ou: "+p(n.value),1),Bs,e("span",null,[Z("groups: "),e("pre",null,p(w.value),1)]),Ls,e("span",null,"role: "+p(B.value),1),Gs,e("span",null,"selected roles: "+p(A.value),1),Ns,e("span",null,"account control codes: "+p(g.value),1),qs,e("span",null,"selected account control code: "+p(M.value),1)])):v("",!0),e("div",Hs,[e("div",Fs,[e("div",Ks,[e("div",Js,[o(t(et),{class:"w-7"}),Xs]),e("div",Qs,[e("div",null,[Ws,e("div",eo,[o(t(T),{modelValue:k.value.first_name,"onUpdate:modelValue":f[2]||(f[2]=V=>k.value.first_name=V),type:"text",name:"first-name",id:"first-name",rules:"required|textOnly|minLength:2|maxLength:50",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte jméno..."},null,8,["modelValue"]),o(t(X),{name:"first-name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[to,e("div",so,[o(t(T),{modelValue:k.value.last_name,"onUpdate:modelValue":f[3]||(f[3]=V=>k.value.last_name=V),type:"text",name:"last-name",id:"last-name",rules:"required|textOnly|minLength:2|maxLength:50",autocomplete:"family-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte Příjmení..."},null,8,["modelValue"]),o(t(X),{name:"last-name",class:"text-rose-400 text-sm block pt-1"})])])]),e("div",null,[oo,e("div",ao,[o(t(T),{modelValue:k.value.phone,"onUpdate:modelValue":f[4]||(f[4]=V=>k.value.phone=V),type:"tel",name:"phone",id:"phone",rules:"phone",autocomplete:"phone",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte tel. číslo..."},null,8,["modelValue"]),o(t(X),{name:"phone",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[lo,e("div",no,[o(t(T),{modelValue:k.value.email,"onUpdate:modelValue":f[5]||(f[5]=V=>k.value.email=V),id:"new-email",name:"new-email",type:"email",rules:m.value,class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte emailovou adresu..."},null,8,["modelValue","rules"]),o(t(X),{name:"new-email",class:"text-rose-400 text-sm block pt-1"})])]),e("div",io,[e("div",ro,[co,e("div",uo,[o(t(T),{modelValue:k.value.email_confirmation,"onUpdate:modelValue":f[6]||(f[6]=V=>k.value.email_confirmation=V),id:"new-email-confirmation",rules:m.value+"|isEqual:"+k.value.email,name:"new-email-confirmation",type:"email",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Opakujte emailovou adresu..."},null,8,["modelValue","rules"]),o(t(X),{name:"new-email-confirmation",class:"text-rose-400 text-sm block pt-1"})])]),e("div",mo,[e("div",po,[o(t(T),{id:"verifiedEmail","aria-describedby":"verifiedEmail",name:"verifiedEmail",modelValue:$.value,"onUpdate:modelValue":f[7]||(f[7]=V=>$.value=V),value:!$.value,type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"]),vo])])])]),l.value==1?(s(),a("div",_o,[e("div",ho,[o(t(xe),{class:"w-7"}),go]),e("div",xo,[o(t(T),{id:"visitor","aria-describedby":"visitor",name:"visitor",type:"checkbox",modelValue:j.value,"onUpdate:modelValue":f[8]||(f[8]=V=>j.value=V),value:!j.value,class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"]),fo]),e("div",null,[o(t(Ce),{i18n:"cs","as-single":"",shortcuts:!1,modelValue:k.value.expire_date,"onUpdate:modelValue":f[9]||(f[9]=V=>k.value.expire_date=V),placeholder:"Zvolte datum, do kdy je účet aktivní"},{default:i(({clear:V})=>[e("div",null,[e("div",bo,[e("button",yo,[e("div",ko,[e("div",$o,[k.value.expire_date&&k.value.expire_date.length?(s(),a("span",wo,[e("span",null,p(t(ae)(k.value.expire_date[0]).format("DD.MM.YYYY")),1)])):(s(),a("span",Uo,"Zvolte datum, do kdy je účet aktivní"))]),e("div",Co,[k.value.expire_date&&k.value.expire_date.length?(s(),L(t(W),{key:0,onClick:V,class:"h-6 w-6 text-gray-900","aria-hidden":"true"},null,8,["onClick"])):(s(),L(t(we),{key:1,class:"h-6 w-6 text-gray-900","aria-hidden":"true"}))])])])])])]),_:1},8,["modelValue"])]),e("div",Vo,[e("div",null,[o(t(pe),{class:"h-5 w-5 text-main-color-600","aria-hidden":"true"})]),Mo])])):v("",!0)]),e("div",Oo,[e("div",So,[e("div",zo,[o(t(Ie),{class:"w-7"}),Po]),e("div",null,[jo,e("div",Ro,[o(t(T),{modelValue:k.value.password,"onUpdate:modelValue":f[10]||(f[10]=V=>k.value.password=V),id:"newUserPassword",name:"newUserPassword",type:"password",rules:"password",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte heslo..."},null,8,["modelValue"]),o(t(X),{name:"newUserPassword",class:"text-rose-400 text-sm block pt-1"})])]),e("div",Zo,[e("div",To,[Do,e("div",Ao,[o(t(T),{modelValue:k.value.password_confirmation,"onUpdate:modelValue":f[11]||(f[11]=V=>k.value.password_confirmation=V),id:"newUserPasswordConfirmation",name:"newUserPasswordConfirmation",type:"password",rules:"password|isEqual:"+k.value.password,class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Opakujte heslo..."},null,8,["modelValue","rules"]),o(t(X),{name:"newUserPasswordConfirmation",class:"text-rose-400 text-sm block pt-1"})])]),e("div",Io,[e("div",Yo,[o(t(T),{id:"sendSms","aria-describedby":"sendSms",name:"sendSms",modelValue:r.value,"onUpdate:modelValue":f[12]||(f[12]=V=>r.value=V),value:!r.value,type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"]),Eo])]),l.value==1?(s(),a("div",Bo,[e("div",Lo,[o(t(T),{id:"mustChangePassword","aria-describedby":"mustChangePassword",name:"mustChangePassword",modelValue:c.value,"onUpdate:modelValue":f[13]||(f[13]=V=>c.value=V),value:!c.value,type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"]),Go])])):v("",!0)])]),l.value==1?(s(),a("div",No,[e("div",qo,[o(t(tt),{class:"w-7"}),Ho]),o(t(Oe),{modelValue:n.value,"onUpdate:modelValue":f[15]||(f[15]=V=>n.value=V)},{default:i(()=>[e("div",Fo,[o(t(Se),{class:"relative w-full text-left cursor-pointer block rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},{default:i(()=>[n.value&&n.value.name?(s(),a("span",Ko,p(n.value.name),1)):(s(),a("span",Jo,"Zvolte OU")),e("span",Xo,[o(t(ye),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]),_:1}),o(t(T),{modelValue:n.value,"onUpdate:modelValue":f[14]||(f[14]=V=>n.value=V),name:"selectedOu",rules:"required",class:"hidden"},null,8,["modelValue"]),o(t(X),{name:"selectedOu",class:"text-rose-400 text-sm block pt-1"}),o(be,{"leave-active-class":"transition duration-100 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:i(()=>[_.value&&_.value.length?(s(),L(t(ze),{key:0,class:"absolute z-20 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:i(()=>[(s(!0),a(J,null,Q(_.value,V=>(s(),L(t(Pe),{key:V.name,value:V,as:"template"},{default:i(({active:ne,selected:x})=>[e("li",{class:N([ne?"bg-main-color-100 text-main-color-900":"text-gray-900","relative cursor-pointer select-none py-2 pl-10 pr-4"])},[e("span",{class:N([x?"font-medium":"font-normal","block truncate"])},p(V.name),3),x?(s(),a("span",Qo,[o(t(Ye),{class:"h-5 w-5","aria-hidden":"true"})])):v("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})):v("",!0)]),_:1})])]),_:1},8,["modelValue"])])):v("",!0),l.value==1?(s(),a("div",Wo,[e("div",ea,[o(t(xe),{class:"w-7"}),ta]),o(t(T),{name:"selectedGroups"},{default:i(({handleChange:V,value:ne})=>[o(t(Be),{name:"selectedGroups",modelValue:w.value,"onUpdate:modelValue":[f[16]||(f[16]=x=>w.value=x),V],mode:"tags",label:"name","value-prop":"id",options:U.value,object:!0,placeholder:"Zvolte si skupiny",class:"!rounded-lg !text-sm"},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1}),o(t(X),{name:"selectedGroups",class:"text-rose-400 text-sm block pt-1"})])):v("",!0),l.value==0?(s(),a("div",sa,[e("div",oa,[o(t(xe),{class:"w-7"}),aa]),e("div",null,[e("fieldset",null,[e("div",la,[(s(!0),a(J,null,Q(B.value,V=>(s(),a("div",{key:V.id,class:"relative flex items-start"},[e("div",na,[o(t(T),{rules:"requiredCheckbox",id:V.name,name:"roles",value:V.id,onClick:ne=>d(V),type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["id","value","onClick"])]),e("div",ia,[e("label",{for:V.name,class:"font-medium text-gray-900 cursor-pointer"},p(V.name),9,ra)])]))),128)),o(t(X),{name:"roles",class:"text-rose-400 text-sm block pt-1"})])])])])):v("",!0),l.value==1?(s(),a("div",da,[e("div",ca,[o(t(st),{class:"w-7"}),ua]),e("fieldset",ma,[e("div",pa,[(s(!0),a(J,null,Q(g.value,V=>(s(),a("div",{key:V.id,class:"flex items-center"},[o(t(T),{rules:"requiredRadio",id:V.id,modelValue:M.value,"onUpdate:modelValue":f[17]||(f[17]=ne=>M.value=ne),name:"selectedAccountControlCode",type:"radio",checked:M.value==V.id,value:V.id,class:"h-4 w-4 border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["id","modelValue","checked","value"]),e("label",{label:"",for:V.id,class:"ml-3 block text-sm leading-6 text-gray-900 cursor-pointer"},p(V.name),9,va)]))),128)),o(t(X),{name:"selectedAccountControlCode",class:"text-rose-400 text-sm block pt-1"})])])])):v("",!0)])])]),e("div",_a,[e("div",ha,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:f[18]||(f[18]=D(V=>se(),["prevent"]))}," Zavřít "),ga])])]),_:1})]),_:1})]),_:1},8,["show"]))}},fa=e("div",{class:"p-6"},[e("span",null,"Opravdu chcete synchronizovat Active Directory?")],-1),ba={class:"border-t p-5"},ya={class:"text-right space-x-3"},ka={__name:"syncAdModal",emits:["reloadAd"],setup(R,{expose:P,emit:O}){const y=u(!1);function _(){y.value=!1}function g(){y.value=!0}function n(){I.post("/api/users/sync").then(U=>{q.success("Synchronizace byla úspěšná!"),_()}).catch(U=>{console.log(U)})}return P({openModal:g}),(U,h)=>(s(),L(t(te),{appear:"",show:y.value,as:"template",onClose:h[3]||(h[3]=l=>_())},{default:i(()=>[o(ee,null,{"modal-title":i(()=>[Z("Synchronizovat AD")]),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:h[0]||(h[0]=l=>_())},[o(t(W),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[fa,e("div",ba,[e("div",ya,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:h[1]||(h[1]=D(l=>_(),["prevent"]))}," Zavřít "),e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",onClick:h[2]||(h[2]=D(l=>n(),["prevent"]))}," Synchronizovat ")])])]),_:1})]),_:1},8,["show"]))}},$a={class:"p-6"},wa={key:0,class:"grid grid-cols-2"},Ua={class:"col-span-1"},Ca=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolená OU:",-1),Va={key:0,class:"uppercase text-2xl"},Ma={class:"col-span-1 space-y-5"},Oa=e("span",{class:"text-lg text-gray-400 font-light"},"Odeslání hesla do SMS:",-1),Sa={class:"relative flex items-start"},za={class:"flex h-6 items-center"},Pa=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"sendPasswordSms",class:"text-gray-900 cursor-pointer"},"ANO")],-1),ja={class:"flex gap-2"},Ra=e("span",{class:"font-light text-sm"},"Pokud je aktivní možnost odeslat heslo SMS, proběhne kontrola doplnění tel. čísel. V případě nedoplnění tel. čísla budete vyzváni k doplnění a proces budete muset opakovat.",-1),Za={class:"col-span-12 sm:col-span-12 mb-2"},Ta={class:"flex h-6 justify-start items-center"},Da=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"mustChangePassword",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Vyžádat změnu hesla po přihlášení")],-1),Aa={key:1,class:"grid grid-cols-2"},Ia={class:"col-span-1"},Ya=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolení uživatele:",-1),Ea={key:0,class:"col-span-1 space-y-1 py-4"},Ba={key:0},La={key:0},Ga={key:1},Na={key:2},qa={key:1},Ha=e("span",{class:"text-xs text-red-500"}," - Chybí některé osobní údaje",-1),Fa={class:"col-span-1 space-y-5"},Ka=e("span",{class:"text-lg text-gray-400 font-light"},"Odeslání hesla do SMS:",-1),Ja={class:"relative flex items-start"},Xa={class:"flex h-6 items-center"},Qa=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"sendPasswordSms",class:"text-gray-900 cursor-pointer"},"ANO")],-1),Wa={class:"flex gap-2"},el=e("span",{class:"font-light text-sm"},"Pokud je aktivní možnost odeslat heslo SMS, proběhne kontrola doplnění tel. čísel. V případě nedoplnění tel. čísla budete vyzváni k doplnění a proces budete muset opakovat.",-1),tl={class:"col-span-12 sm:col-span-12 mb-2"},sl={class:"flex h-6 justify-start items-center"},ol=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"mustChangePassword",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Vyžádat změnu hesla po přihlášení")],-1),al={class:"border-t p-5"},ll={class:"text-right space-x-3"},nl=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Změnit hesla ",-1),il={class:"p-6 pb-0"},rl={class:"text-center text-xl mx-1"},dl={key:0,class:"border-b pb-6"},cl={key:1,class:"border-b pb-6"},ul={key:0,class:"grid grid-cols-2 divide-x"},ml={key:0,class:"col-span-1 space-y-1 py-4"},pl={key:0},vl={key:0},_l={key:1},hl={key:2},gl={key:1},xl=e("span",{class:"text-xs text-red-500"}," - Chybí některé osobní údaje",-1),fl={key:1,class:"col-span-1 space-y-1 py-4"},bl={key:0},yl={key:0},kl={key:1},$l={key:2},wl={key:1},Ul=e("span",{class:"text-xs text-red-500"}," - Chybí některé osobní údaje",-1),Cl={class:"col-span-1 space-y-5 py-6 px-8"},Vl=e("p",{class:"text-center text-sm"},"U zobrazovaných uživatelů chybí tel. číslo. Doplňte telefonní čísla ke všem uživatelům a opakujte proces změny hesla znovu.",-1),Ml=e("p",{class:"text-center text-sm font-semibold"},"Nyní nebyla žádná změna provedena.",-1),Ol={class:"border-t p-5"},Sl={class:"text-right space-x-3"},zl={class:"p-6 pb-0"},Pl={class:"text-center text-xl mx-1"},jl={key:0,class:"border-b pb-6"},Rl={key:1,class:"border-b pb-6"},Zl={class:"grid grid-cols-2 divide-x"},Tl={class:"col-span-1 space-y-6 py-8 text-center"},Dl=e("span",{class:"text-sm"},"Stažení hesel v papírové podobě",-1),Al={class:"col-span-1 space-y-5 py-6 px-8"},Il=e("p",{class:"text-center text-sm"},"U zvolených uživatelů proběhlo úspěšné restartování hesel.",-1),Yl=e("p",{class:"text-center text-sm font-semibold"},"V případě, že byla aktivována možnost odeslat hesla do SMS, uživatelům budou nyní hesla postupně rozeslány.",-1),El={class:"border-t p-5"},Bl={class:"text-right space-x-3"},Ll={__name:"resetUsersPasswordsModal",props:{openModal:Boolean},emits:["reloadUsersTable"],setup(R,{expose:P,emit:O}){le("debugModeGlobalVar");const y=ie(),_=u([]),g=u(0),n=u(0),U=u({}),h=u(null),l=u(null),m=u(null),$=u(!1);function r(){$.value=!1}function c(C,z){h.value=C,n.value=0,z&&(_.value=z),$.value=!0}function j(){g.value=!g.value,g.value==!0?g.value=1:g.value=0}function S(C){if(C=="organization_unit")h.value=="organization_unit",I.post("/api/organization-units/generate-password",{organization_units:[y.treePosition.id],send_sms:g.value,must_change_password:n.value},{responseType:"blob",headers:{"content-type":"multipart/form-data"}}).then(d=>{l.value=d.headers["file-name"],m.value=window.URL.createObjectURL(new Blob([d.data],{type:d.headers["content-type"]})),q.success("Hesla byla úspěšně změněna!"),g.value=0,r(),F(),_.value=[],O("reloadUsersTable",!0)}).catch(d=>{w(d.response.data),console.log(d)});else if(C=="users"){h.value=="users";var z=[];_.value.forEach(d=>{z.push(d.id)}),I.post("/api/users/generate-password",{users:z,send_sms:g.value,must_change_password:n.value},{responseType:"blob",headers:{"content-type":"multipart/form-data"}}).then(d=>{z=[],l.value=d.headers["file-name"],m.value=window.URL.createObjectURL(new Blob([d.data],{type:d.headers["content-type"]})),q.success("Hesla byla úspěšně změněna!"),g.value=0,r(),F(),_.value=[],O("reloadUsersTable",!0)}).catch(d=>{w(d.response.data),console.log(d)})}}async function w(C){try{const z=await C,d=await new Response(z).text(),Y=JSON.parse(d);U.value=Y.data,g.value=0,A(),$.value==!0&&r(),resetSelectedUsersPasswordsModal.value==!0&&closeResetSelectedUsersPasswordsModal()}catch(z){console.error("Error fetching data:",z)}}const k=u(!1);function B(){k.value=!1,U.value={}}function A(){k.value=!0,_.value=[]}const M=u(!1);function H(){M.value=!1}function F(){M.value=!0}function K(){var C=l.value;C=decodeURIComponent(C),C=C.replaceAll("+"," ");var z=m.value,d=document.createElement("a");d.href=z,d.setAttribute("download",C),document.body.appendChild(d),d.click(),l.value=null,m.value=null,H()}return P({openModal:c}),(C,z)=>(s(),a(J,null,[o(t(te),{appear:"",show:$.value,as:"template",onClose:z[7]||(z[7]=d=>r())},{default:i(()=>[o(ee,null,$e({"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:z[0]||(z[0]=d=>r())},[o(t(W),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[o(t(ue),{onSubmit:z[6]||(z[6]=d=>S(h.value))},{default:i(({values:d})=>[e("div",$a,[h.value=="organization_unit"?(s(),a("div",wa,[e("div",Ua,[Ca,t(y).treePosition.name?(s(),a("h2",Va,p(t(y).treePosition.name),1)):v("",!0)]),e("div",Ma,[Oa,e("div",Sa,[e("div",za,[o(t(T),{id:"sendPasswordSms","aria-describedby":"passwords-sms",name:"sendPasswordSms",onClick:z[1]||(z[1]=Y=>j()),type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",value:"0"})]),Pa]),e("div",ja,[e("div",null,[o(t(pe),{class:"h-5 w-5 text-main-color-600","aria-hidden":"true"})]),Ra]),e("div",Za,[e("div",Ta,[o(t(T),{id:"mustChangePassword","aria-describedby":"mustChangePassword",name:"mustChangePassword",modelValue:n.value,"onUpdate:modelValue":z[2]||(z[2]=Y=>n.value=Y),value:!n.value,type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"]),Da])])])])):(s(),a("div",Aa,[e("div",Ia,[Ya,_.value?(s(),a("div",Ea,[(s(!0),a(J,null,Q(_.value,Y=>(s(),a("div",{key:Y.id},[Y.first_name&&Y.last_name?(s(),a("div",Ba,[Y.first_name?(s(),a("span",La,p(Y.first_name+" "),1)):v("",!0),Y.middle_name?(s(),a("span",Ga,p(Y.middle_name+" "),1)):v("",!0),Y.last_name?(s(),a("span",Na,p(Y.last_name),1)):v("",!0)])):(s(),a("div",qa,[e("span",null,[Z(p(Y.account_name)+" ",1),Ha])]))]))),128))])):v("",!0)]),e("div",Fa,[Ka,e("div",Ja,[e("div",Xa,[o(t(T),{id:"sendPasswordSms","aria-describedby":"passwords-sms",name:"sendPasswordSms",onClick:z[3]||(z[3]=Y=>j()),type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",value:"0"})]),Qa]),e("div",Wa,[e("div",null,[o(t(pe),{class:"h-5 w-5 text-main-color-600","aria-hidden":"true"})]),el]),e("div",tl,[e("div",sl,[o(t(T),{id:"mustChangePassword","aria-describedby":"mustChangePassword",name:"mustChangePassword",modelValue:n.value,"onUpdate:modelValue":z[4]||(z[4]=Y=>n.value=Y),value:!n.value,type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"]),ol])])])]))]),e("div",al,[e("div",ll,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:z[5]||(z[5]=D(Y=>r(),["prevent"]))}," Zavřít "),nl])])]),_:1})]),_:2},[h.value=="organization_unit"?{name:"modal-title",fn:i(()=>[Z("Hromadná změna hesla")]),key:"0"}:{name:"modal-title",fn:i(()=>[Z("Změna hesla")]),key:"1"}]),1024)]),_:1},8,["show"]),o(t(te),{appear:"",show:k.value,as:"template",onClose:z[10]||(z[10]=d=>B())},{default:i(()=>[o(ee,null,$e({"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:z[8]||(z[8]=d=>B())},[o(t(W),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[e("div",il,[e("div",rl,[h.value=="organization_unit"?(s(),a("h2",dl,"Hromadná změna hesla se nepodařila.")):(s(),a("h2",cl,"Změna hesla se nepodařila."))]),U.value&&U.value[0]?(s(),a("div",ul,[U.value[0].users?(s(),a("div",ml,[(s(!0),a(J,null,Q(U.value[0].users,d=>(s(),a("div",{key:d.id,class:"flex items-center gap-x-3"},[o(t(Ue),{class:"h-5 w-5 text-amber-500","aria-hidden":"true"}),d.first_name&&d.last_name?(s(),a("div",pl,[d.first_name?(s(),a("span",vl,p(d.first_name+" "),1)):v("",!0),d.middle_name?(s(),a("span",_l,p(d.middle_name+" "),1)):v("",!0),d.last_name?(s(),a("span",hl,p(d.last_name),1)):v("",!0)])):(s(),a("div",gl,[e("span",null,[Z(p(d.account_name)+" ",1),xl])]))]))),128))])):(s(),a("div",fl,[(s(!0),a(J,null,Q(U.value,d=>(s(),a("div",{key:d.id,class:"flex items-center gap-x-3"},[o(t(Ue),{class:"h-5 w-5 text-amber-500","aria-hidden":"true"}),d.first_name&&d.last_name?(s(),a("div",bl,[d.first_name?(s(),a("span",yl,p(d.first_name+" "),1)):v("",!0),d.middle_name?(s(),a("span",kl,p(d.middle_name+" "),1)):v("",!0),d.last_name?(s(),a("span",$l,p(d.last_name),1)):v("",!0)])):(s(),a("div",wl,[e("span",null,[Z(p(d.account_name)+" ",1),Ul])]))]))),128))])),e("div",Cl,[o(t(ot),{class:"h-12 w-12 text-red-500 mx-auto","aria-hidden":"true"}),Vl,Ml])])):v("",!0)]),e("div",Ol,[e("div",Sl,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:z[9]||(z[9]=D(d=>B(),["prevent"]))}," Zavřít ")])])]),_:2},[h.value=="organization_unit"?{name:"modal-title",fn:i(()=>[Z("Hromadná změna hesla")]),key:"0"}:{name:"modal-title",fn:i(()=>[Z("Změna hesla")]),key:"1"}]),1024)]),_:1},8,["show"]),o(t(te),{appear:"",show:M.value,as:"template",onClose:z[14]||(z[14]=d=>H())},{default:i(()=>[o(ee,null,$e({"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:z[11]||(z[11]=d=>H())},[o(t(W),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[e("div",zl,[e("div",Pl,[h.value=="organization_unit"?(s(),a("h2",jl,"Hromadná změna hesla byla úspěšná")):(s(),a("h2",Rl,"Změna hesla byla úspěšná"))]),e("div",Zl,[e("div",Tl,[Dl,e("button",{class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700",onClick:z[12]||(z[12]=D(d=>K(),["prevent"]))}," Stáhnout hesla v PDF ")]),e("div",Al,[o(t(at),{class:"h-12 w-12 text-green-500 mx-auto","aria-hidden":"true"}),Il,Yl])])]),e("div",El,[e("div",Bl,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:z[13]||(z[13]=D(d=>H(),["prevent"]))}," Zavřít ")])])]),_:2},[h.value=="organization_unit"?{name:"modal-title",fn:i(()=>[Z("Hromadná změna hesla")]),key:"0"}:{name:"modal-title",fn:i(()=>[Z("Změna hesla")]),key:"1"}]),1024)]),_:1},8,["show"])],64))}};const Gl={class:"p-6 grid grid-cols-3"},Nl={class:"col-span-1"},ql={key:0,class:"text-lg text-gray-400 font-light"},Hl={key:1,class:"text-lg text-gray-400 font-light"},Fl={key:2,class:"col-span-1 space-y-1 py-4"},Kl={key:0},Jl={key:1},Xl={key:2},Ql={key:3,class:"col-span-1 py-4"},Wl={class:"uppercase text-2xl"},en={class:"col-span-2"},tn=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolte typ blokace",-1),sn={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block"},on=e("br",null,null,-1),an={class:"mt-4"},ln={class:"space-y-4 sm:flex sm:items-center sm:space-x-10 sm:space-y-0 mb-6"},nn=["for"],rn={key:0},dn={class:"rounded-l-full"},cn={class:"rounded-md border border-gray-300 block font-normal px-2.5 py-1.5 w-full text-sm leading-6 items-center justify-center space-x-2 sm:space-x-4 transition ease-out duration-300"},un={class:"flex items-center w-full"},mn={class:"w-6 h-6"},pn={class:"flex-1"},vn={key:0,class:"text-gray-900"},_n={key:1,class:"text-gray-400"},hn=e("span",null,"Zvolte datum od / do",-1),gn=[hn],xn={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block"},fn={key:1},bn={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block"},yn=e("br",null,null,-1),kn=e("span",null,"selected school hours",-1),$n=e("div",null,[e("span",{class:"text-lg text-gray-400 font-light"},"Vyučující hodiny:")],-1),wn={class:"space-x-2 mb-4"},Un=["for"],Cn={class:"selectedTimetablesDate"},Vn={class:"rounded-l-full"},Mn={class:"rounded-md border border-gray-300 block font-normal px-2.5 py-1.5 w-full text-sm leading-6 items-center justify-center space-x-2 sm:space-x-4 transition ease-out duration-300"},On={class:"flex items-center w-full"},Sn={class:"w-6 h-6"},zn={class:"flex-1"},Pn={key:0,class:"text-gray-900"},jn={key:1,class:"text-gray-400"},Rn=e("span",null,"Zvolte datum blokace..",-1),Zn=[Rn],Tn={key:2},Dn=e("div",{class:"text-center"},[e("span",{class:"text-gray-400 font-light"},"Nastavení nemá žádné možnosti.")],-1),An=[Dn],In={class:"border-t p-5"},Yn={class:"text-right space-x-3"},En=e("button",{type:"submit",class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600"}," Aktivovat blokaci ",-1),Bn={__name:"blockInternetModal",emits:["reloadUsersTable"],setup(R,{expose:P,emit:O}){const y=ie(),_=le("debugModeGlobalVar"),g=u(!1),n=u([]);ve(()=>{g.value=!0});const U=u(null);function h(){I.get("/api/timetables").then(K=>{U.value=K.data.data}).catch(K=>{console.log(K)})}const l=u("user"),m=[{id:1,title:"Termín od / do"},{id:2,title:"Vyučovací hodiny"},{id:3,title:"Trvale"}],$=u({date:"YYYY-MM-DD",month:"MM"}),r=u(null),c=u([]),j=u([]),S=u(null);function w(K){j.value.includes(K)?j.value=j.value.filter(C=>C!==K):j.value.push(K)}const k=u(!1);function B(){k.value=!1}function A(K,C){h(),n.value=[],C&&(n.value=C),l.value=K,j.value=[],S.value="",r.value=null,k.value=!0}function M(){l.value=="users"?H():l.value=="organization_unit"&&F(j.value)}function H(){var K=[];n.value.forEach(C=>{K.push(C.id)}),r.value==1?I.post("/api/internet-blocks/datetime",{users:K,from:ae(c.value[0]).format("YYYY-MM-DD HH:mm"),to:ae(c.value[1]).format("YYYY-MM-DD HH:mm")}).then(C=>{q.success(C.data.message),O("reloadUsersTable",!0),B(),c.value=["",""],n.value=[]}).catch(C=>{console.log(C)}):r.value==2?I.post("/api/internet-blocks/timetable",{users:K,date:ae(S.value).format("YYYY-MM-DD"),timetables:j.value}).then(C=>{q.success(C.data.message),O("reloadUsersTable",!0),B(),n.value=[]}).catch(C=>{console.log(C)}):r.value==3&&I.post("/api/internet-blocks/permanent",{users:K}).then(C=>{q.success(C.data.message),O("reloadUsersTable",!0),B(),n.value=[]}).catch(C=>{console.log(C)})}function F(K){r.value==1?I.post("/api/internet-blocks/organization-units/datetime/",{organization_units:[y.treePosition.id],from:ae(c.value[0]).format("YYYY-MM-DD HH:mm"),to:ae(c.value[1]).format("YYYY-MM-DD HH:mm")}).then(C=>{q.success(C.data.message),O("reloadUsersTable",!0),B(),n.value=[]}).catch(C=>{console.log(C)}):r.value==2?I.post("api/internet-blocks/organization-units/timetable/",{organization_units:[y.treePosition.id],date:ae(S.value).format("YYYY-MM-DD"),timetables:j.value}).then(C=>{q.success(C.data.message),O("reloadUsersTable",!0),B(),n.value=[]}).catch(C=>{console.log(C)}):r.value==3&&I.post("/api/internet-blocks/organization-units/permanent/",{organization_units:[y.treePosition.id]}).then(C=>{q.success(C.data.message),O("reloadUsersTable",!0),B(),n.value=[]}).catch(C=>{console.log(C)})}return P({openModal:A}),(K,C)=>(s(),L(t(te),{appear:"",show:k.value,as:"template",onClose:C[5]||(C[5]=z=>B())},{default:i(()=>[o(ee,null,{"modal-title":i(()=>[Z("Blokace Internetu")]),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:C[0]||(C[0]=z=>B())},[o(t(W),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[o(t(ue),{onSubmit:M},{default:i(({values:z})=>[e("div",Gl,[e("div",Nl,[l.value=="users"?(s(),a("span",ql,"Zvolení uživatele:")):l.value=="organization_unit"?(s(),a("span",Hl,"Zvolená OU:")):v("",!0),n.value&&l.value=="users"?(s(),a("div",Fl,[(s(!0),a(J,null,Q(n.value,d=>(s(),a("div",{key:d.id},[e("div",null,[d.first_name?(s(),a("span",Kl,p(d.first_name+" "),1)):v("",!0),d.middle_name?(s(),a("span",Jl,p(d.middle_name+" "),1)):v("",!0),d.last_name?(s(),a("span",Xl,p(d.last_name),1)):v("",!0)])]))),128))])):l.value=="organization_unit"?(s(),a("div",Ql,[e("span",Wl,p(t(y).treePosition.name),1)])):v("",!0)]),e("div",en,[tn,e("div",null,[t(_)?(s(),a("div",sn,[Z(p(r.value)+" ",1),on])):v("",!0),e("fieldset",an,[e("div",ln,[(s(),a(J,null,Q(m,d=>e("div",{key:d.id,class:"flex items-center"},[o(t(T),{id:d.id,name:"notification_method",type:"radio",rules:"requiredRadio",checked:d.id===r.value,modelValue:r.value,"onUpdate:modelValue":C[1]||(C[1]=Y=>r.value=Y),value:d.id,class:"h-4 w-4 border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["id","checked","modelValue","value"]),e("label",{for:d.id,class:"ml-3 block text-sm font-medium leading-6 text-gray-900 cursor-pointer"},p(d.title),9,nn)])),64))]),o(t(X),{name:"notification_method",class:"text-rose-400 text-sm block pt-1"}),r.value==1?(s(),a("div",rn,[e("div",null,[o(t(T),{name:"blockedInternetDate",rules:"required"},{default:i(({handleChange:d,value:Y})=>[o(t(Ce),{i18n:"cs","use-range":"",shortcuts:!1,modelValue:c.value,"onUpdate:modelValue":[C[2]||(C[2]=se=>c.value=se),d],formatter:$.value},{default:i(({clear:se})=>[e("div",null,[e("div",dn,[e("button",cn,[e("div",un,[e("div",mn,[c.value&&c.value[0]&&c.value[1]?(s(),L(t(W),{key:0,onClick:_e=>K.unsetBlockedInternetDate(),class:"h-6 w-6 text-gray-900","aria-hidden":"true"},null,8,["onClick"])):(s(),L(t(we),{key:1,class:"h-6 w-6 text-gray-900","aria-hidden":"true"}))]),e("div",pn,[c.value&&c.value[0]&&c.value[1]?(s(),a("span",vn,[e("span",null,p(t(ae)(c.value[0]).format("DD.MM.YYYY"))+" - "+p(t(ae)(c.value[1]).format("DD.MM.YYYY")),1)])):(s(),a("span",_n,gn))])])])])])]),_:2},1032,["modelValue","onUpdate:modelValue","formatter"]),t(_)?(s(),a("div",xn,[e("p",null,"value: "+p(Y),1)])):v("",!0)]),_:1}),o(t(X),{name:"blockedInternetDate",class:"text-rose-400 text-sm block pt-1"})])])):r.value==2?(s(),a("div",fn,[t(_)?(s(),a("div",bn,[Z(p(U.value)+" ",1),yn,kn,Z(p(j.value)+" ",1),e("span",null,"selected school hours date: "+p(t(ae)(S.value).format("YYYY-MM-DD")),1)])):v("",!0),$n,e("div",null,[e("fieldset",null,[e("div",wn,[(s(!0),a(J,null,Q(U.value,d=>(s(),a("div",{key:d.id,class:"inline-block border p-2 text-center"},[e("div",null,[e("label",{for:"timetable"+d.id,class:"font-medium text-gray-900 cursor-pointer"},p(d.teaching_hour_number),9,Un)]),e("div",null,[o(t(T),{type:"checkbox",onClick:Y=>w(d.id),id:"timetable"+d.id,value:d.id,name:"timetables",rules:"requiredCheckbox",class:"h-5 w-5 rounded cursor-pointer border-gray-300 text-main-color-600 focus:ring-transparent"},null,8,["onClick","id","value"])])]))),128)),o(t(X),{name:"timetables",class:"text-rose-400 text-sm"})])]),e("div",Cn,[o(t(T),{name:"blockedTimetableDate",rules:"required"},{default:i(({handleChange:d,value:Y})=>[o(t(Ce),{name:"blockedTimetableDate",i18n:"cs","as-single":"",shortcuts:!1,modelValue:S.value,"onUpdate:modelValue":[C[3]||(C[3]=se=>S.value=se),d],formatter:$.value,placeholder:"Zvolte datum blokace.."},{default:i(({clear:se})=>[e("div",null,[e("div",Vn,[e("button",Mn,[e("div",On,[e("div",Sn,[S.value?(s(),L(t(W),{key:0,onClick:se,class:"h-6 w-6 text-gray-900","aria-hidden":"true"},null,8,["onClick"])):(s(),L(t(we),{key:1,class:"h-6 w-6 text-gray-900","aria-hidden":"true"}))]),e("div",zn,[S.value?(s(),a("span",Pn,[e("span",null,p(t(ae)(S.value).format("DD.MM.YYYY")),1)])):(s(),a("span",jn,Zn))])])])])])]),_:2},1032,["modelValue","onUpdate:modelValue","formatter"])]),_:1}),o(t(X),{name:"blockedTimetableDate",class:"text-rose-400 text-sm block pt-1"})])])])):r.value==3?(s(),a("div",Tn,An)):v("",!0)])])])]),e("div",In,[e("div",Yn,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:C[4]||(C[4]=D(d=>B(),["prevent"]))}," Zavřít "),En])])]),_:1})]),_:1})]),_:1},8,["show"]))}},Ln={class:"p-6"},Gn={class:"col-span-1"},Nn=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolení uživatele:",-1),qn={key:0,class:"col-span-1 space-y-1 py-4"},Hn={key:0},Fn={key:1},Kn={key:2},Jn={class:"border-t p-5"},Xn={class:"text-right space-x-3"},Qn={__name:"deactivateUsersModal",emits:["reloadUsersTable"],setup(R,{expose:P,emit:O}){le("debugModeGlobalVar");const y=u([]),_=u(!1);function g(){_.value=!1}function n(h){h&&(y.value=h),_.value=!0}function U(){var h=[];y.value.forEach(l=>{h.push(l.id)}),I.post("/api/users/disable",{users:h}).then(l=>{q.success(l.data.message),O("reloadUsersTable",!0),g(),y.value=[]}).catch(l=>{console.log(l)})}return P({openDeactivateUsersModal:n}),(h,l)=>(s(),L(t(te),{appear:"",show:_.value,as:"template",onClose:l[3]||(l[3]=m=>g())},{default:i(()=>[o(ee,null,{"modal-title":i(()=>[Z("Deaktivace účtu")]),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:l[0]||(l[0]=m=>g())},[o(t(W),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[e("div",Ln,[e("div",Gn,[Nn,y.value?(s(),a("div",qn,[(s(!0),a(J,null,Q(y.value,m=>(s(),a("div",{key:m.id},[e("div",null,[m.first_name?(s(),a("span",Hn,p(m.first_name+" "),1)):v("",!0),m.middle_name?(s(),a("span",Fn,p(m.middle_name+" "),1)):v("",!0),m.last_name?(s(),a("span",Kn,p(m.last_name),1)):v("",!0)])]))),128))])):v("",!0)])]),e("div",Jn,[e("div",Xn,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:l[1]||(l[1]=D(m=>g(),["prevent"]))}," Zavřít "),e("button",{class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-red-600",onClick:l[2]||(l[2]=D(m=>U(),["prevent"]))}," Deaktivovat účty ")])])]),_:1})]),_:1},8,["show"]))}},Wn={class:"p-6"},ei={class:"col-span-1"},ti=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolení uživatele:",-1),si={key:0,class:"col-span-1 space-y-1 py-4"},oi={key:0},ai={key:1},li={key:2},ni={class:"border-t p-5"},ii={class:"text-right space-x-3"},ri={__name:"enableUsersModal",emits:["reloadUsersTable"],setup(R,{expose:P,emit:O}){le("debugModeGlobalVar");const y=u([]),_=u(!1);function g(){_.value=!1}function n(h){h&&(y.value=h),_.value=!0}function U(){var h=[];y.value.forEach(l=>{h.push(l.id)}),I.post("/api/users/enable",{users:h}).then(l=>{q.success(l.data.message),O("reloadUsersTable",!0),g(),y.value=[]}).catch(l=>{console.log(l)})}return P({openEnableUsersModal:n}),(h,l)=>(s(),L(t(te),{appear:"",show:_.value,as:"template",onClose:l[3]||(l[3]=m=>g())},{default:i(()=>[o(ee,null,{"modal-title":i(()=>[Z("Aktivace účtu")]),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:l[0]||(l[0]=m=>g())},[o(t(W),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[e("div",Wn,[e("div",ei,[ti,y.value?(s(),a("div",si,[(s(!0),a(J,null,Q(y.value,m=>(s(),a("div",{key:m.id},[e("div",null,[m.first_name?(s(),a("span",oi,p(m.first_name+" "),1)):v("",!0),m.middle_name?(s(),a("span",ai,p(m.middle_name+" "),1)):v("",!0),m.last_name?(s(),a("span",li,p(m.last_name),1)):v("",!0)])]))),128))])):v("",!0)])]),e("div",ni,[e("div",ii,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:l[1]||(l[1]=D(m=>g(),["prevent"]))}," Zavřít "),e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",onClick:l[2]||(l[2]=D(m=>U(),["prevent"]))}," Aktivovat účty ")])])]),_:1})]),_:1},8,["show"]))}},di={class:"p-6"},ci={class:"grid grid-cols-2"},ui={class:"col-span-1"},mi=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolená OU:",-1),pi={key:0,class:"uppercase text-2xl"},vi={class:"col-span-1"},_i=e("span",{class:"text-lg text-gray-400 font-light"},"Zdroj importu:",-1),hi={class:"mt-2"},gi={class:"space-y-4 sm:flex sm:items-center sm:space-x-10 sm:space-y-0"},xi={class:"flex items-center"},fi=e("label",{for:"import_bakalari",class:"ml-3 block leading-6 text-gray-900 cursor-pointer"},"Bakaláři",-1),bi={class:"flex items-center"},yi=e("label",{for:"import_skola_online",class:"ml-3 block leading-6 text-gray-900 cursor-pointer"},"Škola online",-1),ki={class:"flex gap-2 pt-4"},$i=e("span",{class:"text-sm"},"Kliknutím na tlačítko Import založíte nové uživatele do zvolené organizační jednotky.",-1),wi={class:"py-4"},Ui={class:"rounded-md border border-gray-300 flex justify-between items-center font-normal px-2.5 py-1.5 w-full text-sm"},Ci={key:0,class:"text-base text-gray-900 font-light"},Vi={key:1,class:"text-base text-gray-400 font-light"},Mi=e("label",{for:"importUsers",class:"rounded-lg bg-main-color-200/75 px-4 py-2 text-xs text-main-color-600 shadow-sm hover:bg-main-color-200 cursor-pointer"},"Vybrat soubor",-1),Oi={class:"relative flex items-start"},Si={class:"flex h-6 items-center"},zi=e("div",{class:"ml-3 text-sm leading-6"},[e("label",{for:"ignore_ou",class:"text-gray-900 cursor-pointer"},"Ignorovat OU")],-1),Pi={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block"},ji=e("br",null,null,-1),Ri={class:"border-t p-5"},Zi={class:"text-right space-x-3"},Ti=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Importovat ",-1),Di={__name:"importUsersModal",emits:["reloadUsersTable"],setup(R,{expose:P,emit:O}){const y=le("debugModeGlobalVar"),_=ie();u(!1);const g=u("bakalari"),n=u(!1),U=ce(()=>{var S;return(S=h.value)==null?void 0:S.name}),h=u(null),l=S=>{h.value=S.target.files[0]},m=u(!1);function $(){m.value=!m.value}function r(){n.value=!1}function c(){h.value=null,m.value=!1,n.value=!0}const j=async()=>{const S=new FormData;S.append("type",g.value),S.append("file",h.value),S.append("ignore_organization_unit",m.value),S.append("organization_unit",_.treePosition.id);try{const w=await I.post("/api/users/import",S,{headers:{"Content-Type":"multipart/form-data"}});O("reloadUsersTable",!0),r(),q.success(w.data.message)}catch(w){q.error(w.data.message)}};return P({openModal:c}),(S,w)=>(s(),L(t(te),{appear:"",show:n.value,as:"template",onClose:w[5]||(w[5]=k=>r())},{default:i(()=>[o(ee,null,{"modal-title":i(()=>[Z("Importovat uživatele")]),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:w[0]||(w[0]=k=>r())},[o(t(W),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[o(t(ue),{onSubmit:j},{default:i(({values:k})=>[e("div",di,[e("div",ci,[e("div",ui,[mi,t(_).treePosition.name?(s(),a("h2",pi,p(t(_).treePosition.name),1)):v("",!0)]),e("div",vi,[_i,e("div",null,[e("fieldset",hi,[e("div",gi,[e("div",xi,[o(t(T),{id:"import_bakalari",rules:"requiredRadio",name:"import_users_checkbox",type:"radio",value:"bakalari",modelValue:g.value,"onUpdate:modelValue":w[1]||(w[1]=B=>g.value=B),class:"h-4 w-4 border-gray-300 text-indigo-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue"]),fi]),e("div",bi,[o(t(T),{id:"import_skola_online",rules:"requiredRadio",name:"import_users_checkbox",type:"radio",value:"skolaonline",modelValue:g.value,"onUpdate:modelValue":w[2]||(w[2]=B=>g.value=B),class:"h-4 w-4 border-gray-300 text-indigo-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue"]),yi])])]),o(t(X),{name:"import_users_checkbox",class:"text-rose-400 text-sm block pt-1"})])])]),e("div",ki,[e("div",null,[o(t(pe),{class:"h-5 w-5 text-main-color-600","aria-hidden":"true"})]),$i]),e("div",wi,[e("div",Ui,[t(U)?(s(),a("span",Ci,p(t(U)),1)):(s(),a("span",Vi,"Zvolte soubor k importu...")),o(t(T),{rules:"requiredFile",onChange:l,class:"hidden",type:"file",id:"importUsers",name:"importUsers",accept:".xlsx"}),Mi]),o(t(X),{name:"importUsers",class:"text-rose-400 text-sm block pt-1"})]),e("div",Oi,[e("div",Si,[o(t(T),{id:"ignore_ou",name:"ignore_ou",value:"false",onClick:w[3]||(w[3]=B=>$()),type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"})]),zi]),t(y)?(s(),a("div",Pi,[e("span",null,"ignore ou: "+p(m.value),1),ji,e("span",null,"selected import source: "+p(g.value),1)])):v("",!0)]),e("div",Ri,[e("div",Zi,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:w[4]||(w[4]=D(B=>r(),["prevent"]))}," Zavřít "),Ti])])]),_:1})]),_:1})]),_:1},8,["show"]))}};const Ai={class:"p-6 promotion-modal-data"},Ii={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block"},Yi=e("h3",{class:"text-center text-gray-500 text-lg font-light pb-6"},"Seznam ročníků dostupných k povýšení",-1),Ei={class:"max-h-64 overflow-y-scroll pr-4"},Bi={class:"grid grid-cols-2 gap-4 py-3 px-4"},Li={class:"cols-span-1 flex justify-between items-center"},Gi={class:"text-lg text-gray-900"},Ni={class:"cols-span-1"},qi={class:"flex gap-2 pt-10"},Hi=e("span",{class:"font-light text-sm"},"Ke každé OU zadejte nový název. Pro urychlení se systém pokusil doplnit nový název automaticky.",-1),Fi={class:"border-t p-5"},Ki={class:"text-right space-x-3"},Ji=e("button",{type:"submit",class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600"}," Povýšit ročníky ",-1),Xi={__name:"promoteOuModal",emits:["reloadUsersTable"],setup(R,{expose:P,emit:O}){const y=le("debugModeGlobalVar"),_=ie(),g=u(!1);function n(){I.get("/api/organization-units/promotions").then(c=>{U.value=c.data.data,g.value=!1}).catch(c=>{console.log(c)})}const U=u({}),h=u(!1);function l(){h.value=!1}function m(){n(),$.value=[],h.value=!0}const $=u([]);function r(){$.value=[],I.post("/api/organization-units/promotions",{organization_units:U.value}).then(c=>{q.success(c.data.message),_.reloadAdTree(!0),O("reloadUsersTable",!0),l()}).catch(c=>{c.response.data.data.failed.forEach(j=>{$.value.push(j)})})}return P({openModal:m}),(c,j)=>(s(),L(t(te),{appear:"",show:h.value,as:"template",onClose:j[2]||(j[2]=S=>l())},{default:i(()=>[o(ee,null,{"modal-title":i(()=>[Z("Povýšení ročníků")]),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:j[0]||(j[0]=S=>l())},[o(t(W),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[o(t(ue),{onSubmit:r},{default:i(({values:S})=>[e("div",Ai,[t(y)?(s(),a("div",Ii,[e("span",null,"failed promotions: "+p($.value),1)])):v("",!0),Yi,e("div",Ei,[(s(!0),a(J,null,Q(U.value,w=>(s(),a("div",{key:w.id,class:"border-t"},[e("div",Bi,[e("div",Li,[e("span",null,p(w.id),1),e("span",Gi,p(w.name),1),o(t(lt),{class:"h-7 w-7 p-1 text-gray-900","aria-hidden":"true"})]),e("div",Ni,[o(t(T),{type:"text",name:"promotion-id:"+w.id,rules:"required",id:"promotion-id:"+w.id,modelValue:w.new_name,"onUpdate:modelValue":k=>w.new_name=k,class:N(["block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 duration-150",$.value.includes(w.id)?"bg-red-50 ring-red-500":""]),placeholder:"Zadejte název nové OU..."},null,8,["name","id","modelValue","onUpdate:modelValue","class"]),o(t(X),{name:"promotion-id:"+w.id,class:"text-rose-400 text-sm block pt-1"},null,8,["name"])])])]))),128))]),e("div",qi,[e("div",null,[o(t(pe),{class:"h-5 w-5 text-main-color-600","aria-hidden":"true"})]),Hi])]),e("div",Fi,[e("div",Ki,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:j[1]||(j[1]=D(w=>l(),["prevent"]))}," Zavřít "),Ji])])]),_:1})]),_:1})]),_:1},8,["show"]))}},Qi={class:"grid grid-cols-2"},Wi={class:"col-span-1 p-6"},er=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolení uživatele:",-1),tr={key:0,class:"col-span-1 space-y-1 py-4"},sr={key:0},or={key:0},ar={key:1},lr={key:2},nr={key:1},ir=e("span",{class:"text-xs text-red-500"}," - Chybí některé osobní údaje",-1),rr={class:"col-span-1 p-6"},dr={class:"border-t p-5"},cr={class:"text-right space-x-3"},ur={__name:"changeGroupModal",emits:["reloadUsersTable"],setup(R,{expose:P,emit:O}){const y=u(!1),_=u([]),g=u([]),n=u([]);function U(){y.value=!1}function h($){if(console.log($),n.value=[],$)for(let r=0;r<$.length;r++)$[r].active_directory===1&&n.value.push($[r]);y.value=!0,l()}function l(){I.get("/api/groups?perpage=9999").then($=>{_.value=$.data.data}).catch($=>{console.log($)})}function m(){var $=[],r=[];n.value.forEach(c=>{$.push(c.id)}),g.value.forEach(c=>{r.push(c.id)}),I.post("/api/users/ad-group-update",{users:$,groups:r}).then(c=>{q.success("Změna skupin byla úspěšná!"),g.value=[],O("reloadUsersTable",!0),U()}).catch(c=>{console.log(c)})}return P({openModal:h}),($,r)=>(s(),L(t(te),{appear:"",show:y.value,as:"template",onClose:r[4]||(r[4]=c=>U())},{default:i(()=>[o(ee,null,{"modal-title":i(()=>[Z("Změna skupiny")]),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:r[0]||(r[0]=c=>U())},[o(t(W),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[e("div",Qi,[e("div",Wi,[er,n.value?(s(),a("div",tr,[(s(!0),a(J,null,Q(n.value,c=>(s(),a("div",{key:c.id},[c.first_name&&c.last_name?(s(),a("div",sr,[c.first_name?(s(),a("span",or,p(c.first_name+" "),1)):v("",!0),c.middle_name?(s(),a("span",ar,p(c.middle_name+" "),1)):v("",!0),c.last_name?(s(),a("span",lr,p(c.last_name),1)):v("",!0)])):(s(),a("div",nr,[e("span",null,[Z(p(c.account_name)+" ",1),ir])]))]))),128))])):v("",!0)]),e("div",rr,[o(t(T),{name:"selectedGroups",rules:"required"},{default:i(({handleChange:c,value:j})=>[o(t(Be),{name:"selectedGroups",modelValue:g.value,"onUpdate:modelValue":[r[1]||(r[1]=S=>g.value=S),c],mode:"tags",label:"name","value-prop":"id",options:_.value,object:!0,placeholder:"Zvolte si skupiny",class:"!rounded-lg"},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1})])]),e("div",dr,[e("div",cr,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:r[2]||(r[2]=D(c=>U(),["prevent"]))}," Zavřít "),e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",onClick:r[3]||(r[3]=D(c=>m(),["prevent"]))}," Uložit ")])])]),_:1})]),_:1},8,["show"]))}},mr={class:"grid grid-cols-2"},pr={class:"col-span-1 p-6"},vr=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolení uživatele:",-1),_r={key:0,class:"col-span-1 space-y-1 py-4"},hr={key:0},gr={key:0},xr={key:1},fr={key:2},br={key:1},yr=e("span",{class:"text-xs text-red-500"}," - Chybí některé osobní údaje",-1),kr={class:"col-span-1 p-6"},$r=e("div",{class:"flex items-center my-4"},[e("p",{class:"ml-4 text-lg text-gray-900"},"Zařazení do OU")],-1),wr={class:"relative mt-1"},Ur={key:0,class:"block truncate"},Cr={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},Vr={key:0,class:"absolute inset-y-0 left-0 flex items-center pl-3 text-main-color-600"},Mr={class:"border-t p-5"},Or={class:"text-right space-x-3"},Sr={__name:"changeOuModal",emits:["reloadUsersTable"],setup(R,{expose:P,emit:O}){const y=u(!1),_=u({}),g=u(),n=u([]);function U(){y.value=!1}function h($){if(console.log($),n.value=[],$)for(let r=0;r<$.length;r++)$[r].active_directory===1&&n.value.push($[r]);y.value=!0,l()}function l(){I.get("/api/organization-units?perpage=9999").then($=>{_.value=$.data.data,g.value=$.data.data[0]}).catch($=>{console.log($)})}function m(){var $=[];n.value.forEach(r=>{$.push(r.id)}),I.post("/api/users/ad-organization-unit-update",{users:$,organization_unit:g.value.id}).then(r=>{q.success("Změna organizační jednotky byla úspěšná!"),O("reloadUsersTable",!0),U()}).catch(r=>{console.log(r)})}return P({openModal:h}),($,r)=>(s(),L(t(te),{appear:"",show:y.value,as:"template",onClose:r[4]||(r[4]=c=>U())},{default:i(()=>[o(ee,null,{"modal-title":i(()=>[Z("Změna Organizační jednotky")]),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:r[0]||(r[0]=c=>U())},[o(t(W),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[e("div",mr,[e("div",pr,[vr,n.value?(s(),a("div",_r,[(s(!0),a(J,null,Q(n.value,c=>(s(),a("div",{key:c.id},[c.first_name&&c.last_name?(s(),a("div",hr,[c.first_name?(s(),a("span",gr,p(c.first_name+" "),1)):v("",!0),c.middle_name?(s(),a("span",xr,p(c.middle_name+" "),1)):v("",!0),c.last_name?(s(),a("span",fr,p(c.last_name),1)):v("",!0)])):(s(),a("div",br,[e("span",null,[Z(p(c.account_name)+" ",1),yr])]))]))),128))])):v("",!0)]),e("div",kr,[$r,o(t(Oe),{modelValue:g.value,"onUpdate:modelValue":r[1]||(r[1]=c=>g.value=c)},{default:i(()=>[e("div",wr,[o(t(Se),{class:"relative w-full text-left cursor-pointer block rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},{default:i(()=>[g.value&&g.value.name?(s(),a("span",Ur,p(g.value.name),1)):v("",!0),e("span",Cr,[o(t(ye),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]),_:1}),o(be,{"leave-active-class":"transition duration-100 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:i(()=>[_.value&&_.value.length?(s(),L(t(ze),{key:0,class:"absolute z-20 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:i(()=>[(s(!0),a(J,null,Q(_.value,c=>(s(),L(t(Pe),{key:c.name,value:c,as:"template"},{default:i(({active:j,selected:S})=>[e("li",{class:N([j?"bg-main-color-100 text-main-color-900":"text-gray-900","relative cursor-pointer select-none py-2 pl-10 pr-4"])},[e("span",{class:N([S?"font-medium":"font-normal","block truncate"])},p(c.name),3),S?(s(),a("span",Vr,[o(t(Ye),{class:"h-5 w-5","aria-hidden":"true"})])):v("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})):v("",!0)]),_:1})])]),_:1},8,["modelValue"])])]),e("div",Mr,[e("div",Or,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:r[2]||(r[2]=D(c=>U(),["prevent"]))}," Zavřít "),e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",onClick:r[3]||(r[3]=D(c=>m(),["prevent"]))}," Uložit ")])])]),_:1})]),_:1},8,["show"]))}},zr={class:"grid grid-cols-2"},Pr={class:"col-span-1 p-6"},jr=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolení uživatele:",-1),Rr={key:0,class:"col-span-1 space-y-1 py-4"},Zr={key:0},Tr={key:0},Dr={key:1},Ar={key:2},Ir={key:1},Yr=e("span",{class:"text-xs text-red-500"}," - Chybí některé osobní údaje",-1),Er=e("div",{class:"col-span-1 p-6"},[e("div",{class:"flex items-center my-4"},[e("p",{class:"ml-4 text-lg text-gray-900"},"Opravdu si přejete uživatele smazat?")])],-1),Br={class:"border-t p-5"},Lr={class:"text-right space-x-3"},Gr={__name:"deleteUsersModal",emits:["reloadUsersTable"],setup(R,{expose:P,emit:O}){const y=u(!1),_=u([]);function g(){y.value=!1}function n(h){if(console.log(h),_.value=[],h)for(let l=0;l<h.length;l++)_.value.push(h[l]);y.value=!0}function U(){if(_.value.length>1){var h=[];_.value.forEach(l=>{h.push(l.id)}),I.post("/api/users/delete",{users:h}).then(l=>{q.success(l.data.message),O("reloadUsersTable",!0),g()}).catch(l=>{console.log(l)})}else I.post("/api/users/"+_.value[0].id+"/delete").then(l=>{q.success(l.data.message),O("reloadUsersTable",!0),g()}).catch(l=>{console.log(l)})}return P({openModal:n}),(h,l)=>(s(),L(t(te),{appear:"",show:y.value,as:"template",onClose:l[3]||(l[3]=m=>g())},{default:i(()=>[o(ee,null,{"modal-title":i(()=>[Z("Smazat uživatele")]),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:l[0]||(l[0]=m=>g())},[o(t(W),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[e("div",zr,[e("div",Pr,[jr,_.value?(s(),a("div",Rr,[(s(!0),a(J,null,Q(_.value,m=>(s(),a("div",{key:m.id},[m.first_name&&m.last_name?(s(),a("div",Zr,[m.first_name?(s(),a("span",Tr,p(m.first_name+" "),1)):v("",!0),m.middle_name?(s(),a("span",Dr,p(m.middle_name+" "),1)):v("",!0),m.last_name?(s(),a("span",Ar,p(m.last_name),1)):v("",!0)])):(s(),a("div",Ir,[e("span",null,[Z(p(m.account_name)+" ",1),Yr])]))]))),128))])):v("",!0)]),Er]),e("div",Br,[e("div",Lr,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:l[1]||(l[1]=D(m=>g(),["prevent"]))}," Zavřít "),e("button",{class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-red-600",onClick:l[2]||(l[2]=D(m=>U(),["prevent"]))}," Smazat ")])])]),_:1})]),_:1},8,["show"]))}};const Nr={class:"flex"},qr=e("span",null,"Synchronizovat AD",-1),Hr={class:"grid grid-cols-12 gap-4"},Fr={class:"col-span-12 xl:col-span-3"},Kr={class:"col-span-12 xl:col-span-9"},Jr={class:"px-0"},Xr={class:"bg-white border border-zinc-200/70 rounded-md p-5"},Qr={class:"grid grid-cols-12 gap-y-4"},Wr={class:"col-span-12 md:col-span-8 flex items-center gap-6"},ed={class:"w-72"},td={class:"space-y-5"},sd={class:"relative flex items-start"},od={class:"flex h-6 items-center"},ad=["checked"],ld=e("div",{class:"ml-3 text-sm leading-6"},[e("label",{for:"missing_phone",class:"text-gray-900 cursor-pointer"},"Chybějící telefon")],-1),nd={class:"col-span-12 md:col-span-4 flex md:justify-end flex-wrap gap-x-2 gap-y-2"},id=e("span",null,"Restartovat",-1),rd=[id],dd={class:"py-6 flex flex-col 2xl:flex-row 2xl:justify-between 2xl:items-center gap-y-4"},cd={class:"flex items-end gap-4"},ud=e("span",{class:"text-lg text-gray-400"},"OU",-1),md={key:0,class:"uppercase text-2xl"},pd={class:"flex items-center flex-wrap gap-3"},vd={class:"flex"},_d=e("span",null,"Povýšení ročníků",-1),hd=["disabled"],gd={class:"flex"},xd=e("span",null,"Import účtů",-1),fd=["disabled"],bd={class:"flex"},yd=e("span",null,"Blokace internetu",-1),kd=["disabled"],$d={class:"flex"},wd=e("span",null,"Změna hesla",-1),Ud={class:"flow-root bg-white border border-zinc-200/70 rounded-md overflow-scroll md:overflow-x-visible"},Cd={class:"inline-block w-full align-middle"},Vd={key:0,class:"min-w-full divide-y divide-gray-200"},Md={scope:"col",class:"py-4 pl-5 pr-3 text-center text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"},Od=["checked"],Sd=e("th",{scope:"col",class:"py-4 pl-5 pr-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"}," Jméno ",-1),zd=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"},"Příjmení ",-1),Pd=e("th",{scope:"col",class:"pl-10 pr-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Login ",-1),jd=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"},"Telefon ",-1),Rd=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"},"Email ",-1),Zd=e("th",{scope:"col",class:"py-4 pl-10 pr-5 text-left text-sm font-semibold text-gray-900 rounded-tr-md bg-gray-100/70"}," Akce ",-1),Td={key:0,class:"divide-y divide-gray-200"},Dd=["onClick","value","checked"],Ad={class:"flex gap-2 items-center"},Id={key:0,class:"bg-main-color-100 rounded-full p-1"},Yd={key:1,class:"w-5"},Ed={key:0},Bd={key:1,class:"flex items-center gap-2"},Ld=e("span",{class:"text-amber-500"},"Chybí",-1),Gd={class:"flex items-center gap-2"},Nd={key:0},qd={key:1,class:"w-5"},Hd={class:"flex items-center gap-2"},Fd={key:0,class:"px-1 py-1"},Kd=["onClick"],Jd={key:1,class:"px-1 py-1"},Xd=["onClick"],Qd={key:2,class:"px-1 py-1"},Wd=["onClick"],ec={key:3,class:"px-1 py-1"},tc=["onClick"],sc={key:4,class:"px-1 py-1"},oc=["onClick"],ac={key:5,class:"px-1 py-1 text-left"},lc=["onClick"],nc=e("div",{class:"whitespace-pre break-words text-left"},[e("p",{class:"w-full break-words"},[Z("Změnit organizační "),e("br"),Z("jednotku")])],-1),ic=[nc],rc={key:6,class:"px-1 py-1"},dc=["onClick"],cc={key:1},uc=e("tr",null,[e("td",{colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},"Nebyli nalezeni žádní úživatelé")],-1),mc=[uc],pc={class:"bg-gray-100/70"},vc={colspan:"7",class:"py-4 pl-5 px-3 text-sm text-gray-900 bg-gray-100/70"},_c={class:"flex items-center gap-2"},hc=e("span",null,"Označené:",-1),gc={class:"absolute -translate-y-5 pt-0.5"},xc={key:0,class:"block truncate"},fc={key:1,class:"block h-6 text-gray-400"},bc={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},yc={key:1,class:"rounded-lg bg-main-color-300 px-4 py-2 text-sm text-white shadow-sm cursor-not-allowed"},kc={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block"},$c={key:0},wc=e("br",null,null,-1),Uc=e("br",null,null,-1),Cc=e("br",null,null,-1),Vc=e("br",null,null,-1),qc={__name:"Users",setup(R){const P=u(),O=u(),y=le("debugModeGlobalVar"),_=u(["skolasys-root","users"]),g=[{id:1,name:"Aktivovat účty",permission:"users.edit"},{id:2,name:"Deaktivovat účty",permission:"users.edit"},{id:3,name:"Resetovat hesla",permission:"users.set_password"},{id:4,name:"Blokace internetu",permission:"internet_blocks.create"},{id:5,name:"Změnit skupinu",permission:"users.edit"},{id:6,name:"Změnit organizační jednotku",permission:"users.edit"},{id:7,name:"Smazat uživatele",permission:"users.delete"}],n=u(),U=ie(),h=u([]),l=u(null),m=u(!1),$=u([]),r=u([]),c=u(""),j=u(1),S=u(0),w=u(null),k=u(null),B=u(null),A=u(null),M=u(null),H=u(null),F=u(null),K=u(null),C=u(null),z=u(null);ve(()=>{m.value=!0,d()}),me(()=>U.treePosition,(f,b)=>{m.value=!0,he(),d()}),me(()=>U.perPage,(f,b)=>{m.value=!0,j.value=1,d()});function d(){if(G.check("users.read")){let f="";c.value&&c.value.length&&c.value.length>0?f="":f=U.treePosition.id,I.get("/api/users?page="+j.value+"&perpage="+U.perPage+"&search="+c.value+"&organization_unit="+f+"&missing_tel_number="+S.value).then(b=>{h.value=b.data.data,l.value=b.data.meta,$.value=[],r.value=[],b.data.data.filter((V,ne)=>{$.value.push(V)}),m.value=!1}).catch(b=>{console.log(b),q.error(b.data.message)})}else m.value=!1}function Y(f){r.value.includes(f)?r.value=r.value.filter(b=>b!==f):r.value.push(f)}function se(){$.value.length!==r.value.length?(r.value=[],h.value.filter((f,b)=>{r.value.push(f)})):r.value=[]}function _e(f){j.value=f,r.value=[],d()}function ke(){S.value=!S.value,S.value==!0?S.value=1:S.value=0}function he(){m.value=!0,j.value=1,c.value="",S.value=0,d()}function ge(){m.value=!0,j.value=1,r.value=[],d()}function E(){n.value&&r.value.length?n.value.id==1?M.value.openEnableUsersModal(r.value):n.value.id==2?A.value.openDeactivateUsersModal(r.value):n.value.id==3?G.check("users.set_password")&&(w.value="users",k.value.openModal("users",r.value)):n.value.id==4?G.check("internet_blocks.create")&&B.value.openModal("users",r.value):n.value.id==5?G.check("users.edit")&&K.value.openModal(r.value):n.value.id==6?G.check("users.edit")&&C.value.openModal(r.value):n.value.id==7&&G.check("users.delete")&&z.value.openModal(r.value):q.error("Nebyli vybráni žádní uživatele!")}return(f,b)=>{const V=fe("router-link"),ne=fe("VueSpinner");return s(),a(J,null,[o(Je,{breadCrumbs:_.value},{topbarButtons:i(()=>[e("div",null,[t(G).check("active_directory.sync")?(s(),a("button",{key:0,onClick:b[0]||(b[0]=x=>f.$refs.syncAdRef.openModal()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},[e("div",Nr,[o(t(nt),{class:"mr-2 h-5 w-5 text-main-color-600","aria-hidden":"true"}),qr])])):v("",!0)]),t(G).check("users.create")?(s(),a("button",{key:0,onClick:b[1]||(b[1]=x=>f.$refs.createUserRef.openModal()),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},"Přidat uživatele ")):v("",!0)]),_:1},8,["breadCrumbs"]),e("div",Hr,[e("div",Fr,[o(Os)]),e("div",Kr,[e("div",Jr,[e("div",Xr,[e("div",Qr,[e("div",Wr,[e("div",ed,[He(e("input",{type:"text",name:"search",id:"search","onUpdate:modelValue":b[2]||(b[2]=x=>c.value=x),onKeyup:b[3]||(b[3]=Ke(x=>ge(),["enter"])),class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Zadejte hledaný výraz..."},null,544),[[Fe,c.value]])]),e("fieldset",null,[e("div",td,[e("div",sd,[e("div",od,[e("input",{id:"missing_phone",name:"missing_phone",onClick:b[4]||(b[4]=x=>ke()),type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",checked:S.value},null,8,ad)]),ld])])])]),e("div",nd,[e("div",null,[e("button",{onClick:b[5]||(b[5]=x=>he()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},rd)]),e("button",{onClick:b[6]||(b[6]=x=>ge()),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700"}," Filtrovat ")])])])]),e("div",dd,[e("div",cd,[ud,t(U).treePosition.name?(s(),a("h2",md,p(t(U).treePosition.name),1)):v("",!0)]),e("div",pd,[t(G).check("active_directory_ou.promotion")?(s(),a("button",{key:0,onClick:b[7]||(b[7]=D(x=>f.$refs.promoteOuRef.openModal(),["prevent"])),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 font-medium shadow-sm hover:bg-main-color-200 disabled:opacity-50 disabled:hover:bg-main-color-200/75 disabled:cursor-not-allowed"},[e("div",vd,[o(t(De),{class:"mr-2 h-5 w-5 text-main-color-600","aria-hidden":"true"}),_d])])):v("",!0),t(G).check("users.create")?(s(),a("button",{key:1,onClick:b[8]||(b[8]=D(x=>f.$refs.importUsersRef.openModal(),["prevent"])),disabled:!t(U).treePosition.id,class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 font-medium shadow-sm hover:bg-main-color-200 disabled:opacity-50 disabled:hover:bg-main-color-200/75 disabled:cursor-not-allowed"},[e("div",gd,[o(t(it),{class:"mr-2 h-5 w-5 text-main-color-600","aria-hidden":"true"}),xd])],8,hd)):v("",!0),t(G).check("internet_blocks.create")?(s(),a("button",{key:2,onClick:b[9]||(b[9]=D(x=>f.$refs.blockInternetRef.openModal("organization_unit",null),["prevent"])),disabled:!t(U).treePosition.id,class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 font-medium shadow-sm hover:bg-main-color-200 disabled:opacity-50 disabled:hover:bg-main-color-200/75 disabled:cursor-not-allowed"},[e("div",bd,[o(t(Re),{class:"mr-2 h-5 w-5 text-main-color-600","aria-hidden":"true"}),yd])],8,fd)):v("",!0),t(G).check("users.set_password")?(s(),a("button",{key:3,onClick:b[10]||(b[10]=D(x=>f.$refs.resetPasswordRef.openModal("organization_unit",null),["prevent"])),disabled:!t(U).treePosition.id,class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 font-medium shadow-sm hover:bg-main-color-200 disabled:opacity-50 disabled:hover:bg-main-color-200/75 disabled:cursor-not-allowed"},[e("div",$d,[o(t(Ie),{class:"mr-2 h-5 w-5 text-main-color-600","aria-hidden":"true"}),wd])],8,kd)):v("",!0)])]),e("div",null,[e("div",Ud,[e("div",null,[e("div",Cd,[m.value==!1?(s(),a("table",Vd,[e("thead",null,[e("tr",null,[e("th",Md,[e("input",{id:"users_select",name:"users_select",type:"checkbox",onClick:b[11]||(b[11]=x=>se()),checked:$.value.length&&$.value.length==r.value.length,class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent ring-transparent cursor-pointer"},null,8,Od)]),Sd,zd,Pd,jd,Rd,Zd])]),h.value&&h.value.length?(s(),a("tbody",Td,[(s(!0),a(J,null,Q(h.value,x=>(s(),a("tr",{key:x.id},[e("td",{class:N(["whitespace-nowrap py-4 pl-5 pr-3 text-center",{"bg-red-100/70":x.account_control_code&&x.account_control_code.id==3}])},[e("input",{id:"user_select",name:"user_select",type:"checkbox",onClick:()=>{Y(x)},value:x,class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",checked:r.value.includes(x)},null,8,Dd)],2),e("td",{class:N(["whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600",{"bg-red-100/70":x.account_control_code&&x.account_control_code.id==3}])},p(x.first_name),3),e("td",{class:N(["whitespace-nowrap px-3 py-4 text-sm text-gray-600",{"bg-red-100/70":x.account_control_code&&x.account_control_code.id==3}])},p(x.last_name),3),e("td",{class:N(["whitespace-nowrap px-3 py-4 text-sm text-gray-600",{"bg-red-100/70":x.account_control_code&&x.account_control_code.id==3}])},[e("div",Ad,[x.active_directory==0?(s(),a("span",Id,[o(t(rt),{class:"h-3 w-3 text-main-color-600","aria-hidden":"true"})])):(s(),a("span",Yd)),e("span",null,p(x.account_name),1)])],2),e("td",{class:N(["whitespace-nowrap px-3 py-4 text-sm text-gray-600",{"bg-red-100/70":x.account_control_code&&x.account_control_code.id==3}])},[x.phone?(s(),a("span",Ed,p(x.phone),1)):(s(),a("span",Bd,[o(t(Ue),{class:"h-5 w-5 text-amber-500","aria-hidden":"true"}),Ld]))],2),e("td",{class:N(["whitespace-nowrap px-3 py-4 text-sm text-gray-600",{"bg-red-100/70":x.account_control_code&&x.account_control_code.id==3}])},p(x.email),3),e("td",{class:N(["whitespace-nowrap px-3 py-4 text-sm text-gray-600",{"bg-red-100/70":x.account_control_code&&x.account_control_code.id==3}])},[e("div",Gd,[x.block_internet==1?(s(),a("span",Nd,[o(t(Re),{class:"h-5 w-5 text-red-600","aria-hidden":"true"})])):(s(),a("span",qd)),e("div",Hd,[t(G).check("users.edit")?(s(),L(V,{key:0,to:{name:"users-edit",params:{id:x.id}},class:"rounded-md bg-main-color-200/75 w-8 h-7 flex justify-center items-center text-main-color-600 shadow-sm hover:bg-main-color-200"},{default:i(()=>[o(t(dt),{class:"h-4 w-4 text-main-color-600","aria-hidden":"true"})]),_:2},1032,["to"])):v("",!0),o(t(kt),{as:"div",class:"inline-block text-left"},{default:i(()=>[e("div",null,[o(t(bt),{class:"rounded-md bg-main-color-200/75 w-8 h-7 flex justify-center items-center text-main-color-600 shadow-sm hover:bg-main-color-200"},{default:i(()=>[o(t(ye),{class:"h-6 w-6 text-main-color-600","aria-hidden":"true"})]),_:1})]),o(be,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:i(()=>[o(t(yt),{class:"absolute z-10 right-5 mt-2 w-40 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:i(()=>[x.active_directory&&t(G).check("users.edit")?(s(),a("div",Fd,[o(t(de),null,{default:i(({active:oe})=>[e("button",{onClick:D(re=>f.$refs.deactivateUsersRef.openDeactivateUsersModal([x]),["prevent"]),class:N([oe?"bg-main-color-600 text-white":"text-gray-900","group flex w-full items-center rounded-md px-2 py-2 text-sm"])}," Deaktivovat účet ",10,Kd)]),_:2},1024)])):v("",!0),x.active_directory&&t(G).check("users.edit")?(s(),a("div",Jd,[o(t(de),null,{default:i(({active:oe})=>[e("button",{onClick:D(re=>f.$refs.enableUsersRef.openEnableUsersModal([x]),["prevent"]),class:N([oe?"bg-main-color-600 text-white":"text-gray-900","group flex w-full items-center rounded-md px-2 py-2 text-sm"])}," Aktivovat účet ",10,Xd)]),_:2},1024)])):v("",!0),t(G).check("users.set_password")?(s(),a("div",Qd,[o(t(de),null,{default:i(({active:oe})=>[e("button",{onClick:D(re=>f.$refs.resetPasswordRef.openModal("users",[x]),["prevent"]),class:N([oe?"bg-main-color-600 text-white":"text-gray-900","group flex w-full items-center rounded-md px-2 py-2 text-sm"])}," Resetovat heslo ",10,Wd)]),_:2},1024)])):v("",!0),t(G).check("internet_blocks.create")?(s(),a("div",ec,[o(t(de),null,{default:i(({active:oe})=>[e("button",{onClick:D(re=>{f.$refs.blockInternetRef.openModal("users",[x]),r.value=[x],w.value="users"},["prevent"]),class:N([oe?"bg-main-color-600 text-white":"text-gray-900","group flex w-full items-center rounded-md px-2 py-2 text-sm"])}," Blokace internetu ",10,tc)]),_:2},1024)])):v("",!0),t(G).check("users.edit")&&x.active_directory==1?(s(),a("div",sc,[o(t(de),null,{default:i(({active:oe})=>[e("button",{onClick:D(re=>f.$refs.changeGroupRef.openModal([x]),["prevent"]),class:N([oe?"bg-main-color-600 text-white":"text-gray-900","group flex w-full items-center rounded-md px-2 py-2 text-sm"])}," Změnit skupinu ",10,oc)]),_:2},1024)])):v("",!0),t(G).check("users.edit")&&x.active_directory==1?(s(),a("div",ac,[o(t(de),null,{default:i(({active:oe})=>[e("button",{onClick:D(re=>f.$refs.changeOuRef.openModal([x]),["prevent"]),class:N([oe?"bg-main-color-600 text-white":"text-gray-900","group rounded-md px-2 pr-4 py-2 text-sm w-38"])},ic,10,lc)]),_:2},1024)])):v("",!0),t(G).check("users.delete")?(s(),a("div",rc,[o(t(de),null,{default:i(({active:oe})=>[e("button",{onClick:D(re=>f.$refs.deleteUsersRef.openModal([x]),["prevent"]),class:N([oe?"bg-main-color-600 text-white":"text-gray-900","group flex w-full items-center rounded-md px-2 py-2 text-sm"])}," Smazat uživatele ",10,dc)]),_:2},1024)])):v("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1024)])])],2)]))),128))])):(s(),a("tbody",cc,mc)),e("tfoot",pc,[e("tr",null,[e("td",vc,[e("div",_c,[hc,o(t(Oe),{as:"div",modelValue:n.value,"onUpdate:modelValue":b[12]||(b[12]=x=>n.value=x),class:"w-48"},{default:i(()=>[e("div",gc,[o(t(Se),{class:"relative cursor-pointer rounded-lg bg-white w-48 py-1.5 pl-3 pr-10 text-left text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6"},{default:i(()=>[n.value&&n.value.name?(s(),a("span",xc,p(n.value.name),1)):(s(),a("span",fc," vyberte akci... ")),e("span",bc,[o(t(ye),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]),_:1}),o(be,{"leave-active-class":"transition ease-in duration-100","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:i(()=>[o(t(ze),{class:"absolute bottom-11 z-10 max-h-60 w-48 overflow-auto rounded-md bg-white divide-y divide-gray-100 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:i(()=>[(s(),a(J,null,Q(g,x=>o(t(Pe),{as:"template",key:x.id,value:x},{default:i(({active:oe,selectedAction:re})=>[e("li",null,[t(G).check(x.permission)?(s(),a("span",{key:0,class:N([oe?"bg-indigo-600 text-white":"text-gray-900","relative cursor-pointer select-none p-2 m-1 rounded-md block"])},[e("span",{class:N([re?"font-semibold":"font-normal","block truncate"])},p(x.name),3)],2)):v("",!0)])]),_:2},1032,["value"])),64))]),_:1})]),_:1})])]),_:1},8,["modelValue"]),n.value?(s(),a("button",{key:0,onClick:b[13]||(b[13]=x=>E()),class:"rounded-lg bg-main-color-600 px-4 py-2 text-sm text-white shadow-sm hover:bg-main-color-700"}," Potvrdit ")):(s(),a("button",yc," Potvrdit "))])])])])])):(s(),L(ne,{key:1,class:"mx-auto text-spinner-color",size:"40"}))])])]),l.value!==null?(s(),L(ft,{key:0,meta:l.value,onSetPage:_e,modelValue:j.value,"onUpdate:modelValue":b[14]||(b[14]=x=>j.value=x)},null,8,["meta","modelValue"])):v("",!0)]),t(y)?(s(),a("div",kc,[e("p",null,p($.value.length),1),e("p",null,p(r.value.length),1),$.value==r.value?(s(),a("div",$c,"jjjj")):v("",!0),Z(" "+p($.value)+" ",1),wc,Uc,Z(" "+p(r.value)+" ",1),Cc,Vc])):v("",!0)])]),o(Ll,{ref_key:"resetPasswordRef",ref:k,onReloadUsersTable:b[15]||(b[15]=x=>d())},null,512),o(Qn,{ref_key:"deactivateUsersRef",ref:A,onReloadUsersTable:b[16]||(b[16]=x=>d())},null,512),o(ri,{ref_key:"enableUsersRef",ref:M,onReloadUsersTable:b[17]||(b[17]=x=>d())},null,512),o(Di,{ref_key:"importUsersRef",ref:H,onReloadUsersTable:b[18]||(b[18]=x=>d())},null,512),o(Xi,{ref_key:"promoteOuRef",ref:F,onReloadUsersTable:b[19]||(b[19]=x=>d())},null,512),o(Bn,{ref_key:"blockInternetRef",ref:B,onReloadUsersTable:b[20]||(b[20]=x=>d())},null,512),o(xa,{ref_key:"createUserRef",ref:P,onReloadUsersTable:b[21]||(b[21]=x=>d())},null,512),o(ka,{ref_key:"syncAdRef",ref:O,onReloadAd:b[22]||(b[22]=x=>f.reloadAd())},null,512),o(ur,{ref_key:"changeGroupRef",ref:K,onReloadUsersTable:b[23]||(b[23]=x=>d())},null,512),o(Sr,{ref_key:"changeOuRef",ref:C,onReloadUsersTable:b[24]||(b[24]=x=>d())},null,512),o(Gr,{ref_key:"deleteUsersRef",ref:z,onReloadUsersTable:b[25]||(b[25]=x=>d())},null,512)],64)}}};export{qc as default};
