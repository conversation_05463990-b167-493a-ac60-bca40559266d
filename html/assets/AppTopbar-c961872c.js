import{c as h}from"./index-f6faa48e.js";import{C as v,r as p,o as t,b as s,d as o,F as x,f,e as c,w as i,h as d,t as n,u as _,v as m,Q as g}from"./index-ad469968.js";const y={class:"px-0 z-10 top-0 sticky pb-6"},b={class:"bg-white border border-zinc-200/70 rounded-md p-5"},k={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-y-3"},w={key:0,class:"flex items-end"},C={key:0,class:"flex items-start"},N={key:1,class:"flex items-end"},B={class:"text-2xl pt-2"},D={key:1},S={class:"text-sm"},V={class:"flex flex-wrap gap-x-2 gap-y-2"},L={__name:"AppTopbar",props:{breadCrumbs:Array,customName:String,customDesc:String},setup(e){return v(e,()=>{calculateLinks()}),(a,z)=>{const l=p("router-link");return t(),s("div",y,[o("div",b,[o("div",k,[o("div",null,[e.breadCrumbs?(t(),s("div",w,[(t(!0),s(x,null,f(e.breadCrumbs,(r,u)=>(t(),s("div",null,[u==e.breadCrumbs.length-1?(t(),s("div",C,[c(l,{to:{name:r},class:"text-sm hover:underline"},{default:i(()=>[d(n(a.$router.resolve({name:r}).meta.title),1)]),_:2},1032,["to"])])):(t(),s("div",N,[c(l,{to:{name:r},class:"text-sm hover:underline"},{default:i(()=>[d(n(a.$router.resolve({name:r}).meta.title),1)]),_:2},1032,["to"]),c(_(h),{class:"h-5 text-gray-700 px-2"})]))]))),256))])):m("",!0),o("div",B,n(e.customName||a.$route.meta.title),1),e.customDesc?(t(),s("div",D,[o("p",S,n(e.customDesc),1)])):m("",!0)]),o("div",V,[g(a.$slots,"topbarButtons")])])])])}}};export{L as _};
