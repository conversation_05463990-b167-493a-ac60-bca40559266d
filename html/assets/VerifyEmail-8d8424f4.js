import{y as k,a as u,k as _,q as x,r as V,o as j,b as E,z as a,A as l,d as e,e as o,w as r,u as c,t as q,F as C,s as b,E as z,x as w,h as g}from"./index-3de9bee7.js";const F={class:"flex min-h-full flex-col justify-center py-12 sm:px-6 lg:px-8"},S=e("div",{class:"sm:mx-auto sm:w-full sm:max-w-md"},[e("img",{class:"mx-auto h-12 w-auto",src:"https://tailwindui.com/img/logos/mark.svg?color=main-color&shade=600",alt:"Your Company"}),e("h2",{class:"mt-6 text-center text-3xl font-bold tracking-tight text-gray-900"},"Ověření emailové adresy"),e("p",{class:"mt-2 text-center text-sm text-gray-600"}," Pro přístup do systému je nutné ověřit emailovou adresu. Zkontrolujte Vaši emailovou schránku (také složku spam) a email verifikujte. V případě, že Vám email k ověření nepřišel do pár minut od založení účtu, zažádejte si níže o nový. ")],-1),Z={class:"mt-8 sm:mx-auto sm:w-full sm:max-w-md"},B={class:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10"},N=e("label",{for:"email",class:"block text-sm font-medium leading-6 text-gray-900"},"Email",-1),P={class:"mt-2"},D=e("div",null,[e("button",{type:"submit",class:"flex w-full justify-center rounded-md bg-main-color-600 py-2 px-3 text-sm font-semibold text-white shadow-sm hover:bg-main-color-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-main-color-600"},"Zaslat nový ověřovací email")],-1),K={class:"mt-4 text-center text-sm text-gray-600"},O=e("span",{"aria-hidden":"true"},"→",-1),T={class:"flex min-h-full flex-col justify-center py-12 sm:px-6 lg:px-8"},U=e("div",{class:"sm:mx-auto sm:w-full sm:max-w-md"},[e("img",{class:"mx-auto h-12 w-auto",src:"https://tailwindui.com/img/logos/mark.svg?color=main-color&shade=600",alt:"Your Company"}),e("h2",{class:"mt-6 text-center text-3xl font-bold tracking-tight text-gray-900"},"Ověřování emailové adresy")],-1),Y={class:"mt-8 sm:mx-auto sm:w-full sm:max-w-md"},$={class:"mt-2 text-center text-sm text-gray-600"},A={class:"mt-8 text-center text-sm text-gray-600"},M={class:"text-left mt-8 sm:mx-auto sm:w-full sm:max-w-md"},R={class:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10"},G=e("label",{for:"email",class:"block text-sm font-medium leading-6 text-gray-900"},"Email",-1),H={class:"mt-2"},I=e("div",null,[e("button",{type:"submit",class:"flex w-full justify-center rounded-md bg-main-color-600 py-2 px-3 text-sm font-semibold text-white shadow-sm hover:bg-main-color-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-main-color-600"},"Zaslat nový ověřovací email")],-1),J={class:"mt-4 text-center text-sm text-gray-600"},L=e("span",{"aria-hidden":"true"},"→",-1),X={__name:"VerifyEmail",setup(Q){const h=k(),i=u(""),d=u(!1),m=u(!1),v=u(!1),p=u("");h.query.token?(m.value=!0,d.value=!0,_.get("/api/auth/verify-email?token="+h.query.token).then(t=>{p.value="Emailová adresa byla ověřena, nyní se můžete přihlásit do systému.",v.value=!0,x.success(t.data.message)}).catch(t=>{p.value="Emailovou adresu se nepodařilo ověřit. Zkuste tuto akci opakovat zasláním nového ověřovacího emailu. Chyba: "+t.response.data.message,v.value=!1,x.error("Email se nepodařilo ověřit")}),m.value=!1):d.value=!1;function y(){_.post("/api/auth/verify-email",{email:i.value}).then(t=>{x.success(t.data.message)}).catch(t=>{x.error(t.response.data.message)})}return(t,s)=>{const f=V("router-link");return j(),E(C,null,[a(e("div",F,[S,e("div",Z,[e("div",B,[o(c(w),{onSubmit:s[1]||(s[1]=n=>y()),class:"space-y-6",action:"#"},{default:r(()=>[e("div",null,[N,e("div",P,[o(c(b),{rules:"required|email",modelValue:i.value,"onUpdate:modelValue":s[0]||(s[0]=n=>i.value=n),id:"email",name:"email",type:"email",autocomplete:"email",required:"",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},null,8,["modelValue"]),o(c(z),{name:"email",class:"text-rose-400 text-sm block pt-1"})])]),D]),_:1})])]),e("p",K,[o(f,{to:{name:"contact-support"},class:"text-sm font-semibold text-gray-900"},{default:r(()=>[g(" Kontaktovat podporu "),O]),_:1})])],512),[[l,!d.value]]),a(e("div",T,[U,e("div",Y,[a(e("p",$," Prosíme vyčkejte, právě oveřujeme Vaši emailovou adresu. ",512),[[l,m.value]]),a(e("p",{class:"mt-2 text-center text-sm text-gray-600"},q(p.value),513),[[l,!m.value]])]),a(e("p",A,[a(o(f,{to:{name:"login"},class:"font-medium text-main-color-600 hover:text-main-color-500"},{default:r(()=>[g(" Přihlásit se ")]),_:1},512),[[l,v.value]]),a(e("div",M,[e("div",R,[o(c(w),{onSubmit:s[3]||(s[3]=n=>y()),class:"space-y-6"},{default:r(()=>[e("div",null,[G,e("div",H,[o(c(b),{rules:"required|email",modelValue:i.value,"onUpdate:modelValue":s[2]||(s[2]=n=>i.value=n),id:"email",name:"email",type:"email",autocomplete:"email",required:"",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},null,8,["modelValue"])])]),I]),_:1})])],512),[[l,!v.value]])],512),[[l,!m.value]]),e("p",J,[o(f,{to:{name:"contact-support"},class:"text-sm font-semibold text-gray-900"},{default:r(()=>[g(" Kontaktovat podporu "),L]),_:1})])],512),[[l,d.value]])],64)}}};export{X as default};
