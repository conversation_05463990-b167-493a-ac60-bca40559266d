import{a as i,j as S,o as d,c as j,w as p,e as n,h as Z,d as t,u as s,y as A,v as z,E as I,G as _,k as R,s as U,D as F,b as c,x as V,M as T,C as X,r as L,A as Y,a2 as H,Y as J,F as K,f as Q,t as P}from"./index-c2402147.js";import{_ as W}from"./AppTopbar-cd1b5f57.js";import{o as ee}from"./index-a54879fc.js";import{X as N}from"./index-945c46cd.js";import{_ as te}from"./pagination-0d8b11de.js";import{c as x}from"./checkPermission.service-94919896.js";import{_ as q}from"./basicModal-cef5a3ee.js";import{S as B}from"./transition-b398339f.js";import"./listbox-9ed050a9.js";import"./hidden-c900ed71.js";import"./use-tracked-pointer-cac67442.js";import"./use-resolve-button-type-a285cf13.js";import"./use-controllable-42a02934.js";import"./dialog-b3ac1cb1.js";const oe={class:"p-6 gap-4 space-y-4"},se={class:"mt-2"},ae={class:"mt-2"},ne={class:"border-t p-5"},le={class:"text-right space-x-3"},re={__name:"createCategoryModal",emits:["reloadItems"],setup(M,{expose:w,emit:C}){const b=C,v=i(!1);S("debugModeGlobalVar");const l=i(""),a=i(""),h=i(!1);function m(){v.value=!0,l.value="",a.value=""}function k(){v.value=!1}async function $(){x.check("buildings.create")||x.check("property.master")?await R.post("api/accounting-categories",{name:l.value,code:a.value}).then(r=>{U.success(r.data.message)}).catch(r=>{console.log(r)}):h.value=!1,k(),b("reloadItems",!0)}return w({openModal:m}),(r,e)=>(d(),j(s(B),{appear:"",show:v.value,as:"template",onClose:e[5]||(e[5]=g=>k())},{default:p(()=>[n(q,{size:"xs"},{"modal-title":p(()=>e[6]||(e[6]=[Z("Založení nového druhu majetku")])),"modal-close-button":p(()=>[t("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:e[0]||(e[0]=g=>k())},[n(s(N),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":p(()=>[n(s(A),{onSubmit:e[4]||(e[4]=g=>$())},{default:p(({values:g})=>[t("div",oe,[t("div",null,[e[7]||(e[7]=t("label",{for:"category-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Název:",-1)),t("div",se,[n(s(z),{rules:"required",modelValue:l.value,"onUpdate:modelValue":e[1]||(e[1]=f=>l.value=f),type:"text",name:"category-name",id:"category-name",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte název druhu majetku..."},null,8,["modelValue"]),n(s(I),{name:"category-name",class:"text-rose-400 text-sm block pt-1"})])]),t("div",null,[e[8]||(e[8]=t("label",{for:"item-code",class:"block text-sm font-normal leading-6 text-gray-900"},"Kód (nepovinné):",-1)),t("div",ae,[n(s(z),{modelValue:a.value,"onUpdate:modelValue":e[2]||(e[2]=f=>a.value=f),type:"text",name:"item-code",id:"item-code",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte kód druhu majetku..."},null,8,["modelValue"]),n(s(I),{name:"item-code",class:"text-rose-400 text-sm block pt-1"})])])]),t("div",ne,[t("div",le,[t("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:e[3]||(e[3]=_(f=>k(),["prevent"]))}," Zavřít "),e[9]||(e[9]=t("button",{type:"submit",class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600"}," Vytvořit ",-1))])])]),_:1})]),_:1})]),_:1},8,["show"]))}},de={key:0,class:"p-6 space-y-4"},ie={class:"mt-2"},ue={class:"mt-2"},me={class:"border-t p-5"},ce={class:"text-right space-x-3"},pe={__name:"editCategoryModal",props:{selectedCategory:{type:Object,required:!0}},emits:["reloadItems"],setup(M,{expose:w,emit:C}){const b=C,v=M,l=i(!1);S("debugModeGlobalVar");const a=i(""),h=i(!1);F(()=>v.selectedCategory,(r,e)=>{a.value={},a.value=r});function m(){l.value=!1}async function k(){l.value=!0}async function $(){x.check("buildings.edit")||x.check("property.master")?await R.post("api/accounting-categories/"+v.selectedCategory.id+"/update",{name:a.value.name,code:a.value.code}).then(r=>{U.success(r.data.message)}).catch(r=>{console.log(r)}):h.value=!1,m(),b("reloadItems",!0)}return w({openModal:k}),(r,e)=>(d(),j(s(B),{appear:"",show:l.value,as:"template",onClose:e[5]||(e[5]=g=>m())},{default:p(()=>[n(q,{size:"sm"},{"modal-title":p(()=>e[6]||(e[6]=[Z("Úprava druhu majetku")])),"modal-close-button":p(()=>[t("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:e[0]||(e[0]=g=>m())},[n(s(N),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":p(()=>[n(s(A),{onSubmit:e[4]||(e[4]=g=>$())},{default:p(({values:g})=>[a.value?(d(),c("div",de,[t("div",null,[e[7]||(e[7]=t("label",{for:"category-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Název:",-1)),t("div",ie,[n(s(z),{rules:"required",modelValue:a.value.name,"onUpdate:modelValue":e[1]||(e[1]=f=>a.value.name=f),type:"text",name:"category-name",id:"category-name",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte název budovy..."},null,8,["modelValue"]),n(s(I),{name:"category-name",class:"text-rose-400 text-sm block pt-1"})])]),t("div",null,[e[8]||(e[8]=t("label",{for:"category-code",class:"block text-sm font-normal leading-6 text-gray-900"},"Kód (nepovinné):",-1)),t("div",ue,[n(s(z),{modelValue:a.value.code,"onUpdate:modelValue":e[2]||(e[2]=f=>a.value.code=f),type:"text",name:"category-code",id:"category-code",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte kód budovy..."},null,8,["modelValue"]),n(s(I),{name:"category-code",class:"text-rose-400 text-sm block pt-1"})])])])):V("",!0),t("div",me,[t("div",ce,[t("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:e[3]||(e[3]=_(f=>m(),["prevent"]))}," Zavřít "),e[9]||(e[9]=t("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Upravit ",-1))])])]),_:1})]),_:1})]),_:1},8,["show"]))}},ge={key:0,class:"p-6"},ve={class:"border-t p-5"},ye={class:"text-right space-x-3"},be={__name:"deleteCategoryModal",props:{selectedCategory:{type:Object,required:!0}},emits:["reloadItems"],setup(M,{expose:w,emit:C}){const b=C,v=M,l=i(!1);S("debugModeGlobalVar");const a=i(""),h=i(!1);F(()=>v.selectedCategory,(r,e)=>{a.value=r});function m(){l.value=!1}async function k(){l.value=!0}async function $(){x.check("buildings.delete")||x.check("property.master")?await R.post("api/accounting-categories/"+a.value.id+"/delete").then(r=>{U.success(r.data.message)}).catch(r=>{console.log(r)}):h.value=!1,m(),b("reloadItems",!0)}return w({openModal:k}),(r,e)=>(d(),j(s(B),{appear:"",show:l.value,as:"template",onClose:e[3]||(e[3]=g=>m())},{default:p(()=>[n(q,{size:"sm"},{"modal-title":p(()=>e[4]||(e[4]=[Z("Smazat druh majetku")])),"modal-close-button":p(()=>[t("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:e[0]||(e[0]=g=>m())},[n(s(N),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":p(()=>[a.value?(d(),c("div",ge,e[5]||(e[5]=[t("span",null,"Opravdu si přejete druh majetku smazat?",-1)]))):V("",!0),t("div",ve,[t("div",ye,[t("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:e[1]||(e[1]=_(g=>m(),["prevent"]))}," Zavřít "),t("button",{onClick:e[2]||(e[2]=_(g=>$(),["prevent"])),class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-red-600",type:"submit"}," Smazat ")])])]),_:1})]),_:1},8,["show"]))}},xe={class:"space-y-6"},fe={class:"px-0 hidden"},he={class:"bg-white border border-zinc-200/70 rounded-md p-5"},ke={class:"sm:flex justify-between items-center gap-4"},we={class:"w-80"},Ce={class:"flex items-center gap-4"},$e={class:"flow-root bg-white border border-zinc-200/70 rounded-md"},_e={class:"sm:-mx-6 lg:-mx-8"},Ve={class:"inline-block min-w-full align-middle sm:px-6 lg:px-8"},Me={key:0,class:"min-w-full divide-y divide-gray-200"},je={key:0,class:"divide-y divide-gray-200"},ze={class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600"},Ie={key:0},Se={key:1},Re={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},Ne={key:0},Ze={key:1},Ue={class:"text-end pr-5 w-40"},Fe=["onClick"],qe=["onClick"],Be={key:1},We={__name:"accountingCategories",setup(M){const w=T();S("debugModeGlobalVar");const C=i(["accounting-categories"]),b=i(!1),v=i(""),l=i(1),a=i({}),h=i({}),m=i({}),k=i(""),$=i(""),r=i("");X(()=>{e()}),F(()=>w.perPage,(y,o)=>{b.value=!0,l.value=1,e()});async function e(){b.value=!0,await R.get("/api/accounting-categories?page="+l.value+"&search="+v.value).then(y=>{h.value=y.data.data,a.value=y.data.meta}).catch(y=>{console.log(y)}),b.value=!1}function g(y){m.value=y}function f(y){l.value=y,e()}function D(){b.value=!0,l.value=1,v.value="",e()}function G(){b.value=!0,l.value=1,e()}return(y,o)=>{const E=L("VueSpinner");return d(),c(K,null,[n(W,{breadCrumbs:C.value},{topbarButtons:p(()=>[s(x).check("buildings.create")||s(x).check("property.master")?(d(),c("button",{key:0,onClick:o[0]||(o[0]=_(u=>y.$refs.createCategoryRef.openModal(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},"Nový druh majetku ")):V("",!0)]),_:1},8,["breadCrumbs"]),t("div",xe,[t("div",fe,[t("div",he,[t("div",ke,[t("div",we,[Y(t("input",{type:"text",name:"search",id:"search","onUpdate:modelValue":o[1]||(o[1]=u=>v.value=u),onKeyup:o[2]||(o[2]=J(u=>G(),["enter"])),class:"block w-full rounded-md py-1.5 text-gray-900 border border-gray-300 placeholder:text-gray-400 focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte hledaný výraz..."},null,544),[[H,v.value]])]),t("div",Ce,[t("button",{onClick:o[3]||(o[3]=u=>D()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},o[9]||(o[9]=[t("span",null,"Resetovat",-1)])),t("button",{onClick:o[4]||(o[4]=u=>G()),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700"}," Filtrovat ")])])])]),t("div",null,[t("div",$e,[t("div",_e,[t("div",Ve,[b.value==!1?(d(),c("table",Me,[o[11]||(o[11]=t("thead",null,[t("tr",null,[t("th",{scope:"col",class:"pl-5 pr-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Kód druhu majetku "),t("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Název druhu majetku "),t("th",{scope:"col",class:"pl-5 pr-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"})])],-1)),h.value&&h.value.length?(d(),c("tbody",je,[(d(!0),c(K,null,Q(h.value,u=>(d(),c("tr",{key:u.id},[t("td",ze,[u.code?(d(),c("span",Ie,P(u.code),1)):(d(),c("span",Se,"-"))]),t("td",Re,[u.name?(d(),c("span",Ne,P(u.name),1)):(d(),c("span",Ze,"-"))]),t("td",Ue,[s(x).check("buildings.edit")||s(x).check("property.master")?(d(),c("button",{key:0,onClick:_(O=>(g(u),y.$refs.editCategoryRef.openModal()),["prevent"]),class:"mr-2"},[n(s(ee),{class:"h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 p-2 rounded-lg","aria-hidden":"true"})],8,Fe)):V("",!0),s(x).check("buildings.delete")||s(x).check("property.master")?(d(),c("button",{key:1,onClick:_(O=>(g(u),y.$refs.deleteCategoryRef.openModal()),["prevent"])},[n(s(N),{class:"h-8 w-8 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-1.5 rounded-lg","aria-hidden":"true"})],8,qe)):V("",!0)])]))),128))])):(d(),c("tbody",Be,o[10]||(o[10]=[t("tr",null,[t("td",{colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},"Nebyly nalezeny žádné budovy.")],-1)])))])):(d(),j(E,{key:1,class:"mx-auto text-spinner-color",size:"40"}))])])]),a.value!==null?(d(),j(te,{key:0,meta:a.value,onSetPage:f,modelValue:l.value,"onUpdate:modelValue":o[5]||(o[5]=u=>l.value=u)},null,8,["meta","modelValue"])):V("",!0)])]),n(re,{ref_key:"createCategoryRef",ref:k,size:"sm",onReloadItems:o[6]||(o[6]=u=>e())},null,512),n(pe,{ref_key:"editCategoryRef",ref:$,size:"sm",selectedCategory:m.value,onReloadItems:o[7]||(o[7]=u=>e())},null,8,["selectedCategory"]),n(be,{ref_key:"deleteCategoryRef",ref:r,size:"sm",selectedCategory:m.value,onReloadItems:o[8]||(o[8]=u=>e())},null,8,["selectedCategory"])],64)}}};export{We as default};
