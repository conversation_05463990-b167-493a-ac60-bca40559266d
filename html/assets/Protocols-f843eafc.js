import{a as c,j as $e,J as oe,D as q,r as se,o as a,c as N,w as d,e as l,h as Ce,d as e,u as s,b as o,t as R,T as xe,f as D,n as w,F as I,x as v,G as P,k as Z,s as Oe,C as Ve,M as ze}from"./index-c2402147.js";import{_ as Ee}from"./AppTopbar-cd1b5f57.js";import{X as ge,i as Re,g as Me,A as Se,x as Te,O as Fe,_ as Le,Z as Ie,$ as je,W as De,o as Ne,a0 as Ze}from"./index-a54879fc.js";import{X as ye,d as he,e as ne,C as Ue}from"./index-945c46cd.js";import{_ as Ae}from"./pagination-0d8b11de.js";import{c as S}from"./checkPermission.service-94919896.js";import{d as ie}from"./debounce-51dd45ad.js";import{_ as Pe}from"./basicModal-cef5a3ee.js";import{g as be,S as _e,b as we,M as ke}from"./menu-0cb5aeca.js";import{N as re,$ as de,K as ue,U as ce,_ as ve}from"./combobox-b7d810b4.js";import{S as ee}from"./transition-b398339f.js";import"./listbox-9ed050a9.js";import"./hidden-c900ed71.js";import"./use-tracked-pointer-cac67442.js";import"./use-resolve-button-type-a285cf13.js";import"./use-controllable-42a02934.js";import"./dialog-b3ac1cb1.js";import"./use-tree-walker-958c2fbd.js";const Ge={class:"p-6"},Be={class:"w-full text-left"},Qe={key:0,class:"text-gray-900"},Xe={key:1,class:"text-gray-400"},qe=["onClick"],Ke={key:0,class:"mt-4"},Je={key:0,class:"w-full"},We={key:0},He={class:"relative mt-1"},Ye={class:"relative w-full cursor-default overflow-hidden rounded-md bg-white border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 text-sm"},et={key:0},tt={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},lt={key:1,class:"h-20 max-h-20 overflow-hidden"},at={key:2},st={key:1},ot={class:"relative mt-1"},nt={class:"relative w-full cursor-default overflow-hidden rounded-md bg-white border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 text-sm"},it={key:0},rt={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},dt={key:1,class:"h-20 max-h-20 overflow-hidden"},ut={key:2},ct={class:"border-t p-5"},vt={class:"text-right space-x-3"},mt={__name:"createProtocolModal",emits:["reloadProtocols"],setup(me,{expose:J,emit:W}){const H=W,F=c(!1);$e("debugModeGlobalVar");const j=c([{id:"TO_USER",name:"Přiřadit uživateli"},{id:"FROM_USER",name:"Odebrat uživateli"},{id:"TO_ROOM",name:"Přiřadit místnosti"},{id:"FROM_ROOM",name:"Odebrat místnosti"}]),y=c({id:""}),m=c(null),x=c(null),k=c(!1),C=c([]),p=c([]),h=c(""),g=c(""),_=c(!1),U=c(!1);function A(u){y.value=u,console.log(u),console.log(m.value),u.id=="TO_USER"&&m.value&&m.value.deleted_at&&(m.value=null)}const V=oe(()=>h.value===""?C.value:C.value.filter(u=>`${u.first_name} ${u.last_name}`.toLowerCase().replace(/\s+/g,"").includes(h.value.toLowerCase().replace(/\s+/g,"")))),z=oe(()=>g.value===""?p.value:p.value.filter(u=>u.name.toLowerCase().replace(/\s+/g,"").includes(g.value.toLowerCase().replace(/\s+/g,"")))),G=ie(async()=>{try{let u="/api/users?page=1&perpage=50&search="+h.value;y.value.id==="FROM_USER"&&(u+="&with_deleted=1");const i=await Z.get(u);C.value=i.data.data}catch(u){console.error(u)}finally{_.value=!1}},300),B=ie(async()=>{try{const u=await Z.get("/api/rooms?page=1&perpage=50&search="+g.value);p.value=u.data.data}catch(u){console.error(u)}finally{U.value=!1}},300);q(h,()=>{_.value=!0,G()}),q(g,()=>{U.value=!0,B()});function L(){F.value=!1}async function te(){await G(),await B(),F.value=!0,m.value=null,x.value=null,y.value={id:""}}async function le(){if(S.check("moves.create")||S.check("property.master")){const u={type:y.value.id};["TO_USER","FROM_USER"].includes(y.value.id)&&(u.user_id=m.value.id),["TO_ROOM","FROM_ROOM"].includes(y.value.id)&&(u.room_id=x.value.id),await Z.post("api/moves",u).then(i=>{Oe.success(i.data.message)}).catch(i=>{console.log(i)})}else k.value=!1;L(),H("reloadProtocols",!0)}return J({openModal:te}),(u,i)=>{const E=se("VueSpinner");return a(),N(s(ee),{appear:"",show:F.value,as:"template",onClose:i[9]||(i[9]=Q=>L())},{default:d(()=>[l(Pe,{size:"sm"},{"modal-title":d(()=>i[10]||(i[10]=[Ce("Vytvoření nového protokolu")])),"modal-close-button":d(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:i[0]||(i[0]=Q=>L())},[l(s(ye),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":d(()=>{var Q;return[e("div",null,[e("div",Ge,[e("div",null,[l(s(be),{as:"div",class:"relative block text-left"},{default:d(()=>[e("div",null,[i[11]||(i[11]=e("label",{class:"block text-sm font-normal leading-6 text-gray-900 pb-1"},"Typ protokolu:",-1)),l(s(_e),{class:"inline-flex w-full justify-center rounded-md bg-white px-4 py-2 text-sm font-medium text-white border border-gray-300"},{default:d(()=>[e("div",Be,[y.value&&y.value.name?(a(),o("span",Qe,R(y.value.name),1)):(a(),o("span",Xe,"Zvolte typ protokolu..."))]),l(s(he),{class:"ml-2 -mr-1 h-5 w-5 text-violet-200 hover:text-violet-100","aria-hidden":"true"})]),_:1})]),l(xe,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:d(()=>[l(s(we),{class:"absolute z-10 right-0 mt-2 w-full origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:d(()=>[(a(!0),o(I,null,D(j.value,t=>(a(),o("div",{key:t.id,class:"px-1 py-1"},[l(s(ke),null,{default:d(({active:b})=>[e("button",{onClick:M=>A(t),class:w([b?"bg-main-color-600 text-white":"text-gray-900","group flex w-full rounded-md px-2 py-2 text-sm text-left"])},R(t.name),11,qe)]),_:2},1024)]))),128))]),_:1})]),_:1})]),_:1})]),(Q=y.value)!=null&&Q.id?(a(),o("div",Ke,[y.value.id=="FROM_USER"||y.value.id=="TO_USER"?(a(),o("div",Je,[C.value?(a(),o("div",We,[i[12]||(i[12]=e("label",{class:"block text-sm font-normal leading-6 text-gray-900"},"Přiřazený uživatel:",-1)),l(s(re),{modelValue:m.value,"onUpdate:modelValue":i[3]||(i[3]=t=>m.value=t)},{default:d(()=>[e("div",He,[e("div",Ye,[l(s(de),{class:"w-full border-none py-2 pl-4 pr-10 text-sm leading-5 text-gray-900 focus:ring-0 font-medium",placeholder:"Zvolte uživatele",displayValue:t=>t?`${t.first_name||""} ${t.middle_name?t.middle_name+" ":""}${t.last_name||""}`.trim():"",onChange:i[1]||(i[1]=t=>h.value=t.target.value)},null,8,["displayValue"]),l(s(ue),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:d(()=>[l(s(ne),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),l(s(ee),{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:i[2]||(i[2]=t=>h.value="")},{default:d(()=>[l(s(ce),{class:"absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},{default:d(()=>[_.value?v("",!0):(a(),o("div",et,[V.value.length===0&&h.value!==""?(a(),o("div",tt," Žádný uživatel nenalezen. ")):v("",!0)])),_.value?(a(),o("div",lt,[l(E,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(a(),o("div",at,[(a(!0),o(I,null,D(V.value,t=>(a(),N(s(ve),{as:"template",key:t.id,value:t},{default:d(({active:b})=>{var M,T,$;return[e("li",{class:w(["relative cursor-default select-none py-2 pl-4 pr-10",{"bg-main-color-600 text-white":b,"text-gray-900":!b}])},[e("span",{class:w(["block truncate",{"font-medium":((M=m.value)==null?void 0:M.id)==t.id,"font-normal":((T=m.value)==null?void 0:T.id)!=t.id}])},R((t==null?void 0:t.first_name)+" "+(t!=null&&t.middle_name?(t==null?void 0:t.middle_name)+" ":"")+(t==null?void 0:t.last_name)),3),(($=m.value)==null?void 0:$.id)==t.id?(a(),o("span",{key:0,class:w(["absolute inset-y-0 right-0 flex items-center pr-3",{"text-white":b,"text-main-color-600":!b}])},[l(s(Ue),{class:"h-5 w-5","aria-hidden":"true"})],2)):v("",!0)],2)]}),_:2},1032,["value"]))),128))]))]),_:1})]),_:1})])]),_:1},8,["modelValue"])])):v("",!0)])):(a(),o("div",st,[i[13]||(i[13]=e("label",{class:"block text-sm font-normal leading-6 text-gray-900"},"Přiřazená místnost:",-1)),l(s(re),{modelValue:x.value,"onUpdate:modelValue":i[6]||(i[6]=t=>x.value=t)},{default:d(()=>[e("div",ot,[e("div",nt,[l(s(de),{class:"w-full border-none py-2 pl-4 pr-10 text-sm leading-5 text-gray-900 focus:ring-0 font-medium",placeholder:"Zvolte místnost",displayValue:t=>t?`${t.name}`.trim():"",onChange:i[4]||(i[4]=t=>g.value=t.target.value)},null,8,["displayValue"]),l(s(ue),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:d(()=>[l(s(ne),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),l(s(ee),{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:i[5]||(i[5]=t=>g.value="")},{default:d(()=>[l(s(ce),{class:"absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},{default:d(()=>[U.value?v("",!0):(a(),o("div",it,[z.value.length===0&&g.value!==""?(a(),o("div",rt," Žádná místnost nenalezena. ")):v("",!0)])),U.value?(a(),o("div",dt,[l(E,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(a(),o("div",ut,[(a(!0),o(I,null,D(z.value,t=>(a(),N(s(ve),{as:"template",key:t.id,value:t},{default:d(({active:b})=>{var M,T,$;return[e("li",{class:w(["relative cursor-default select-none py-2 pl-4 pr-10",{"bg-main-color-600 text-white":b,"text-gray-900":!b}])},[e("span",{class:w(["block truncate",{"font-medium":((M=x.value)==null?void 0:M.id)==t.id,"font-normal":((T=x.value)==null?void 0:T.id)!=t.id}])},R(t==null?void 0:t.name),3),(($=x.value)==null?void 0:$.id)==t.id?(a(),o("span",{key:0,class:w(["absolute inset-y-0 right-0 flex items-center pr-3",{"text-white":b,"text-main-color-600":!b}])},[l(s(Ue),{class:"h-5 w-5","aria-hidden":"true"})],2)):v("",!0)],2)]}),_:2},1032,["value"]))),128))]))]),_:1})]),_:1})])]),_:1},8,["modelValue"])]))])):v("",!0)]),e("div",ct,[e("div",vt,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:i[7]||(i[7]=P(t=>L(),["prevent"]))}," Zavřít "),e("button",{onClick:i[8]||(i[8]=P(t=>le(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Přidat ")])])])]}),_:1})]),_:1},8,["show"])}}},pt={class:"p-6 grid grid-cols-1 gap-4"},ft={class:"w-full text-left"},gt={key:0,class:"text-gray-900"},yt={key:1,class:"text-gray-400"},xt=["onClick"],ht={key:0},bt={class:"relative"},_t={class:"relative w-full cursor-default overflow-hidden rounded-md bg-white border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 text-sm"},wt={key:0,class:"absolute top-1.5 right-8 w-6 h-6"},kt={key:0},$t={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},Rt={key:1,class:"h-20 max-h-20 overflow-hidden"},Ct={key:2},Ot={key:1},Pt={class:"relative"},Mt={class:"relative w-full cursor-default overflow-hidden rounded-md bg-white border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 text-sm"},St={key:0,class:"absolute top-1.5 right-8 w-6 h-6"},Ut={key:0},Vt={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},zt={key:1,class:"h-20 max-h-20 overflow-hidden"},Et={key:2},Tt={class:"border-t p-5"},Ft={class:"text-right space-x-3"},Lt={__name:"editProtocolModal",props:{selectedProtocol:{type:Object,required:!0}},emits:["reloadGroups"],setup(me,{expose:J,emit:W}){const H=W,F=me,j=c(!1);$e("debugModeGlobalVar");const y=c([{id:"TO_USER",name:"Přiřadit uživateli"},{id:"FROM_USER",name:"Odebrat uživateli"},{id:"TO_ROOM",name:"Přiřadit místnosti"},{id:"FROM_ROOM",name:"Odebrat místnosti"}]),m=c({id:""}),x=c({id:""}),k=c({id:""}),C=c(!1),p=c({}),h=c({});Ve(()=>{G(),B()}),q(()=>F.selectedProtocol,(u,i)=>{u.type&&(m.value=y.value.find(E=>E.id===u.type)),u.user&&(x.value=u.user,k.value=""),u.room&&(k.value=u.room,x.value="")});const g=c(""),_=c(""),U=c(!1),A=c(!1),V=oe(()=>g.value===""?p.value:p.value.filter(u=>`${u.first_name} ${u.last_name}`.toLowerCase().replace(/\s+/g,"").includes(g.value.toLowerCase().replace(/\s+/g,"")))),z=oe(()=>_.value===""?h.value:h.value.filter(u=>u.name.toLowerCase().replace(/\s+/g,"").includes(_.value.toLowerCase().replace(/\s+/g,"")))),G=ie(async()=>{try{const u=await Z.get("/api/users?page=1&perpage=50&search="+g.value);p.value=u.data.data}catch(u){console.error(u)}finally{U.value=!1}},300),B=ie(async()=>{try{const u=await Z.get("/api/rooms?page=1&perpage=50&search="+_.value);h.value=u.data.data}catch(u){console.error(u)}finally{A.value=!1}},300);q(g,()=>{U.value=!0,G()}),q(_,()=>{A.value=!0,B()});function L(){j.value=!1}async function te(){j.value=!0}async function le(){if(S.check("moves.edit")||S.check("property.master")){var u,i;m.value.id=="TO_USER"||m.value.id=="FROM_USER"?(i=x.value.id,u=null):(i=null,u=k.value.id),await Z.post("api/moves/"+F.selectedProtocol.id+"/update",{user_id:i,room_id:u,type:m.value.id}).then(E=>{Oe.success(E.data.message)}).catch(E=>{console.log(E)})}else C.value=!1;L(),H("reloadProtocols",!0)}return J({openModal:te}),(u,i)=>{const E=se("VueSpinner"),Q=se("CheckIcon");return a(),N(s(ee),{appear:"",show:j.value,as:"template",onClose:i[11]||(i[11]=t=>L())},{default:d(()=>[l(Pe,{size:"sm"},{"modal-title":d(()=>i[12]||(i[12]=[Ce("Editace protokolu")])),"modal-close-button":d(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:i[0]||(i[0]=t=>L())},[l(s(ye),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":d(()=>[e("div",null,[e("div",pt,[e("div",null,[i[13]||(i[13]=e("label",{class:"block text-sm font-normal leading-6 text-gray-900 pb-1"},"Typ protokolu:",-1)),l(s(be),{as:"div",class:"relative block text-left"},{default:d(()=>[e("div",null,[l(s(_e),{class:"inline-flex w-full justify-center rounded-md bg-white px-4 py-2 text-sm font-medium text-white border border-gray-300"},{default:d(()=>[e("div",ft,[m.value&&m.value.name?(a(),o("span",gt,R(m.value.name),1)):(a(),o("span",yt,"Zvolte typ protokolu..."))]),l(s(he),{class:"ml-2 -mr-1 h-5 w-5 text-violet-200 hover:text-violet-100","aria-hidden":"true"})]),_:1})]),l(xe,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:d(()=>[l(s(we),{class:"absolute z-10 right-0 mt-2 w-full origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:d(()=>[(a(!0),o(I,null,D(y.value,t=>(a(),o("div",{key:t.id,class:"px-1 py-1"},[l(s(ke),null,{default:d(({active:b})=>[e("button",{onClick:M=>m.value=t,class:w([b?"bg-main-color-600 text-white":"text-gray-900","group flex w-full rounded-md px-2 py-2 text-sm text-left"])},R(t.name),11,xt)]),_:2},1024)]))),128))]),_:1})]),_:1})]),_:1})]),m.value.id=="TO_USER"||m.value.id=="FROM_USER"?(a(),o("div",ht,[i[14]||(i[14]=e("label",{class:"block text-sm font-normal leading-6 text-gray-900 pb-1"},"Přiřazený uživatel:",-1)),l(s(re),{modelValue:x.value,"onUpdate:modelValue":i[4]||(i[4]=t=>x.value=t)},{default:d(()=>[e("div",bt,[e("div",_t,[l(s(de),{class:"w-full border-none py-2 pl-4 pr-10 text-sm leading-5 text-gray-900 placeholder:text-gray-400 focus:ring-0 font-medium",placeholder:"Zvolte uživatele...",displayValue:t=>t?`${t.first_name||""} ${t.middle_name?t.middle_name+" ":""}${t.last_name||""}`.trim():"",onChange:i[1]||(i[1]=t=>g.value=t.target.value)},null,8,["displayValue"]),x.value&&x.value.first_name?(a(),o("div",wt,[e("button",{onClick:i[2]||(i[2]=P(t=>x.value=null,["prevent"])),type:"button"},[l(s(ye),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):v("",!0),l(s(ue),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:d(()=>[l(s(ne),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),l(s(ee),{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:i[3]||(i[3]=t=>g.value="")},{default:d(()=>[l(s(ce),{class:"absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},{default:d(()=>[U.value?v("",!0):(a(),o("div",kt,[V.value.length===0&&g.value!==""?(a(),o("div",$t," Žádný uživatel nenalezen. ")):v("",!0)])),U.value?(a(),o("div",Rt,[l(E,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(a(),o("div",Ct,[(a(!0),o(I,null,D(V.value,t=>(a(),N(s(ve),{as:"template",key:t.id,value:t},{default:d(({active:b})=>{var M,T,$;return[e("li",{class:w(["relative cursor-default select-none py-2 pl-4 pr-10",{"bg-main-color-600 text-white":b,"text-gray-900":!b}])},[e("span",{class:w(["block truncate",{"font-medium":((M=x.value)==null?void 0:M.id)==t.id,"font-normal":((T=x.value)==null?void 0:T.id)!=t.id}])},R((t==null?void 0:t.first_name)+" "+(t!=null&&t.middle_name?(t==null?void 0:t.middle_name)+" ":"")+(t==null?void 0:t.last_name)),3),(($=x.value)==null?void 0:$.id)==t.id?(a(),o("span",{key:0,class:w(["absolute inset-y-0 right-0 flex items-center pr-3",{"text-white":b,"text-main-color-600":!b}])},[l(Q,{class:"h-5 w-5","aria-hidden":"true"})],2)):v("",!0)],2)]}),_:2},1032,["value"]))),128))]))]),_:1})]),_:1})])]),_:1},8,["modelValue"])])):v("",!0),m.value.id=="TO_ROOM"||m.value.id=="FROM_ROOM"?(a(),o("div",Ot,[i[15]||(i[15]=e("label",{class:"block text-sm font-normal leading-6 text-gray-900 pb-1"},"Přiřazená místnost",-1)),l(s(re),{modelValue:k.value,"onUpdate:modelValue":i[8]||(i[8]=t=>k.value=t)},{default:d(()=>[e("div",Pt,[e("div",Mt,[l(s(de),{class:"w-full border-none py-2 pl-4 pr-10 text-sm leading-5 text-gray-900 placeholder:text-gray-400 focus:ring-0 font-medium",placeholder:"Zvolte místnost...",displayValue:t=>t?`${t.name}`.trim():"",onChange:i[5]||(i[5]=t=>_.value=t.target.value)},null,8,["displayValue"]),k.value&&k.value.name?(a(),o("div",St,[e("button",{onClick:i[6]||(i[6]=P(t=>k.value=null,["prevent"])),type:"button"},[l(s(ye),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):v("",!0),l(s(ue),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:d(()=>[l(s(ne),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),l(s(ee),{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:i[7]||(i[7]=t=>_.value="")},{default:d(()=>[l(s(ce),{class:"absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},{default:d(()=>[A.value?v("",!0):(a(),o("div",Ut,[z.value.length===0&&_.value!==""?(a(),o("div",Vt," Žádná místnost nenalezena. ")):v("",!0)])),A.value?(a(),o("div",zt,[l(E,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(a(),o("div",Et,[(a(!0),o(I,null,D(z.value,t=>(a(),N(s(ve),{as:"template",key:t.id,value:t},{default:d(({active:b})=>{var M,T,$;return[e("li",{class:w(["relative cursor-default select-none py-2 pl-4 pr-10",{"bg-main-color-600 text-white":b,"text-gray-900":!b}])},[e("span",{class:w(["block truncate",{"font-medium":((M=k.value)==null?void 0:M.id)==t.id,"font-normal":((T=k.value)==null?void 0:T.id)!=t.id}])},R(t==null?void 0:t.name),3),(($=k.value)==null?void 0:$.id)==t.id?(a(),o("span",{key:0,class:w(["absolute inset-y-0 right-0 flex items-center pr-3",{"text-white":b,"text-main-color-600":!b}])},[l(Q,{class:"h-5 w-5","aria-hidden":"true"})],2)):v("",!0)],2)]}),_:2},1032,["value"]))),128))]))]),_:1})]),_:1})])]),_:1},8,["modelValue"])])):v("",!0)]),e("div",Tt,[e("div",Ft,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:i[9]||(i[9]=P(t=>L(),["prevent"]))}," Zavřít "),e("button",{onClick:i[10]||(i[10]=P(t=>le(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Uložit ")])])])]),_:1})]),_:1},8,["show"])}}},It={class:"border-t p-5"},jt={class:"text-right space-x-3"},Dt={__name:"deleteProtocolModal",props:{selectedProtocol:{type:Object,required:!0}},emits:["reloadGroups"],setup(me,{expose:J,emit:W}){const H=W,F=me,j=c(!1);$e("debugModeGlobalVar");const y=c(!1);q(()=>F.selectedProtocol,(C,p)=>{});function m(){j.value=!1}async function x(){j.value=!0}async function k(){S.check("moves.delete")||S.check("property.master")?await Z.post("api/moves/"+F.selectedProtocol.id+"/delete",{}).then(C=>{Oe.success(C.data.message)}).catch(C=>{console.log(C)}):y.value=!1,m(),H("reloadProtocols",!0)}return J({openModal:x}),(C,p)=>(a(),N(s(ee),{appear:"",show:j.value,as:"template",onClose:p[3]||(p[3]=h=>m())},{default:d(()=>[l(Pe,{size:"sm"},{"modal-title":d(()=>p[4]||(p[4]=[Ce("Smazat protokol")])),"modal-close-button":d(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:p[0]||(p[0]=h=>m())},[l(s(ye),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":d(()=>[e("div",null,[p[5]||(p[5]=e("div",{class:"p-6"},[e("p",null,"Opravdu si přejete protokol smazat?")],-1)),e("div",It,[e("div",jt,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:p[1]||(p[1]=P(h=>m(),["prevent"]))}," Zavřít "),e("button",{onClick:p[2]||(p[2]=P(h=>k(),["prevent"])),class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-red-600",type:"submit"}," Smazat ")])])])]),_:1})]),_:1},8,["show"]))}},Nt={class:"space-y-6"},Zt={class:"px-0"},At={class:"bg-white border border-zinc-200/70 rounded-md p-5"},Gt={class:"sm:flex justify-between items-center gap-x-20 gap-y-4"},Bt={class:"grid grid-cols-7 grow gap-4"},Qt={class:"col-span-2"},Xt={class:"w-full text-left"},qt={key:0,class:"text-gray-900"},Kt={key:1,class:"text-gray-400"},Jt={key:0,class:"flex ml-4"},Wt=["onClick"],Ht={class:"col-span-2"},Yt={class:"w-full text-left"},el={key:0,class:"text-gray-900"},tl={key:1,class:"text-gray-400"},ll={key:0,class:"flex ml-4"},al=["onClick"],sl={class:"col-span-2"},ol={class:"relative"},nl={class:"relative w-full cursor-default overflow-hidden rounded-md bg-white border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 text-sm"},il={key:0,class:"absolute top-1.5 right-8 w-6 h-6"},rl={key:0},dl={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},ul={key:1,class:"h-20 max-h-20 overflow-hidden"},cl={key:2},vl={class:"col-span-2"},ml={class:"relative"},pl={class:"relative w-full cursor-default overflow-hidden rounded-md bg-white border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 text-sm"},fl={key:0,class:"absolute top-1.5 right-8 w-6 h-6"},gl={key:0},yl={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},xl={key:1,class:"h-20 max-h-20 overflow-hidden"},hl={key:2},bl={class:"flex items-center gap-4"},_l={class:"flow-root bg-white border border-zinc-200/70 rounded-md"},wl={class:"sm:-mx-6 lg:-mx-8"},kl={class:"inline-block min-w-full align-middle sm:px-6 lg:px-8"},$l={key:0,class:"min-w-full divide-y divide-gray-200"},Rl={key:0,scope:"col",class:"py-4 pl-5 pr-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"},Cl={key:0,class:"divide-y divide-gray-200"},Ol={class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600"},Pl={key:0,class:"flex items-center"},Ml={key:0},Sl={key:1,class:"text-amber-600"},Ul={key:1,class:"flex items-center"},Vl={key:0},zl={key:1,class:"text-amber-600"},El={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},Tl={key:0},Fl={key:0,class:"px-2 py-1 rounded-md text-xs text-green-600 inline-block"},Ll={class:"flex items-center"},Il={key:1,class:"px-2 py-1 rounded-md text-xs text-green-600 inline-block"},jl={class:"flex items-center"},Dl={key:2,class:"px-2 py-1 rounded-md text-xs text-red-600 inline-block"},Nl={class:"flex items-center"},Zl={key:3,class:"px-2 py-1 rounded-md text-xs text-red-600 inline-block"},Al={class:"flex items-center"},Gl={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},Bl={key:0},Ql={key:0,class:"border border-green-600 px-2 py-1 rounded-md text-xs text-green-600 inline-block"},Xl={class:"flex"},ql={key:1,class:"border border-red-600 px-2 py-1 rounded-md text-xs text-red-600 inline-block"},Kl={class:"flex"},Jl={class:"whitespace-nowrap py-4 pl-3 pr-5 text-sm text-gray-600 text-end"},Wl={class:"flex justify-end items-center gap-2"},Hl=["onClick"],Yl=["onClick"],ea=["onClick"],ta={key:1},la={class:"text-sm mt-2"},aa={class:"flex items-center"},sa={class:"flex items-center"},ka={__name:"Protocols",setup(me){const J=c(),W=c(),H=c(),F=ze();$e("debugModeGlobalVar");const j=c(["protocols"]),y=c(!1),m=c(1),x=c({}),k=c({}),C=c(""),p=c({id:"",name:""}),h=c({id:"",name:""}),g=c(null),_=c(null),U=c([]),A=c([]),V=c(""),z=c(""),G=c(!1),B=c(!1),L=oe(()=>V.value===""?U.value:U.value.filter(f=>`${f.first_name} ${f.last_name}`.toLowerCase().replace(/\s+/g,"").includes(V.value.toLowerCase().replace(/\s+/g,"")))),te=oe(()=>z.value===""?A.value:A.value.filter(f=>f.name.toLowerCase().replace(/\s+/g,"").includes(z.value.toLowerCase().replace(/\s+/g,"")))),le=ie(async()=>{try{const f=await Z.get("/api/users?page=1&perpage=50&search="+V.value+"&with_deleted=1");U.value=f.data.data}catch(f){console.error(f)}finally{G.value=!1}},300),u=ie(async()=>{try{const f=await Z.get("/api/rooms?page=1&perpage=50&search="+z.value);A.value=f.data.data}catch(f){console.error(f)}finally{B.value=!1}},300);q(V,()=>{G.value=!0,le()}),q(z,()=>{B.value=!0,u()}),q(()=>F.perPage,(f,r)=>{y.value=!0,m.value=1,t()});const i=c([{id:"PROCESSED",name:"Vyřízeno"},{id:"UNPROCESSED",name:"Nevyřízeno"}]),E=c([{id:"TO_ROOM",name:"Přiřazeno do místnosti"},{id:"TO_USER",name:"Přiřazeno uživateli"},{id:"FROM_ROOM",name:"Odebráno z místnosti"},{id:"FROM_USER",name:"Odebráno uživateli"}]);Ve(()=>{t(),u(),le()});function Q(f){C.value=f}async function t(){var f,r,K,X;y.value=!0,S.check("moves.read")?await Z.get("api/moves?page="+m.value+"&perpage="+F.perPage+"&state="+(((f=p.value)==null?void 0:f.id)||"")+"&type="+(((r=h.value)==null?void 0:r.id)||"")+"&user_id="+(((K=g.value)==null?void 0:K.id)||"")+"&room_id="+(((X=_.value)==null?void 0:X.id)||"")).then(ae=>{k.value=ae.data.data,x.value=ae.data.meta,y.value=!1}).catch(ae=>{console.log(ae)}):y.value=!1}function b(f){Z.post("api/moves/"+f.id+"/generate-pdf",{type:f.type},{responseType:"blob"}).then(r=>{const K=window.URL.createObjectURL(new Blob([r.data])),X=document.createElement("a");X.href=K,X.setAttribute("download","Protokol.pdf"),document.body.appendChild(X),X.click()}).catch(r=>{console.error("Chyba při získávání souboru:",r)})}function M(f){m.value=f,t()}function T(){y.value=!0,m.value=1,p.value={id:"",name:""},h.value={id:"",name:""},g.value="",_.value="",t()}function $(){y.value=!0,m.value=1,t()}return(f,r)=>{const K=se("VueSpinner"),X=se("TransitionRoot"),ae=se("router-link");return a(),o(I,null,[l(Ee,{breadCrumbs:j.value},{topbarButtons:d(()=>[s(S).check("moves.create")||s(S).check("property.master")?(a(),o("button",{key:0,onClick:r[0]||(r[0]=P(n=>f.$refs.createProtocolRef.openModal(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},"Nový protokol ")):v("",!0)]),_:1},8,["breadCrumbs"]),e("div",Nt,[e("div",Zt,[e("div",At,[e("div",Gt,[e("div",Bt,[e("div",Qt,[l(s(be),{as:"div",class:"relative inline-block text-left w-full"},{default:d(()=>[e("div",null,[l(s(_e),{class:"inline-flex w-full justify-center items-center rounded-md bg-white px-4 py-2 text-sm font-medium text-white border border-gray-300"},{default:d(()=>[e("div",Xt,[h.value&&h.value.name?(a(),o("span",qt,R(h.value.name),1)):(a(),o("span",Kt,"Typ protokolu..."))]),l(s(he),{class:"ml-2 -mr-1 h-5 w-5 text-violet-200 hover:text-violet-100","aria-hidden":"true"}),h.value&&h.value.name?(a(),o("div",Jt,[e("button",{onClick:r[1]||(r[1]=P(n=>(h.value={id:"",name:""},$()),["prevent"]))},[l(s(ge),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):v("",!0)]),_:1})]),l(xe,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:d(()=>[l(s(we),{class:"absolute z-10 right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:d(()=>[(a(!0),o(I,null,D(E.value,n=>(a(),o("div",{key:n.id,class:"px-1 py-1"},[l(s(ke),null,{default:d(({active:O})=>[e("button",{onClick:Y=>h.value=n,class:w([O?"bg-main-color-600 text-white":"text-gray-900","group flex w-full rounded-md px-2 py-2 text-sm text-left"])},R(n.name),11,Wt)]),_:2},1024)]))),128))]),_:1})]),_:1})]),_:1})]),e("div",Ht,[l(s(be),{as:"div",class:"relative inline-block text-left w-full"},{default:d(()=>[e("div",null,[l(s(_e),{class:"inline-flex w-full justify-center items-center rounded-md bg-white px-4 py-2 text-sm font-medium text-white border border-gray-300"},{default:d(()=>[e("div",Yt,[p.value&&p.value.name?(a(),o("span",el,R(p.value.name),1)):(a(),o("span",tl,"Stav protokolu..."))]),l(s(he),{class:"ml-2 -mr-1 h-5 w-5 text-violet-200 hover:text-violet-100","aria-hidden":"true"}),p.value&&p.value.name?(a(),o("div",ll,[e("button",{onClick:r[2]||(r[2]=P(n=>(p.value={id:"",name:""},$()),["prevent"]))},[l(s(ge),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):v("",!0)]),_:1})]),l(xe,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:d(()=>[l(s(we),{class:"absolute z-10 right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:d(()=>[(a(!0),o(I,null,D(i.value,n=>(a(),o("div",{key:n.id,class:"px-1 py-1"},[l(s(ke),null,{default:d(({active:O})=>[e("button",{onClick:Y=>p.value=n,class:w([O?"bg-main-color-600 text-white":"text-gray-900","group flex w-full rounded-md px-2 py-2 text-sm text-left"])},R(n.name),11,al)]),_:2},1024)]))),128))]),_:1})]),_:1})]),_:1})]),e("div",sl,[l(s(re),{modelValue:g.value,"onUpdate:modelValue":r[6]||(r[6]=n=>g.value=n)},{default:d(()=>[e("div",ol,[e("div",nl,[l(s(de),{class:"w-full border-none py-2 pl-4 pr-10 text-sm leading-5 text-gray-900 placeholder:text-gray-400 focus:ring-0 font-medium",placeholder:"Zvolte uživatele...",displayValue:n=>n?`${n.first_name||""} ${n.middle_name?n.middle_name+" ":""}${n.last_name||""}`.trim():"",onChange:r[3]||(r[3]=n=>V.value=n.target.value)},null,8,["displayValue"]),g.value&&g.value.first_name?(a(),o("div",il,[e("button",{onClick:r[4]||(r[4]=P(n=>(g.value=null,$()),["prevent"]))},[l(s(ge),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):v("",!0),l(s(ue),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:d(()=>[l(s(ne),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),l(X,{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:r[5]||(r[5]=n=>V.value="")},{default:d(()=>[l(s(ce),{class:"absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},{default:d(()=>[G.value?v("",!0):(a(),o("div",rl,[L.value.length===0&&V.value!==""?(a(),o("div",dl," Žádný uživatel nenalezen. ")):v("",!0)])),G.value?(a(),o("div",ul,[l(K,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(a(),o("div",cl,[(a(!0),o(I,null,D(L.value,n=>(a(),N(s(ve),{as:"template",key:n.id,value:n},{default:d(({active:O})=>{var Y,pe,fe;return[e("li",{class:w(["relative cursor-default select-none py-2 pl-4 pr-10",{"bg-main-color-600 text-white":O,"text-gray-900":!O}])},[e("span",{class:w(["block truncate",{"font-medium":((Y=g.value)==null?void 0:Y.id)==n.id,"font-normal":((pe=g.value)==null?void 0:pe.id)!=n.id}])},R((n==null?void 0:n.first_name)+" "+(n!=null&&n.middle_name?(n==null?void 0:n.middle_name)+" ":"")+(n==null?void 0:n.last_name)),3),((fe=g.value)==null?void 0:fe.id)==n.id?(a(),o("span",{key:0,class:w(["absolute inset-y-0 right-0 flex items-center pr-3",{"text-white":O,"text-main-color-600":!O}])},[l(s(Re),{class:"h-5 w-5","aria-hidden":"true"})],2)):v("",!0)],2)]}),_:2},1032,["value"]))),128))]))]),_:1})]),_:1})])]),_:1},8,["modelValue"])]),e("div",vl,[l(s(re),{modelValue:_.value,"onUpdate:modelValue":r[10]||(r[10]=n=>_.value=n)},{default:d(()=>[e("div",ml,[e("div",pl,[l(s(de),{class:"w-full border-none py-2 pl-4 pr-10 text-sm leading-5 text-gray-900 placeholder:text-gray-400 focus:ring-0 font-medium",placeholder:"Zvolte místnost...",displayValue:n=>n?`${n.name}`.trim():"",onChange:r[7]||(r[7]=n=>z.value=n.target.value)},null,8,["displayValue"]),_.value&&_.value.name?(a(),o("div",fl,[e("button",{onClick:r[8]||(r[8]=P(n=>(_.value=null,$()),["prevent"]))},[l(s(ge),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):v("",!0),l(s(ue),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:d(()=>[l(s(ne),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),l(X,{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:r[9]||(r[9]=n=>z.value="")},{default:d(()=>[l(s(ce),{class:"absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},{default:d(()=>[B.value?v("",!0):(a(),o("div",gl,[te.value.length===0&&z.value!==""?(a(),o("div",yl," Žádná místnost nenalezena. ")):v("",!0)])),B.value?(a(),o("div",xl,[l(K,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(a(),o("div",hl,[(a(!0),o(I,null,D(te.value,n=>(a(),N(s(ve),{as:"template",key:n.id,value:n},{default:d(({active:O})=>{var Y,pe,fe;return[e("li",{class:w(["relative cursor-default select-none py-2 pl-4 pr-10",{"bg-main-color-600 text-white":O,"text-gray-900":!O}])},[e("span",{class:w(["block truncate",{"font-medium":((Y=_.value)==null?void 0:Y.id)==n.id,"font-normal":((pe=_.value)==null?void 0:pe.id)!=n.id}])},R(n==null?void 0:n.name),3),((fe=_.value)==null?void 0:fe.id)==n.id?(a(),o("span",{key:0,class:w(["absolute inset-y-0 right-0 flex items-center pr-3",{"text-white":O,"text-main-color-600":!O}])},[l(s(Re),{class:"h-5 w-5","aria-hidden":"true"})],2)):v("",!0)],2)]}),_:2},1032,["value"]))),128))]))]),_:1})]),_:1})])]),_:1},8,["modelValue"])])]),e("div",bl,[e("button",{onClick:r[11]||(r[11]=n=>T()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},r[17]||(r[17]=[e("span",null,"Resetovat",-1)])),e("button",{onClick:r[12]||(r[12]=n=>$()),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700"}," Filtrovat ")])])])]),e("div",null,[e("div",_l,[e("div",wl,[e("div",kl,[y.value==!1?(a(),o("table",$l,[e("thead",null,[e("tr",null,[r[18]||(r[18]=e("th",{scope:"col",class:"py-4 pl-5 pr-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"}," Uživatel / místnost ",-1)),r[19]||(r[19]=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Směr ",-1)),r[20]||(r[20]=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"},"Stav ",-1)),s(S).check("moves.edit")||s(S).check("moves.delete")?(a(),o("th",Rl)):v("",!0)])]),k.value&&k.value.length?(a(),o("tbody",Cl,[(a(!0),o(I,null,D(k.value,n=>(a(),o("tr",{key:n.id},[e("td",Ol,[n.user?(a(),o("span",Pl,[l(s(Me),{class:"h-5 w-5 mr-2","aria-hidden":"true"}),n.user.full_name?(a(),o("span",Ml,R(n.user.full_name),1)):(a(),o("span",Sl,R(n.user.email)+" - Uživateli chybí jméno ",1))])):v("",!0),n.room?(a(),o("span",Ul,[l(s(Se),{class:"h-5 w-5 mr-2","aria-hidden":"true"}),n.room.name?(a(),o("span",Vl,R(n.room.name),1)):(a(),o("span",zl," Místnosti chybí jméno "))])):v("",!0)]),e("td",El,[n.type?(a(),o("div",Tl,[n.type=="TO_USER"?(a(),o("div",Fl,[e("div",Ll,[l(s(Te),{class:"h-5 w-5 text-green-600 mr-1","aria-hidden":"true"}),r[21]||(r[21]=e("span",{class:"font-semibold"},"Přiřazeno uživateli",-1))])])):v("",!0),n.type=="TO_ROOM"?(a(),o("div",Il,[e("div",jl,[l(s(Fe),{class:"h-5 w-5 text-green-600 mr-1","aria-hidden":"true"}),r[22]||(r[22]=e("span",{class:"font-semibold"},"Přiřazeno do místnosti",-1))])])):n.type=="FROM_USER"?(a(),o("div",Dl,[e("div",Nl,[l(s(Le),{class:"h-5 w-5 text-red-600 mr-1","aria-hidden":"true"}),r[23]||(r[23]=e("span",{class:"font-semibold"},"Odebráno uživateli",-1))])])):n.type=="FROM_ROOM"?(a(),o("div",Zl,[e("div",Al,[l(s(Ie),{class:"h-5 w-5 text-red-600 mr-1","aria-hidden":"true"}),r[24]||(r[24]=e("span",{class:"font-semibold"},"Odebráno z místnosti",-1))])])):v("",!0)])):v("",!0)]),e("td",Gl,[n.state?(a(),o("div",Bl,[n.state=="PROCESSED"?(a(),o("div",Ql,[e("div",Xl,[l(s(Re),{class:"h-4 w-4 text-green-600 mr-1","aria-hidden":"true"}),r[25]||(r[25]=e("span",null,"Vyřízeno",-1))])])):n.state=="UNPROCESSED"?(a(),o("div",ql,[e("div",Kl,[l(s(je),{class:"h-4 w-4 text-red-600 mr-1","aria-hidden":"true"}),r[26]||(r[26]=e("span",null,"Nevyřízeno",-1))])])):v("",!0)])):v("",!0)]),e("td",Jl,[e("div",Wl,[l(ae,{to:{name:"protocol-detail",params:{id:n.id}}},{default:d(()=>[l(s(De),{class:"h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 p-2 rounded-lg","aria-hidden":"true"})]),_:2},1032,["to"]),(s(S).check("moves.edit")||s(S).check("property.master"))&&n.state=="UNPROCESSED"?(a(),o("button",{key:0,onClick:P(O=>(Q(n),f.$refs.editProtocolRef.openModal()),["prevent"])},[l(s(Ne),{class:"h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 p-2 rounded-lg","aria-hidden":"true"})],8,Hl)):v("",!0),n.state=="PROCESSED"?(a(),o("button",{key:1,onClick:P(O=>b(n),["prevent"])},[l(s(Ze),{class:"h-8 w-8 text-white bg-main-color-600 hover:bg-main-color-700 duration-150 p-2 rounded-lg","aria-hidden":"true"})],8,Yl)):v("",!0),s(S).check("moves.delete")||s(S).check("property.master")?(a(),o("button",{key:2,onClick:P(O=>(Q(n),f.$refs.deleteProtocolRef.openModal()),["prevent"])},[l(s(ge),{class:"h-8 w-8 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-1.5 rounded-lg","aria-hidden":"true"})],8,ea)):v("",!0)])])]))),128))])):(a(),o("tbody",ta,r[27]||(r[27]=[e("tr",null,[e("td",{colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},"Nebyly nalezeny žádné Protokoly.")],-1)])))])):(a(),N(K,{key:1,class:"mx-auto text-spinner-color",size:"40"}))])])]),x.value!==null?(a(),N(Ae,{key:0,meta:x.value,onSetPage:M,modelValue:m.value,"onUpdate:modelValue":r[13]||(r[13]=n=>m.value=n)},null,8,["meta","modelValue"])):v("",!0)])]),e("div",la,[e("span",aa,[l(s(Me),{class:"h-4 w-4 mr-2","aria-hidden":"true"}),r[28]||(r[28]=e("span",null," - Uživatel",-1))]),e("span",sa,[l(s(Se),{class:"h-4 w-4 mr-2","aria-hidden":"true"}),r[29]||(r[29]=e("span",null," - Místnost",-1))])]),l(mt,{ref_key:"createProtocolRef",ref:J,onReloadProtocols:r[14]||(r[14]=n=>t())},null,512),l(Lt,{ref_key:"editProtocolRef",ref:W,selectedProtocol:C.value,onReloadProtocols:r[15]||(r[15]=n=>t())},null,8,["selectedProtocol"]),l(Dt,{ref_key:"deleteProtocolRef",ref:H,selectedProtocol:C.value,onReloadProtocols:r[16]||(r[16]=n=>t())},null,8,["selectedProtocol"])],64)}}};export{ka as default};
