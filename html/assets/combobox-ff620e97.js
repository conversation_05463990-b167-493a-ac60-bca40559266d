import{u as k,y as W,c as X,l as M,o as y,f as Y,K as Z,b as $,H as L,T as z,t as K,N as U,p as ee,I as te,a as w}from"./hidden-a70e7379.js";import{x as oe,a as C,u as ae}from"./use-tracked-pointer-22b1e651.js";import{b as le}from"./use-resolve-button-type-b3f8121e.js";import{p as ne}from"./use-tree-walker-5ee0cc2f.js";import{d as ue,e as ie}from"./use-controllable-a2a436f1.js";import{n as re}from"./dialog-a220f9aa.js";import{H as N,a as D,J as x,ac as f,I as se,C as J,D as _,a9 as G,F as pe,L as q,K as ve,p as A,j as de}from"./index-e505f2bc.js";function be(o,O){return o===O}var ce=(o=>(o[o.Open=0]="Open",o[o.Closed=1]="Closed",o))(ce||{}),fe=(o=>(o[o.Single=0]="Single",o[o.Multi=1]="Multi",o))(fe||{}),me=(o=>(o[o.Pointer=0]="Pointer",o[o.Other=1]="Other",o))(me||{});let Q=Symbol("ComboboxContext");function j(o){let O=de(Q,null);if(O===null){let S=new Error(`<${o} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(S,j),S}return O}let Ie=N({name:"Combobox",emits:{"update:modelValue":o=>!0},props:{as:{type:[Object,String],default:"template"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],default:()=>be},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},name:{type:String},nullable:{type:Boolean,default:!1},multiple:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(o,{slots:O,attrs:S,emit:P}){let e=D(1),t=D(null),v=D(null),I=D(null),i=D(null),b=D({static:!1,hold:!1}),m=D([]),g=D(null),T=D(1),V=D(!1);function B(l=p=>p){let p=g.value!==null?m.value[g.value]:null,d=te(l(m.value.slice()),c=>y(c.dataRef.domRef)),u=p?d.indexOf(p):null;return u===-1&&(u=null),{options:d,activeOptionIndex:u}}let a=x(()=>o.multiple?1:0),n=x(()=>o.nullable),[s,h]=ue(x(()=>o.modelValue===void 0?k(a.value,{[1]:[],[0]:void 0}):o.modelValue),l=>P("update:modelValue",l),x(()=>o.defaultValue)),r={comboboxState:e,value:s,mode:a,compare(l,p){if(typeof o.by=="string"){let d=o.by;return(l==null?void 0:l[d])===(p==null?void 0:p[d])}return o.by(l,p)},defaultValue:x(()=>o.defaultValue),nullable:n,inputRef:v,labelRef:t,buttonRef:I,optionsRef:i,disabled:x(()=>o.disabled),options:m,change(l){h(l)},activeOptionIndex:x(()=>{if(V.value&&g.value===null&&m.value.length>0){let l=m.value.findIndex(p=>!p.dataRef.disabled);if(l!==-1)return l}return g.value}),activationTrigger:T,optionsPropsRef:b,closeCombobox(){V.value=!1,!o.disabled&&e.value!==1&&(e.value=1,g.value=null)},openCombobox(){if(V.value=!0,o.disabled||e.value===0)return;let l=m.value.findIndex(p=>{let d=f(p.dataRef.value);return k(a.value,{[0]:()=>r.compare(f(r.value.value),f(d)),[1]:()=>f(r.value.value).some(u=>r.compare(f(u),f(d)))})});l!==-1&&(g.value=l),e.value=0},goToOption(l,p,d){if(V.value=!1,o.disabled||i.value&&!b.value.static&&e.value===1)return;let u=B();if(u.activeOptionIndex===null){let R=u.options.findIndex(H=>!H.dataRef.disabled);R!==-1&&(u.activeOptionIndex=R)}let c=oe(l===C.Specific?{focus:C.Specific,id:p}:{focus:l},{resolveItems:()=>u.options,resolveActiveIndex:()=>u.activeOptionIndex,resolveId:R=>R.id,resolveDisabled:R=>R.dataRef.disabled});g.value=c,T.value=d??1,m.value=u.options},selectOption(l){let p=m.value.find(u=>u.id===l);if(!p)return;let{dataRef:d}=p;h(k(a.value,{[0]:()=>d.value,[1]:()=>{let u=f(r.value.value).slice(),c=f(d.value),R=u.findIndex(H=>r.compare(c,f(H)));return R===-1?u.push(c):u.splice(R,1),u}}))},selectActiveOption(){if(r.activeOptionIndex.value===null)return;let{dataRef:l,id:p}=m.value[r.activeOptionIndex.value];h(k(a.value,{[0]:()=>l.value,[1]:()=>{let d=f(r.value.value).slice(),u=f(l.value),c=d.findIndex(R=>r.compare(u,f(R)));return c===-1?d.push(u):d.splice(c,1),d}})),r.goToOption(C.Specific,p)},registerOption(l,p){let d={id:l,dataRef:p},u=B(c=>[...c,d]);if(g.value===null){let c=p.value.value;k(a.value,{[0]:()=>r.compare(f(r.value.value),f(c)),[1]:()=>f(r.value.value).some(R=>r.compare(f(R),f(c)))})&&(u.activeOptionIndex=u.options.indexOf(d))}m.value=u.options,g.value=u.activeOptionIndex,T.value=1},unregisterOption(l){var p;r.activeOptionIndex.value!==null&&((p=r.options.value[r.activeOptionIndex.value])==null?void 0:p.id)===l&&(V.value=!0);let d=B(u=>{let c=u.findIndex(R=>R.id===l);return c!==-1&&u.splice(c,1),u});m.value=d.options,g.value=d.activeOptionIndex,T.value=1}};W([v,I,i],()=>r.closeCombobox(),x(()=>e.value===0)),se(Q,r),X(x(()=>k(e.value,{[0]:M.Open,[1]:M.Closed})));let F=x(()=>r.activeOptionIndex.value===null?null:m.value[r.activeOptionIndex.value].dataRef.value),E=x(()=>{var l;return(l=y(v))==null?void 0:l.closest("form")});return J(()=>{_([E],()=>{if(!E.value||o.defaultValue===void 0)return;function l(){r.change(o.defaultValue)}return E.value.addEventListener("reset",l),()=>{var p;(p=E.value)==null||p.removeEventListener("reset",l)}},{immediate:!0})}),()=>{let{name:l,disabled:p,...d}=o,u={open:e.value===0,disabled:p,activeIndex:r.activeOptionIndex.value,activeOption:F.value,value:s.value};return G(pe,[...l!=null&&s.value!=null?ie({[l]:s.value}).map(([c,R])=>G(Y,Z({features:$.Hidden,key:c,as:"input",type:"hidden",hidden:!0,readOnly:!0,name:c,value:R}))):[],L({theirProps:{...S,...z(d,["modelValue","defaultValue","nullable","multiple","onUpdate:modelValue","by"])},ourProps:{},slot:u,slots:O,attrs:S,name:"Combobox"})])}}}),he=N({name:"ComboboxLabel",props:{as:{type:[Object,String],default:"label"},id:{type:String,default:()=>`headlessui-combobox-label-${K()}`}},setup(o,{attrs:O,slots:S}){let P=j("ComboboxLabel");function e(){var t;(t=y(P.inputRef))==null||t.focus({preventScroll:!0})}return()=>{let t={open:P.comboboxState.value===0,disabled:P.disabled.value},{id:v,...I}=o,i={id:v,ref:P.labelRef,onClick:e};return L({ourProps:i,theirProps:I,slot:t,attrs:O,slots:S,name:"ComboboxLabel"})}}}),Pe=N({name:"ComboboxButton",props:{as:{type:[Object,String],default:"button"},id:{type:String,default:()=>`headlessui-combobox-button-${K()}`}},setup(o,{attrs:O,slots:S,expose:P}){let e=j("ComboboxButton");P({el:e.buttonRef,$el:e.buttonRef});function t(i){e.disabled.value||(e.comboboxState.value===0?e.closeCombobox():(i.preventDefault(),e.openCombobox()),A(()=>{var b;return(b=y(e.inputRef))==null?void 0:b.focus({preventScroll:!0})}))}function v(i){switch(i.key){case w.ArrowDown:i.preventDefault(),i.stopPropagation(),e.comboboxState.value===1&&e.openCombobox(),A(()=>{var b;return(b=e.inputRef.value)==null?void 0:b.focus({preventScroll:!0})});return;case w.ArrowUp:i.preventDefault(),i.stopPropagation(),e.comboboxState.value===1&&(e.openCombobox(),A(()=>{e.value.value||e.goToOption(C.Last)})),A(()=>{var b;return(b=e.inputRef.value)==null?void 0:b.focus({preventScroll:!0})});return;case w.Escape:if(e.comboboxState.value!==0)return;i.preventDefault(),e.optionsRef.value&&!e.optionsPropsRef.value.static&&i.stopPropagation(),e.closeCombobox(),A(()=>{var b;return(b=e.inputRef.value)==null?void 0:b.focus({preventScroll:!0})});return}}let I=le(x(()=>({as:o.as,type:O.type})),e.buttonRef);return()=>{var i,b;let m={open:e.comboboxState.value===0,disabled:e.disabled.value,value:e.value.value},{id:g,...T}=o,V={ref:e.buttonRef,id:g,type:I.value,tabindex:"-1","aria-haspopup":"listbox","aria-controls":(i=y(e.optionsRef))==null?void 0:i.id,"aria-expanded":e.disabled.value?void 0:e.comboboxState.value===0,"aria-labelledby":e.labelRef.value?[(b=y(e.labelRef))==null?void 0:b.id,g].join(" "):void 0,disabled:e.disabled.value===!0?!0:void 0,onKeydown:v,onClick:t};return L({ourProps:V,theirProps:T,slot:m,attrs:O,slots:S,name:"ComboboxButton"})}}}),Te=N({name:"ComboboxInput",props:{as:{type:[Object,String],default:"input"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},displayValue:{type:Function},defaultValue:{type:String,default:void 0},id:{type:String,default:()=>`headlessui-combobox-input-${K()}`}},emits:{change:o=>!0},setup(o,{emit:O,attrs:S,slots:P,expose:e}){let t=j("ComboboxInput"),v={value:!1};e({el:t.inputRef,$el:t.inputRef});let I=x(()=>{var a;let n=t.value.value;return y(t.inputRef)?typeof o.displayValue<"u"&&n!==void 0?(a=o.displayValue(n))!=null?a:"":typeof n=="string"?n:"":""});J(()=>{_([I,t.comboboxState],([a,n],[s,h])=>{if(v.value)return;let r=y(t.inputRef);r&&(h===0&&n===1||a!==s)&&(r.value=a)},{immediate:!0}),_([t.comboboxState],([a],[n])=>{if(a===0&&n===1){let s=y(t.inputRef);if(!s)return;let h=s.value,{selectionStart:r,selectionEnd:F,selectionDirection:E}=s;s.value="",s.value=h,E!==null?s.setSelectionRange(r,F,E):s.setSelectionRange(r,F)}})});let i=D(!1);function b(){i.value=!0}function m(){setTimeout(()=>{i.value=!1})}function g(a){switch(v.value=!0,a.key){case w.Backspace:case w.Delete:if(t.mode.value!==0||!t.nullable.value)return;let n=a.currentTarget;requestAnimationFrame(()=>{if(n.value===""){t.change(null);let s=y(t.optionsRef);s&&(s.scrollTop=0),t.goToOption(C.Nothing)}});break;case w.Enter:if(v.value=!1,t.comboboxState.value!==0||i.value)return;if(a.preventDefault(),a.stopPropagation(),t.activeOptionIndex.value===null){t.closeCombobox();return}t.selectActiveOption(),t.mode.value===0&&t.closeCombobox();break;case w.ArrowDown:return v.value=!1,a.preventDefault(),a.stopPropagation(),k(t.comboboxState.value,{[0]:()=>t.goToOption(C.Next),[1]:()=>t.openCombobox()});case w.ArrowUp:return v.value=!1,a.preventDefault(),a.stopPropagation(),k(t.comboboxState.value,{[0]:()=>t.goToOption(C.Previous),[1]:()=>{t.openCombobox(),A(()=>{t.value.value||t.goToOption(C.Last)})}});case w.Home:if(a.shiftKey)break;return v.value=!1,a.preventDefault(),a.stopPropagation(),t.goToOption(C.First);case w.PageUp:return v.value=!1,a.preventDefault(),a.stopPropagation(),t.goToOption(C.First);case w.End:if(a.shiftKey)break;return v.value=!1,a.preventDefault(),a.stopPropagation(),t.goToOption(C.Last);case w.PageDown:return v.value=!1,a.preventDefault(),a.stopPropagation(),t.goToOption(C.Last);case w.Escape:if(v.value=!1,t.comboboxState.value!==0)return;a.preventDefault(),t.optionsRef.value&&!t.optionsPropsRef.value.static&&a.stopPropagation(),t.closeCombobox();break;case w.Tab:if(v.value=!1,t.comboboxState.value!==0)return;t.mode.value===0&&t.selectActiveOption(),t.closeCombobox();break}}function T(a){t.openCombobox(),O("change",a)}function V(){v.value=!1}let B=x(()=>{var a,n,s,h;return(h=(s=(n=o.defaultValue)!=null?n:t.defaultValue.value!==void 0?(a=o.displayValue)==null?void 0:a.call(o,t.defaultValue.value):null)!=null?s:t.defaultValue.value)!=null?h:""});return()=>{var a,n,s,h,r,F;let E={open:t.comboboxState.value===0},{id:l,displayValue:p,onChange:d,...u}=o,c={"aria-controls":(a=t.optionsRef.value)==null?void 0:a.id,"aria-expanded":t.disabled.value?void 0:t.comboboxState.value===0,"aria-activedescendant":t.activeOptionIndex.value===null||(n=t.options.value[t.activeOptionIndex.value])==null?void 0:n.id,"aria-labelledby":(r=(s=y(t.labelRef))==null?void 0:s.id)!=null?r:(h=y(t.buttonRef))==null?void 0:h.id,"aria-autocomplete":"list",id:l,onCompositionstart:b,onCompositionend:m,onKeydown:g,onInput:T,onBlur:V,role:"combobox",type:(F=S.type)!=null?F:"text",tabIndex:0,ref:t.inputRef,defaultValue:B.value};return L({ourProps:c,theirProps:u,slot:E,attrs:S,slots:P,features:U.RenderStrategy|U.Static,name:"ComboboxInput"})}}}),Ve=N({name:"ComboboxOptions",props:{as:{type:[Object,String],default:"ul"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},hold:{type:[Boolean],default:!1}},setup(o,{attrs:O,slots:S,expose:P}){let e=j("ComboboxOptions"),t=`headlessui-combobox-options-${K()}`;P({el:e.optionsRef,$el:e.optionsRef}),q(()=>{e.optionsPropsRef.value.static=o.static}),q(()=>{e.optionsPropsRef.value.hold=o.hold});let v=ee(),I=x(()=>v!==null?(v.value&M.Open)===M.Open:e.comboboxState.value===0);return ne({container:x(()=>y(e.optionsRef)),enabled:x(()=>e.comboboxState.value===0),accept(i){return i.getAttribute("role")==="option"?NodeFilter.FILTER_REJECT:i.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(i){i.setAttribute("role","none")}}),()=>{var i,b,m;let g={open:e.comboboxState.value===0},T={"aria-labelledby":(m=(i=y(e.labelRef))==null?void 0:i.id)!=null?m:(b=y(e.buttonRef))==null?void 0:b.id,id:t,ref:e.optionsRef,role:"listbox","aria-multiselectable":e.mode.value===1?!0:void 0},V=z(o,["hold"]);return L({ourProps:T,theirProps:V,slot:g,attrs:O,slots:S,features:U.RenderStrategy|U.Static,visible:I.value,name:"ComboboxOptions"})}}}),we=N({name:"ComboboxOption",props:{as:{type:[Object,String],default:"li"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1}},setup(o,{slots:O,attrs:S,expose:P}){let e=j("ComboboxOption"),t=`headlessui-combobox-option-${K()}`,v=D(null);P({el:v,$el:v});let I=x(()=>e.activeOptionIndex.value!==null?e.options.value[e.activeOptionIndex.value].id===t:!1),i=x(()=>k(e.mode.value,{[0]:()=>e.compare(f(e.value.value),f(o.value)),[1]:()=>f(e.value.value).some(n=>e.compare(f(n),f(o.value)))})),b=x(()=>({disabled:o.disabled,value:o.value,domRef:v}));J(()=>e.registerOption(t,b)),ve(()=>e.unregisterOption(t)),q(()=>{e.comboboxState.value===0&&I.value&&e.activationTrigger.value!==0&&A(()=>{var n,s;return(s=(n=y(v))==null?void 0:n.scrollIntoView)==null?void 0:s.call(n,{block:"nearest"})})});function m(n){if(o.disabled)return n.preventDefault();e.selectOption(t),e.mode.value===0&&e.closeCombobox(),re()||requestAnimationFrame(()=>{var s;return(s=y(e.inputRef))==null?void 0:s.focus()})}function g(){if(o.disabled)return e.goToOption(C.Nothing);e.goToOption(C.Specific,t)}let T=ae();function V(n){T.update(n)}function B(n){T.wasMoved(n)&&(o.disabled||I.value||e.goToOption(C.Specific,t,0))}function a(n){T.wasMoved(n)&&(o.disabled||I.value&&(e.optionsPropsRef.value.hold||e.goToOption(C.Nothing)))}return()=>{let{disabled:n}=o,s={active:I.value,selected:i.value,disabled:n},h={id:t,ref:v,role:"option",tabIndex:n===!0?void 0:-1,"aria-disabled":n===!0?!0:void 0,"aria-selected":i.value,disabled:void 0,onClick:m,onFocus:g,onPointerenter:V,onMouseenter:V,onPointermove:B,onMousemove:B,onPointerleave:a,onMouseleave:a};return L({ourProps:h,theirProps:o,slot:s,attrs:S,slots:O,name:"ComboboxOption"})}}});export{Te as $,he as H,Pe as K,Ie as N,Ve as U,we as _};
