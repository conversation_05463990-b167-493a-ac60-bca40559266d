import{_ as o}from"./_plugin-vue_export-helper-c27b6911.js";import{r as n,o as r,b as i,d as t,e as l,w as a,h as c}from"./index-0d8d3833.js";const m={},d={class:"grid min-h-full bg-gray-50 place-items-center py-24 px-6 sm:py-32 lg:px-8"},u={class:"text-center"},x={class:"mt-10 flex items-center justify-center gap-x-6"};function f(p,e){const s=n("router-link");return r(),i("main",d,[t("div",u,[e[1]||(e[1]=t("p",{class:"text-base font-semibold text-main-color-600"},"500",-1)),e[2]||(e[2]=t("h1",{class:"mt-4 text-3xl font-bold tracking-tight text-gray-900 sm:text-5xl"},"Chyba serveru",-1)),e[3]||(e[3]=t("p",{class:"mt-6 text-base leading-7 text-gray-600"},"Omlouváme se, ale došlo k interní chybě serveru. Pokud chyba přetrvává, kontaktujte svého správce licence.",-1)),t("div",x,[l(s,{to:{name:"users"},href:"#",class:"rounded-md bg-main-color-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-main-color-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-main-color-600"},{default:a(()=>e[0]||(e[0]=[c("Zpět na výpis")])),_:1})])])])}const g=o(m,[["render",f]]);export{g as default};
