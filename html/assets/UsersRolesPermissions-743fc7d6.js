import{A as M,a as d,D as Z,m as _,r as P,o,b as a,e as n,w as g,d as e,t as p,l as v,u as r,H as q,F as f,f as w,g as A,x as R,y as S,E as G,a6 as I,z as H}from"./index-9d9f1067.js";import{_ as J}from"./AppTopbar-f7fb50ba.js";import{L as j,b as K}from"./index-0c6a7c95.js";import{c as h}from"./checkPermission.service-d7c9bc43.js";import{_ as O}from"./_plugin-vue_export-helper-c27b6911.js";import"./index-adce43bb.js";const Q={class:"md:flex justify-between items-center"},T={class:"pb-6 pl-2 flex items-center gap-6"},W={class:"text-xl"},X={key:0},Y={key:1},ee={key:2},se={key:0,class:"flex gap-2 items-center"},te={class:"bg-main-color-100 rounded-full p-1"},oe={class:"mb-6 sm:border sm:border-zinc-200/70 rounded-md sm:flex gap-x-0.5 sm:bg-zinc-200/70 space-y-2 sm:space-y-0"},ae={class:"flex items-center"},re={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ne={class:"bg-white border border-zinc-200/70 rounded-md p-5 pb-8"},le={key:0},ie={class:"flex justify-between items-center"},de={class:"flex items-center"},ce={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600"},me={class:"relative flex gap-x-3"},ue={class:"flex h-6 items-center"},pe={class:"text-sm leading-6"},ve=["for"],be={key:1,class:"h-40 flex items-center"},_e={class:"bg-white border border-zinc-200/70 rounded-md pt-5 pb-8"},xe={key:0},ge={class:"flex justify-between items-center px-5 pb-0"},fe={class:"flex items-center"},he={class:"p-5 pt-0"},ye={class:"text-sm font-semibold leading-6 text-main-color"},ke={class:"relative flex gap-x-3"},we={class:"flex h-6 items-center"},ze={class:"text-sm leading-6"},Ce=["for"],Ue={key:1,class:"h-40 flex items-center"},Ve={__name:"UsersRolesPermissions",setup(Pe){const b=M(),x=d(!0),i=d({}),z=d({}),C=d({}),m=d([]),u=d([]),U=d([]),$=d(["skolasys-root","users-roles-permissions"]);Z(()=>{x.value=!0,y(),F(),B(),x.value=!1});function y(){_.get("/api/users/"+b.params.id+"/roles-permissions").then(s=>{i.value=s.data.data,m.value=s.data.data.roles,u.value=s.data.data.direct_permissions,U.value=s.data.data.role_permissions}).catch(s=>{console.log(s)})}function B(){_.get("/api/permission-categories/permissions").then(s=>{C.value=s.data}).catch(s=>{console.log(s)})}async function F(){await _.get("/api/roles?page=1&perpage=9999").then(s=>{z.value=s.data.data}).catch(s=>{console.log(s)})}function L(){m.value.length||(m.value=[null]),_.post("/api/users/"+b.params.id+"/roles",{roles:m.value}).then(s=>{y(),R.success(s.data.message)}).catch(s=>{console.log(s)})}function N(){u.value.length||(u.value=[null]),_.post("/api/users/"+b.params.id+"/permissions",{permissions:u.value}).then(s=>{y(),R.success(s.data.message)}).catch(s=>{console.log(s)})}function D(s){return!!U.value.includes(s)}return(s,t)=>{const E=P("ShieldCheckIcon"),V=P("router-link");return o(),a(f,null,[n(J,{breadCrumbs:$.value},{topbarButtons:g(()=>[e("button",{onClick:t[0]||(t[0]=c=>s.$router.go(-1)),type:"button",class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200 flex items-center"},"Zpět ")]),_:1},8,["breadCrumbs"]),e("div",Q,[e("div",T,[e("h2",W,[i.value.first_name?(o(),a("span",X,p(i.value.first_name+" "),1)):v("",!0),i.value.midle_name?(o(),a("span",Y,p(i.value.midle_name+" "),1)):v("",!0),i.value.last_name?(o(),a("span",ee,p(i.value.last_name),1)):v("",!0)]),i.value.active_directory==0?(o(),a("div",se,[e("span",te,[n(E,{class:"h-4 w-4 text-main-color-600","aria-hidden":"true"})]),t[5]||(t[5]=e("span",{class:"text-xs text-main-color-600"},"Lokální účet",-1))])):v("",!0)]),e("div",oe,[n(V,{class:"flex rounded-md sm:rounded-r-none sm:rounded-t-none sm:rounded-b-none sm:rounded-l-md px-8 h-10 text-center text-sm font-medium text-gray-900 bg-white border-b-2 border-zinc-200 sm:border-white hover:bg-gray-50 hover:border-gray-50","exact-active-class":"!border-main-color-600",to:{name:"users-edit",params:{id:r(b).params.id}}},{default:g(()=>t[6]||(t[6]=[e("span",{class:"flex items-center"},"Základní informace ",-1)])),_:1},8,["to"]),n(V,{class:"flex rounded-md sm:rounded-l-none sm:rounded-t-none sm:rounded-b-none sm:rounded-r-md px-8 h-10 text-center text-sm font-medium text-gray-900 bg-white border-b-2 border-zinc-200 sm:border-white hover:bg-gray-50 hover:border-gray-50","exact-active-class":"!border-main-color-600",to:{name:"users-roles-permissions",params:{id:r(b).params.id}}},{default:g(()=>[e("span",ae,[n(r(j),{class:"h-4"}),t[7]||(t[7]=e("p",{class:"ml-2"},"Role a oprávnění",-1))])]),_:1},8,["to"])])]),e("div",re,[n(r(H),{onSubmit:t[2]||(t[2]=c=>L())},{default:g(({values:c})=>[e("div",ne,[x.value?(o(),a("div",be,[n(r(I),{class:"mx-auto text-spinner-color",size:"40"})])):(o(),a("div",le,[e("div",ie,[e("div",de,[n(r(K),{class:"w-7"}),t[8]||(t[8]=e("p",{class:"ml-4 text-lg text-gray-900"},"Zařazení do rolí",-1))]),e("div",null,[r(h).check("users.update_roles")&&r(h).check("roles.edit")?(o(),a("button",ce,"Uložit")):v("",!0)])]),(o(!0),a(f,null,w(z.value,l=>(o(),a("div",{class:"mt-4 space-y-5 ml-1.5",key:l.id},[e("div",me,[e("div",ue,[n(r(S),{rules:"requiredCheckbox",id:"role-"+l.id,name:"roles",type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-main-color-600 cursor-pointer",modelValue:m.value,"onUpdate:modelValue":t[1]||(t[1]=k=>m.value=k),value:l.name},null,8,["id","modelValue","value"])]),e("div",pe,[e("label",{for:"role-"+l.id,class:"font-medium text-gray-900 cursor-pointer"},p(l.name),9,ve)])])]))),128)),n(r(G),{name:"roles",class:"text-rose-400 text-sm block pt-1"})]))])]),_:1}),e("div",null,[e("div",_e,[x.value?(o(),a("div",Ue,[n(r(I),{class:"mx-auto text-spinner-color",size:"40"})])):(o(),a("div",xe,[e("div",ge,[e("div",fe,[n(r(j),{class:"w-7"}),t[9]||(t[9]=e("p",{class:"ml-4 text-lg text-gray-900"},"Individuální oprávnění",-1))]),e("div",null,[r(h).check("users.update_permissions")&&r(h).check("permissions.edit")?(o(),a("button",{key:0,onClick:t[3]||(t[3]=q(c=>N(),["prevent"])),class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600"},"Uložit")):v("",!0)])]),e("fieldset",he,[(o(!0),a(f,null,w(C.value,c=>(o(),a("div",{class:"mb-6 mt-4",key:c.id},[e("legend",ye,p(c.name),1),(o(!0),a(f,null,w(c.permissions,l=>(o(),a("div",{class:"mt-4 space-y-5 ml-3",key:l.id},[e("div",ke,[e("div",we,[n(r(S),{id:"permission-"+l.id,name:"",type:"checkbox",class:"relative h-4 w-4 rounded cursor-pointer border-gray-300 text-main-color-600 focus:ring-main-color-600 disabled:bg-main-color-300 disabled:border-main-color-300 disabled:text-main-color-300",modelValue:u.value,"onUpdate:modelValue":t[4]||(t[4]=k=>u.value=k),value:l.name,disabled:D(l.name)},null,8,["id","modelValue","value","disabled"])]),e("div",ze,[e("label",{for:"permission-"+l.id,class:"font-medium text-gray-900 cursor-pointer"},p(l.human_name),9,Ce)])])]))),128))]))),128))]),t[10]||(t[10]=A('<div class="p-5 pb-0 border-t border-zinc-200/70" data-v-8721a516><div class="relative flex gap-x-3" data-v-8721a516><div class="flex h-6 items-center" data-v-8721a516><input id="" name="" type="checkbox" disabled class="relative h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-main-color-600 disabled:bg-main-color-300 disabled:border-main-color-300 disabled:text-main-color-300" data-v-8721a516></div><div class="text-sm leading-6" data-v-8721a516><label for="" class="font-normal text-gray-900" data-v-8721a516>Položky označené tímto znakem nelze měnit, jelikož přebírají nastaven z přidělené role.</label></div></div></div>',1))]))])])])],64)}}},Fe=O(Ve,[["__scopeId","data-v-8721a516"]]);export{Fe as default};
