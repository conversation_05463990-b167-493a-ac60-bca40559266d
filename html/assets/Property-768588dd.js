import{a as i,j as B,B as ae,o as t,c as R,w as r,e as a,h as K,d as e,u as o,x as de,s as U,E as Y,b as s,t as v,P as L,C,k as G,q as ne,M as be,v as w,O as fe,r as ke,z as re,R as we,S as $e,F as Z,f as q,T as W,n as X,_ as Ie}from"./index-bfe6943f.js";import{_ as Ce}from"./AppTopbar-1fff46f6.js";import{l as N,J as ce,m as ue,i as me,a0 as pe,g as ze,h as Me,k as Q,$ as Se,s as Ve}from"./index-5b4677b6.js";import{_ as De}from"./pagination-7270df9d.js";import{c as z}from"./checkPermission.service-4ccc1117.js";import{_ as F}from"./basicModal-efb13c60.js";import{S as _e}from"./vue-tailwind-datepicker-2621e104.js";/* empty css             */import{S as H}from"./transition-78618ab0.js";import{S as ee,b as te,M as se,g as oe}from"./menu-40a0975a.js";import"./listbox-d7028c3f.js";import"./hidden-bb6f5784.js";import"./use-tracked-pointer-c6b2df1d.js";import"./use-resolve-button-type-122dc2ec.js";import"./use-controllable-21042f24.js";import"./dialog-abb0eee4.js";import"./use-tree-walker-d2a209d4.js";const Ne="/assets/barcodeIcon-2fc825e0.svg",Re="/assets/barcodeIconWhite-4a7321ad.svg",je={class:"p-6 grid grid-cols-2 gap-4"},Ue=e("label",{for:"item-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Název položky:",-1),Ee={class:"mt-2"},Oe=e("label",{for:"item-count",class:"block text-sm font-normal leading-6 text-gray-900"},"Počet položek:",-1),Pe={class:"mt-2"},Ye=e("label",{for:"item-price",class:"block text-sm font-normal leading-6 text-gray-900"},"Cena za kus:",-1),Ge={class:"mt-2"},Te=e("label",{for:"item-invoice",class:"block text-sm font-normal leading-6 text-gray-900"},"Číslo faktury:",-1),Ae={class:"mt-2"},Ze=e("label",{for:"item-buyed-at",class:"block text-sm font-normal leading-6 text-gray-900"},"Datum zakoupení:",-1),Be={class:"mt-2"},qe={class:"rounded-l-full"},Ke={class:"rounded-md border border-gray-300 block font-normal px-2.5 py-1.5 w-full text-sm leading-6 items-center justify-center space-x-2 sm:space-x-4 transition ease-out duration-300"},Le={class:"flex items-center w-full"},Fe={class:"w-6 h-6"},He={class:"flex-1"},Je={key:0,class:"text-gray-900"},We={key:1,class:"text-gray-400"},Xe=e("span",null,"Zvolte datum zakoupení...",-1),Qe=[Xe],et={class:"border-t p-5"},tt={class:"text-right space-x-3"},st=e("button",{type:"submit",class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600"}," Vytvořit ",-1),ot={__name:"createItemModal",emits:["reloadItems"],setup(P,{expose:j,emit:E}){const l=i(!1);B("debugModeGlobalVar");const b=i(null),x=i(null),k=i(null),g=i(""),c=i(null),V=i(!1),$=i({date:"YYYY-MM-DD",month:"MM"});ae(()=>{});function I(){l.value=!1}function M(){l.value=!0,b.value="",x.value=null,k.value=null,g.value="",c.value=""}async function m(){z.check("items.create")||z.check("property.master")?await G.post("api/items",{name:b.value,count:x.value,price:c.value,invoice_number:k.value,buyed_at:g.value}).then(p=>{ne.success(p.data.message)}).catch(p=>{console.log(p)}):V.value=!1,I(),E("reloadItems",!0)}return j({openModal:M}),(p,_)=>(t(),R(o(H),{appear:"",show:l.value,as:"template",onClose:_[8]||(_[8]=y=>I())},{default:r(()=>[a(F,{size:"md"},{"modal-title":r(()=>[K("Vytvoření nové položky")]),"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:_[0]||(_[0]=y=>I())},[a(o(N),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[a(o(de),{onSubmit:_[7]||(_[7]=y=>m())},{default:r(({values:y})=>[e("div",je,[e("div",null,[Ue,e("div",Ee,[a(o(U),{rules:"required",modelValue:b.value,"onUpdate:modelValue":_[1]||(_[1]=h=>b.value=h),type:"text",name:"item-name",id:"item-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte název položky..."},null,8,["modelValue"]),a(o(Y),{name:"item-name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[Oe,e("div",Pe,[a(o(U),{rules:"required|minMax:1,99999",modelValue:x.value,"onUpdate:modelValue":_[2]||(_[2]=h=>x.value=h),type:"number",name:"item-count",id:"item-count",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte počet položek..."},null,8,["modelValue"]),a(o(Y),{name:"item-count",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[Ye,e("div",Ge,[a(o(U),{rules:"minMax:1,9999999999",modelValue:c.value,"onUpdate:modelValue":_[3]||(_[3]=h=>c.value=h),type:"number",name:"item-price",id:"item-price",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte cenu za kus..."},null,8,["modelValue"]),a(o(Y),{name:"item-price",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[Te,e("div",Ae,[a(o(U),{modelValue:k.value,"onUpdate:modelValue":_[4]||(_[4]=h=>k.value=h),type:"text",name:"item-invoice",id:"item-invoice",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte číslo faktury..."},null,8,["modelValue"]),a(o(Y),{name:"item-invoice",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[Ze,e("div",Be,[a(o(U),{name:"blockedTimetableDate"},{default:r(({handleChange:h,value:S})=>[a(o(_e),{name:"blockedTimetableDate",i18n:"cs","as-single":"",shortcuts:!1,modelValue:g.value,"onUpdate:modelValue":[_[5]||(_[5]=f=>g.value=f),h],formatter:$.value,placeholder:"Zvolte datum zakoupení..."},{default:r(({clear:f})=>[e("div",null,[e("div",qe,[e("button",Ke,[e("div",Le,[e("div",Fe,[g.value?(t(),R(o(N),{key:0,onClick:f,class:"h-6 w-6 text-gray-900","aria-hidden":"true"},null,8,["onClick"])):(t(),R(o(ce),{key:1,class:"h-6 w-6 text-gray-900","aria-hidden":"true"}))]),e("div",He,[g.value?(t(),s("span",Je,[e("span",null,v(o(L)(g.value).format("DD.MM.YYYY")),1)])):(t(),s("span",We,Qe))])])])])])]),_:2},1032,["modelValue","onUpdate:modelValue","formatter"])]),_:1})])])]),e("div",et,[e("div",tt,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:_[6]||(_[6]=C(h=>I(),["prevent"]))}," Zavřít "),st])])]),_:1})]),_:1})]),_:1},8,["show"]))}},at=e("div",{class:"p-6 gap-4"},[e("p",null,"Opravdu si přejete položku smazat?")],-1),nt={class:"border-t p-5"},lt={class:"text-right space-x-3"},it={__name:"deleteItemModal",props:{selectedItem:{type:Object,required:!0}},emits:["reloadItems"],setup(P,{expose:j,emit:E}){const l=P,b=i(!1);B("debugModeGlobalVar");const x=i(null),k=i(""),g=i(!1);ae(()=>{});function c(){b.value=!1}function V(){b.value=!0,x.value="",k.value=null}async function $(){z.check("items.delete")||z.check("property.master")?(k.value=parseInt(k.value),await G.post("api/items/"+l.selectedItem.id+"/delete").then(I=>{ne.success(I.data.message)}).catch(I=>{console.log(I)})):g.value=!1,c(),E("reloadItems",!0)}return j({openModal:V}),(I,M)=>(t(),R(o(H),{appear:"",show:b.value,as:"template",onClose:M[3]||(M[3]=m=>c())},{default:r(()=>[a(F,{size:"sm"},{"modal-title":r(()=>[K("Smazat položku")]),"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:M[0]||(M[0]=m=>c())},[a(o(N),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[at,e("div",nt,[e("div",lt,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:M[1]||(M[1]=C(m=>c(),["prevent"]))}," Zavřít "),e("button",{onClick:M[2]||(M[2]=C(m=>$(),["prevent"])),class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-red-600",type:"submit"}," Smazat ")])])]),_:1})]),_:1},8,["show"]))}},rt={class:"p-6 grid grid-cols-2 gap-4"},dt=e("label",{for:"item-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Název položky:",-1),ct={class:"mt-2"},ut=e("label",{for:"item-price",class:"block text-sm font-normal leading-6 text-gray-900"},"Cena za kus:",-1),mt={class:"mt-2"},pt=e("label",{for:"item-invoice",class:"block text-sm font-normal leading-6 text-gray-900"},"Číslo faktury:",-1),_t={class:"mt-2"},vt=e("label",{for:"item-buyed-at",class:"block text-sm font-normal leading-6 text-gray-900"},"Datum zakoupení:",-1),ht={class:"mt-2"},xt={class:"rounded-l-full"},gt={class:"rounded-md border border-gray-300 block font-normal px-2.5 py-1.5 w-full text-sm leading-6 items-center justify-center space-x-2 sm:space-x-4 transition ease-out duration-300"},yt={class:"flex items-center w-full"},bt={class:"w-6 h-6"},ft={class:"flex-1"},kt={key:0,class:"text-gray-900"},wt={key:1,class:"text-gray-400"},$t=e("span",null,"Zvolte datum zakoupení...",-1),It=[$t],Ct={class:"border-t p-5"},zt={class:"text-right space-x-3"},Mt=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Upravit ",-1),St={__name:"editItemModal",props:{selectedItem:{type:Object,required:!0}},emits:["reloadItems"],setup(P,{expose:j,emit:E}){const l=P,b=i(!1);B("debugModeGlobalVar");const x=i(""),k=i(null),g=i(""),c=i(null),V=i(!1);be(()=>l.selectedItem,(m,p)=>{x.value=m.name,k.value=m.invoice_number,g.value=m.buyed_at||"",c.value=m.price});function $(){b.value=!1}async function I(){b.value=!0}async function M(){z.check("items.edit")||z.check("property.master")?await G.post("api/items/"+l.selectedItem.id+"/update",{name:x.value,price:c.value,invoice_number:k.value,buyed_at:g.value}).then(m=>{ne.success(m.data.message)}).catch(m=>{console.log(m)}):V.value=!1,$(),E("reloadItems",!0)}return j({openModal:I}),(m,p)=>(t(),R(o(H),{appear:"",show:b.value,as:"template",onClose:p[7]||(p[7]=_=>$())},{default:r(()=>[a(F,{size:"md"},{"modal-title":r(()=>[K("Úprava položky")]),"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:p[0]||(p[0]=_=>$())},[a(o(N),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[a(o(de),{onSubmit:p[6]||(p[6]=_=>M())},{default:r(({values:_})=>[e("div",rt,[e("div",null,[dt,e("div",ct,[a(o(U),{rules:"required",modelValue:x.value,"onUpdate:modelValue":p[1]||(p[1]=y=>x.value=y),type:"text",name:"item-name",id:"item-name",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte název skupiny..."},null,8,["modelValue"]),a(o(Y),{name:"item-name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[ut,e("div",mt,[a(o(U),{rules:"minMax:1,9999999999",modelValue:c.value,"onUpdate:modelValue":p[2]||(p[2]=y=>c.value=y),type:"number",name:"item-price",id:"item-price",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte cenu za kus..."},null,8,["modelValue"]),a(o(Y),{name:"item-price",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[pt,e("div",_t,[a(o(U),{modelValue:k.value,"onUpdate:modelValue":p[3]||(p[3]=y=>k.value=y),type:"text",name:"item-invoice",id:"item-invoice",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte číslo faktury..."},null,8,["modelValue"]),a(o(Y),{name:"item-invoice",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[vt,e("div",ht,[a(o(U),{name:"blockedTimetableDate"},{default:r(({handleChange:y,value:h})=>[a(o(_e),{name:"blockedTimetableDate",i18n:"cs","as-single":"",shortcuts:!1,modelValue:g.value,"onUpdate:modelValue":[p[4]||(p[4]=S=>g.value=S),y],formatter:m.formatter,placeholder:"Zvolte datum zakoupení..."},{default:r(({clear:S})=>[e("div",null,[e("div",xt,[e("button",gt,[e("div",yt,[e("div",bt,[g.value?(t(),R(o(N),{key:0,onClick:S,class:"h-6 w-6 text-gray-900","aria-hidden":"true"},null,8,["onClick"])):(t(),R(o(ce),{key:1,class:"h-6 w-6 text-gray-900","aria-hidden":"true"}))]),e("div",ft,[g.value?(t(),s("span",kt,[e("span",null,v(o(L)(g.value).format("DD.MM.YYYY")),1)])):(t(),s("span",wt,It))])])])])])]),_:2},1032,["modelValue","onUpdate:modelValue","formatter"])]),_:1})])])]),e("div",Ct,[e("div",zt,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:p[5]||(p[5]=C(y=>$(),["prevent"]))}," Zavřít "),Mt])])]),_:1})]),_:1})]),_:1},8,["show"]))}},Vt={class:"p-6"},Dt={class:"grid grid-cols-3 gap-x-5 gap-y-4"},Nt=e("span",{class:"block text-xs text-gray-400"},"Evidenční číslo:",-1),Rt={key:0},jt={key:1,class:"text-red-600"},Ut=e("span",{class:"block text-xs text-gray-400"},"Název položky:",-1),Et={key:0},Ot={key:1,class:"text-red-600"},Pt=e("span",{class:"block text-xs text-gray-400 pb-1"},"Stav:",-1),Yt={key:0,class:"border border-green-600 px-2 py-1 rounded-md text-xs text-green-600 inline-block"},Gt={class:"flex"},Tt=e("span",null,"Nepřiřazeno",-1),At={key:1,class:"border border-red-600 px-2 py-1 rounded-md text-xs text-red-600 inline-block"},Zt=e("div",null,"Přiřazeno uživateli",-1),Bt=[Zt],qt={key:2,class:"border border-red-600 px-2 py-1 rounded-md text-xs text-red-600 inline-block"},Kt=e("div",null,"Přiřazeno místnosti",-1),Lt=[Kt],Ft={key:3,class:"border border-amber-600 px-2 py-1 rounded-md text-xs text-amber-600 inline-block"},Ht={class:"flex"},Jt=e("span",null,"Uzamčeno",-1),Wt={key:4,class:"border border-amber-600 px-2 py-1 rounded-md text-xs text-amber-600 inline-block"},Xt={class:"flex"},Qt=e("span",null,"Vyřazeno",-1),es=e("span",{class:"block text-xs text-gray-400"},"Cena za ks:",-1),ts={key:0},ss={key:1},os=e("span",{class:"block text-xs text-gray-400"},"Číslo faktury:",-1),as={key:0},ns={key:1},ls=e("span",{class:"block text-xs text-gray-400"},"Datum zakoupení:",-1),is={key:0},rs={key:1},ds={key:0,class:"mt-6 space-y-6"},cs={key:0},us={class:"flex items-center gap-2"},ms=e("span",null,"Informace o přiřazené místnosti",-1),ps={class:"mt-4 flex gap-10"},_s=e("span",{class:"block text-xs text-gray-400"},"Kód místnosti:",-1),vs={key:0},hs={key:1},xs=e("span",{class:"block text-xs text-gray-400"},"Název místnosti:",-1),gs={key:0},ys={key:1},bs={key:1},fs={class:"flex items-center gap-2"},ks=e("span",null,"Informace o přiřazeném uživateli",-1),ws={class:"mt-4 flex gap-10"},$s=e("span",{class:"block text-xs text-gray-400"},"Jméno uživatele:",-1),Is={key:0},Cs={key:1},zs=e("span",{class:"block text-xs text-gray-400"},"E-mail uživatele:",-1),Ms={key:0},Ss={key:1},Vs={key:0},Ds=e("span",{class:"block text-xs text-gray-400"},"Organizace:",-1),Ns={key:0},Rs={key:1},js={class:"border-t p-5"},Us={class:"text-right space-x-3"},Es={__name:"itemDetailModal",props:{selectedItem:{type:Object,required:!0}},emits:["reloadGroups"],setup(P,{expose:j,emit:E}){const l=P,b=i(!1);B("debugModeGlobalVar"),i(!1);function x(){b.value=!1}function k(){b.value=!0}return j({openModal:k}),(g,c)=>(t(),R(o(H),{appear:"",show:b.value,as:"template",onClose:c[2]||(c[2]=V=>x())},{default:r(()=>[a(F,{size:"xs"},{"modal-title":r(()=>[K("Detail položky")]),"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:c[0]||(c[0]=V=>x())},[a(o(N),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[e("div",null,[e("div",Vt,[e("div",Dt,[e("div",null,[Nt,l.selectedItem.evidence_number?(t(),s("span",Rt,v(l.selectedItem.evidence_number),1)):(t(),s("span",jt,"CHYBÍ"))]),e("div",null,[Ut,l.selectedItem.name?(t(),s("span",Et,v(l.selectedItem.name),1)):(t(),s("span",Ot,"CHYBÍ"))]),e("div",null,[Pt,l.selectedItem.state=="NOT_ASSIGNED"?(t(),s("div",Yt,[e("div",Gt,[a(o(ue),{class:"h-4 w-4 text-green-600 mr-1","aria-hidden":"true"}),Tt])])):l.selectedItem.state=="ASSIGNED_TO_USER"?(t(),s("div",At,Bt)):l.selectedItem.state=="ASSIGNED_TO_ROOM"?(t(),s("div",qt,Lt)):l.selectedItem.state=="LOCKED"?(t(),s("div",Ft,[e("div",Ht,[a(o(me),{class:"h-4 w-4 text-amber-600 mr-1","aria-hidden":"true"}),Jt])])):l.selectedItem.state=="DISCARDED"?(t(),s("div",Wt,[e("div",Xt,[a(o(pe),{class:"h-4 w-4 text-amber-600 mr-1","aria-hidden":"true"}),Qt])])):w("",!0)]),e("div",null,[es,l.selectedItem.evidence_number?(t(),s("span",ts,v(l.selectedItem.price),1)):(t(),s("span",ss,"-"))]),e("div",null,[os,l.selectedItem.evidence_number?(t(),s("span",as,v(l.selectedItem.invoice_number),1)):(t(),s("span",ns,"-"))]),e("div",null,[ls,l.selectedItem.buyed_at?(t(),s("span",is,v(o(L)(l.selectedItem.buyed_at).format("DD. MM. YYYY")),1)):(t(),s("span",rs,"-"))])]),l.selectedItem.user||l.selectedItem.room?(t(),s("div",ds,[l.selectedItem.room?(t(),s("div",cs,[e("div",us,[a(o(ze),{class:"h-6 w-6","aria-hidden":"true"}),ms]),e("div",ps,[e("div",null,[_s,l.selectedItem.room.code?(t(),s("span",vs,v(l.selectedItem.room.code),1)):(t(),s("span",hs,"-"))]),e("div",null,[xs,l.selectedItem.room.name?(t(),s("span",gs,v(l.selectedItem.room.name),1)):(t(),s("span",ys,"-"))])])])):w("",!0),l.selectedItem.user?(t(),s("div",bs,[e("div",fs,[a(o(Me),{class:"h-6 w-6","aria-hidden":"true"}),ks]),e("div",ws,[e("div",null,[$s,l.selectedItem.user.full_name?(t(),s("span",Is,v(l.selectedItem.user.full_name),1)):(t(),s("span",Cs,"-"))]),e("div",null,[zs,l.selectedItem.user.email?(t(),s("span",Ms,v(l.selectedItem.user.email),1)):(t(),s("span",Ss,"-"))]),l.selectedItem.user.organization_unit?(t(),s("div",Vs,[Ds,l.selectedItem.user.organization_unit.name?(t(),s("span",Ns,v(l.selectedItem.user.organization_unit.name),1)):(t(),s("span",Rs,"-"))])):w("",!0)])])):w("",!0)])):w("",!0)]),e("div",js,[e("div",Us,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:c[1]||(c[1]=C(V=>x(),["prevent"]))}," Zavřít ")])])])]),_:1})]),_:1},8,["show"]))}},Os={class:"space-y-6"},Ps={class:"px-0"},Ys={class:"bg-white border border-zinc-200/70 rounded-md p-5"},Gs={class:"sm:flex justify-between items-center gap-x-20 gap-y-4"},Ts={class:"grid grid-cols-6 grow gap-4"},As={class:"col-span-6"},Zs={class:"col-span-2"},Bs={class:"w-full text-left"},qs={key:0,class:"text-gray-900"},Ks={key:1,class:"text-gray-400"},Ls={key:0,class:"flex ml-4"},Fs=["onClick"],Hs={class:"col-span-2"},Js={class:"w-full text-left"},Ws={key:0,class:"text-gray-900"},Xs={key:1,class:"text-gray-400"},Qs={key:0,class:"flex ml-4"},eo=["onClick"],to={class:"col-span-2"},so={class:"w-full text-left"},oo={key:0,class:"text-gray-900"},ao={key:1,class:"text-gray-400"},no={key:0,class:"flex ml-4"},lo=["onClick"],io={class:"flex items-center gap-4"},ro=e("span",null,"Resetovat",-1),co=[ro],uo={class:"flow-root bg-white border border-zinc-200/70 rounded-md overflow-hidden"},mo={class:"sm:-mx-6 lg:-mx-8"},po={class:"inline-block overflow-x-auto w-full align-middle sm:px-6 lg:px-8"},_o={key:0,class:"min-w-full divide-y divide-gray-200"},vo={scope:"col",class:"py-4 pl-5 pr-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md w-16"},ho=["checked"],xo=e("th",{scope:"col",class:"py-4 pl-5 pr-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md whitespace-nowrap"}," Ev. číslo ",-1),go=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 whitespace-nowrap"}," Číslo faktury ",-1),yo=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 whitespace-nowrap"}," Název položky ",-1),bo=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 whitespace-nowrap"}," Cena za kus ",-1),fo=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 whitespace-nowrap"}," Datum zakoupení ",-1),ko=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 whitespace-nowrap"}," Přiřazená třída ",-1),wo=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 whitespace-nowrap"}," Přiřazený uživatel ",-1),$o=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"},"Stav ",-1),Io=e("th",{scope:"col",class:"pl-3 pr-5 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"},null,-1),Co={key:0,class:"divide-y divide-gray-200"},zo={class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600"},Mo={class:"flex items-center gap-3"},So=["value"],Vo=["onClick"],Do=e("img",{class:"h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 rounded-lg p-1.5",src:Ne,"aria-hidden":"true",alt:"Generovat carovy kod"},null,-1),No=[Do],Ro={class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600"},jo={key:0},Uo={key:1},Eo={class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600"},Oo={key:0},Po={key:1},Yo={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},Go={key:0},To={key:1},Ao={class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600"},Zo={key:0},Bo={key:1},qo={class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600"},Ko={key:0},Lo={key:1},Fo={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},Ho={key:0},Jo={key:1},Wo={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},Xo={key:0},Qo={key:1},ea={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},ta={key:0},sa={key:0,class:"border border-green-600 px-2 py-1 rounded-md text-xs text-green-600 inline-block"},oa={class:"flex"},aa=e("span",null,"Nepřiřazeno",-1),na={key:1,class:"border border-red-600 px-2 py-1 rounded-md text-xs text-red-600 inline-block"},la=e("div",null,"Přiřazeno uživateli",-1),ia=[la],ra={key:2,class:"border border-red-600 px-2 py-1 rounded-md text-xs text-red-600 inline-block"},da=e("div",null,"Přiřazeno místnosti",-1),ca=[da],ua={key:3,class:"border border-amber-600 px-2 py-1 rounded-md text-xs text-amber-600 inline-block"},ma={class:"flex"},pa=e("span",null,"Uzamčeno",-1),_a={key:4,class:"border border-amber-600 px-2 py-1 rounded-md text-xs text-amber-600 inline-block"},va={class:"flex"},ha=e("span",null,"Vyřazeno",-1),xa={class:"flex items-center justify-end py-4 pl-3 pr-5"},ga=["onClick"],ya=["onClick"],ba=["onClick"],fa={key:1},ka=e("tr",null,[e("td",{colspan:"10",class:"text-center py-10 text-gray-600 text-sm"},"Nebyly nalezeny žádné položky.")],-1),wa=[ka],$a={key:2,class:"bg-gray-100/70"},Ia={colspan:"10",class:"py-4 pl-5 px-3 text-sm text-gray-900 bg-gray-100/70"},Ca={class:"flex items-center gap-2"},za=e("img",{class:"h-5 w-6",src:Re,"aria-hidden":"true",alt:"Generovat carovy kod"},null,-1),Ma=e("span",null,"vytisknout hromadně",-1),Sa=[za,Ma],Va=e("span",null,"Zrušit výběr",-1),Da=[Va],Ha={__name:"Property",setup(P){const j=i(),E=i(),l=i(),b=i(),x=i(),k=fe();B("debugModeGlobalVar");const g=i(["property"]),c=i(!1),V=i(""),$=i(1),I=i({}),M=i([{id:"NOT_ASSIGNED",name:"Nepřiřazeno"},{id:"ASSIGNED_TO_ROOM",name:"Přiřazeno místnosti"},{id:"ASSIGNED_TO_USER",name:"Přiřazeno uživateli"},{id:"LOCKED",name:"Uzamčeno"},{id:"DISCARDED",name:"Vyřazeno"}]),m=i({id:"",name:""}),p=i({}),_=i({}),y=i({}),h=i({id:""}),S=i({id:""}),f=i([]);ae(()=>{ve(),he(),O()});async function ve(){c.value=!0,await G.get("/api/users").then(u=>{p.value=u.data.data}).catch(u=>{console.log(u)})}async function he(){c.value=!0,await G.get("/api/rooms").then(u=>{_.value=u.data.data}).catch(u=>{console.log(u)})}async function O(){c.value=!0,z.check("items.read")?await G.get("api/items?page="+$.value+"&perpage="+k.perPage+"&search="+V.value+"&state="+m.value.id+"&user_id="+h.value.id+"&room_id="+S.value.id).then(u=>{y.value=u.data.data,I.value=u.data.meta,c.value=!1}).catch(u=>{console.log(u)}):c.value=!1}function J(u){x.value=u}function xe(){f.value.length==y.value.length?f.value=[]:f.value=y.value.map(u=>u.id)}function le(){G.post("api/items/barcodes",{items:f.value},{responseType:"blob"}).then(u=>{const d=window.URL.createObjectURL(new Blob([u.data])),T=document.createElement("a");T.href=d,T.setAttribute("download","stitky.pdf"),document.body.appendChild(T),T.click(),f.value=[]}).catch(u=>{console.error("Chyba při získávání souboru:",u)})}function ge(u){$.value=u,O()}function ye(){c.value=!0,$.value=1,V.value="",m.value={id:"",name:""},h.value={id:""},S.value={id:""},f.value=[],O()}function A(){c.value=!0,$.value=1,f.value=[],O()}return(u,d)=>{const T=ke("VueSpinner");return t(),s(Z,null,[a(Ce,{breadCrumbs:g.value},{topbarButtons:r(()=>[o(z).check("items.create")||o(z).check("property.master")?(t(),s("button",{key:0,onClick:d[0]||(d[0]=C(n=>u.$refs.createItemRef.openModal(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},"Nová položka ")):w("",!0)]),_:1},8,["breadCrumbs"]),e("div",Os,[e("div",Ps,[e("div",Ys,[e("div",Gs,[e("div",Ts,[e("div",As,[re(e("input",{type:"text",name:"search",id:"search","onUpdate:modelValue":d[1]||(d[1]=n=>V.value=n),onKeyup:d[2]||(d[2]=$e(n=>A(),["enter"])),class:"block w-full rounded-md py-1.5 text-gray-900 border border-gray-300 placeholder:text-gray-400 focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte hledaný výraz..."},null,544),[[we,V.value]])]),e("div",Zs,[a(o(oe),{as:"div",class:"relative inline-block text-left w-full"},{default:r(()=>[e("div",null,[a(o(ee),{class:"inline-flex w-full justify-center items-center rounded-md bg-white px-4 py-2 text-sm font-medium text-white border border-gray-300"},{default:r(()=>[e("div",Bs,[m.value&&m.value.name?(t(),s("span",qs,v(m.value.name),1)):(t(),s("span",Ks,"Stav položky..."))]),a(o(Q),{class:"ml-2 -mr-1 h-5 w-5 text-violet-200 hover:text-violet-100","aria-hidden":"true"}),m.value&&m.value.name?(t(),s("div",Ls,[e("button",{onClick:d[3]||(d[3]=C(n=>(m.value={id:"",name:""},A()),["prevent"]))},[a(o(N),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):w("",!0)]),_:1})]),a(W,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:r(()=>[a(o(te),{class:"absolute z-10 right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:r(()=>[(t(!0),s(Z,null,q(M.value,n=>(t(),s("div",{key:n.id,class:"px-1 py-1"},[a(o(se),null,{default:r(({active:D})=>[e("button",{onClick:ie=>m.value=n,class:X([D?"bg-main-color-600 text-white":"text-gray-900","group flex w-full rounded-md px-2 py-2 text-sm text-left"])},v(n.name),11,Fs)]),_:2},1024)]))),128))]),_:1})]),_:1})]),_:1})]),e("div",Hs,[a(o(oe),{as:"div",class:"relative inline-block text-left w-full"},{default:r(()=>[e("div",null,[a(o(ee),{class:"inline-flex w-full justify-center items-center rounded-md bg-white px-4 py-2 text-sm font-medium text-white border border-gray-300"},{default:r(()=>[e("div",Js,[h.value&&h.value.first_name?(t(),s("span",Ws,v(h.value.first_name+" "+h.value.last_name),1)):(t(),s("span",Xs,"Přiřazený uživatel..."))]),a(o(Q),{class:"ml-2 -mr-1 h-5 w-5 text-violet-200 hover:text-violet-100","aria-hidden":"true"}),h.value&&h.value.first_name?(t(),s("div",Qs,[e("button",{onClick:d[4]||(d[4]=C(n=>(h.value={id:""},A()),["prevent"]))},[a(o(N),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):w("",!0)]),_:1})]),a(W,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:r(()=>[a(o(te),{class:"absolute z-10 right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:r(()=>[(t(!0),s(Z,null,q(p.value,n=>(t(),s("div",{key:n.id,class:"px-1 py-1"},[a(o(se),null,{default:r(({active:D})=>[e("button",{onClick:ie=>h.value=n,class:X([D?"bg-main-color-600 text-white":"text-gray-900","group flex w-full rounded-md px-2 py-2 text-sm text-left"])},v(n.first_name+" "+n.last_name),11,eo)]),_:2},1024)]))),128))]),_:1})]),_:1})]),_:1})]),e("div",to,[a(o(oe),{as:"div",class:"relative inline-block text-left w-full"},{default:r(()=>[e("div",null,[a(o(ee),{class:"inline-flex w-full justify-center items-center rounded-md bg-white px-4 py-2 text-sm font-medium text-white border border-gray-300"},{default:r(()=>[e("div",so,[S.value&&S.value.name?(t(),s("span",oo,v(S.value.name),1)):(t(),s("span",ao,"Přiřazený místnost..."))]),a(o(Q),{class:"ml-2 -mr-1 h-5 w-5 text-violet-200 hover:text-violet-100","aria-hidden":"true"}),S.value&&S.value.name?(t(),s("div",no,[e("button",{onClick:d[5]||(d[5]=C(n=>(S.value={id:""},A()),["prevent"]))},[a(o(N),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):w("",!0)]),_:1})]),a(W,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:r(()=>[a(o(te),{class:"absolute z-10 right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:r(()=>[(t(!0),s(Z,null,q(_.value,n=>(t(),s("div",{key:n.id,class:"px-1 py-1"},[a(o(se),null,{default:r(({active:D})=>[e("button",{onClick:ie=>S.value=n,class:X([D?"bg-main-color-600 text-white":"text-gray-900","group flex w-full rounded-md px-2 py-2 text-sm text-left"])},v(n.name),11,lo)]),_:2},1024)]))),128))]),_:1})]),_:1})]),_:1})])]),e("div",io,[e("button",{onClick:d[6]||(d[6]=n=>ye()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},co),e("button",{onClick:d[7]||(d[7]=n=>A()),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700"}," Filtrovat ")])])])]),e("div",null,[e("div",uo,[e("div",mo,[e("div",po,[c.value==!1?(t(),s("table",_o,[e("thead",null,[e("tr",null,[e("th",vo,[e("div",null,[e("input",{checked:f.value.length==y.value.length,onClick:xe,"aria-describedby":"comments-description",type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-indigo-600 ring-transparent focus:ring-transparent cursor-pointer"},null,8,ho)])]),xo,go,yo,bo,fo,ko,wo,$o,Io])]),y.value&&y.value.length?(t(),s("tbody",Co,[(t(!0),s(Z,null,q(y.value,n=>(t(),s("tr",{key:n.id},[e("td",zo,[e("div",Mo,[re(e("input",{value:n.id,"onUpdate:modelValue":d[8]||(d[8]=D=>f.value=D),id:"comments","aria-describedby":"comments-description",name:"comments",type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-transparent cursor-pointer"},null,8,So),[[Ie,f.value]]),e("button",{onClick:C(D=>(f.value=[n.id],le()),["prevent"]),class:"w-8 h-8"},No,8,Vo)])]),e("td",Ro,[n.evidence_number?(t(),s("span",jo,v(n.evidence_number),1)):(t(),s("span",Uo,"-"))]),e("td",Eo,[n.invoice_number?(t(),s("span",Oo,v(n.invoice_number),1)):(t(),s("span",Po,"-"))]),e("td",Yo,[n.name?(t(),s("span",Go,v(n.name),1)):(t(),s("span",To,"-"))]),e("td",Ao,[n.price?(t(),s("span",Zo,v(n.price)+" Kč",1)):(t(),s("span",Bo,"-"))]),e("td",qo,[n.buyed_at?(t(),s("span",Ko,v(o(L)(n.buyed_at).format("DD. MM. YYYY")),1)):(t(),s("span",Lo,"-"))]),e("td",Fo,[n.room&&n.room.name?(t(),s("span",Ho,v(n.room.name),1)):(t(),s("span",Jo,"-"))]),e("td",Wo,[n.user&&n.user.full_name?(t(),s("span",Xo,v(n.user.full_name),1)):(t(),s("span",Qo,"-"))]),e("td",ea,[n.state?(t(),s("div",ta,[n.state=="NOT_ASSIGNED"?(t(),s("div",sa,[e("div",oa,[a(o(ue),{class:"h-4 w-4 text-green-600 mr-1","aria-hidden":"true"}),aa])])):n.state=="ASSIGNED_TO_USER"?(t(),s("div",na,ia)):n.state=="ASSIGNED_TO_ROOM"?(t(),s("div",ra,ca)):n.state=="LOCKED"?(t(),s("div",ua,[e("div",ma,[a(o(me),{class:"h-4 w-4 text-amber-600 mr-1","aria-hidden":"true"}),pa])])):n.state=="DISCARDED"?(t(),s("div",_a,[e("div",va,[a(o(pe),{class:"h-4 w-4 text-amber-600 mr-1","aria-hidden":"true"}),ha])])):w("",!0)])):w("",!0)]),e("td",xa,[o(z).check("items.read")||o(z).check("property.master")?(t(),s("button",{key:0,onClick:C(D=>(J(n),u.$refs.detailItemRef.openModal()),["prevent"]),class:"mr-2"},[a(o(Se),{class:"h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 p-2 rounded-lg","aria-hidden":"true"})],8,ga)):w("",!0),o(z).check("items.edit")||o(z).check("property.master")?(t(),s("button",{key:1,onClick:C(D=>(J(n),u.$refs.editItemRef.openModal()),["prevent"]),class:"mr-2"},[a(o(Ve),{class:"h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 p-2 rounded-lg","aria-hidden":"true"})],8,ya)):w("",!0),o(z).check("items.delete")||o(z).check("property.master")?(t(),s("button",{key:2,onClick:C(D=>(J(n),u.$refs.deleteItemRef.openModal()),["prevent"])},[a(o(N),{class:"h-8 w-8 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-1.5 rounded-lg","aria-hidden":"true"})],8,ba)):w("",!0)])]))),128))])):(t(),s("tbody",fa,wa)),f.value&&f.value.length?(t(),s("tfoot",$a,[e("tr",null,[e("td",Ia,[e("div",Ca,[e("button",{onClick:d[9]||(d[9]=C(n=>le(),["prevent"])),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700 flex gap-1 justify-center items-center"},Sa),e("button",{onClick:d[10]||(d[10]=C(n=>f.value=[],["prevent"])),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},Da)])])])])):w("",!0)])):(t(),R(T,{key:1,class:"mx-auto text-spinner-color",size:"40"}))])])]),I.value!==null?(t(),R(De,{key:0,meta:I.value,onSetPage:ge,modelValue:$.value,"onUpdate:modelValue":d[11]||(d[11]=n=>$.value=n)},null,8,["meta","modelValue"])):w("",!0)])]),a(ot,{ref_key:"createItemRef",ref:j,onReloadItems:d[12]||(d[12]=n=>O())},null,512),a(it,{ref_key:"deleteItemRef",ref:E,selectedItem:x.value,onReloadItems:d[13]||(d[13]=n=>O())},null,8,["selectedItem"]),a(St,{ref_key:"editItemRef",ref:l,selectedItem:x.value,onReloadItems:d[14]||(d[14]=n=>O())},null,8,["selectedItem"]),a(Es,{ref_key:"detailItemRef",ref:b,selectedItem:x.value,onReloadItems:d[15]||(d[15]=n=>O())},null,8,["selectedItem"])],64)}}};export{Ha as default};
