import{l as _}from"./vue-tailwind-datepicker-2f6f3483.js";import"./index-ad469968.js";var x={name:"hu",weekdays:"vasárnap_hétfő_kedd_szerda_csütörtök_péntek_szombat".split("_"),weekdaysShort:"vas_hét_kedd_sze_csüt_pén_szo".split("_"),weekdaysMin:"v_h_k_sze_cs_p_szo".split("_"),months:"január_február_március_április_május_június_július_augusztus_szeptember_október_november_december".split("_"),monthsShort:"jan_feb_márc_ápr_máj_jún_júl_aug_szept_okt_nov_dec".split("_"),ordinal:function(e){return e+"."},weekStart:1,relativeTime:{future:"%s múlva",past:"%s",s:function(e,n,r,t){return"néhány másodperc"+(t||n?"":"e")},m:function(e,n,r,t){return"egy perc"+(t||n?"":"e")},mm:function(e,n,r,t){return e+" perc"+(t||n?"":"e")},h:function(e,n,r,t){return"egy "+(t||n?"óra":"órája")},hh:function(e,n,r,t){return e+" "+(t||n?"óra":"órája")},d:function(e,n,r,t){return"egy "+(t||n?"nap":"napja")},dd:function(e,n,r,t){return e+" "+(t||n?"nap":"napja")},M:function(e,n,r,t){return"egy "+(t||n?"hónap":"hónapja")},MM:function(e,n,r,t){return e+" "+(t||n?"hónap":"hónapja")},y:function(e,n,r,t){return"egy "+(t||n?"év":"éve")},yy:function(e,n,r,t){return e+" "+(t||n?"év":"éve")}},formats:{LT:"H:mm",LTS:"H:mm:ss",L:"YYYY.MM.DD.",LL:"YYYY. MMMM D.",LLL:"YYYY. MMMM D. H:mm",LLLL:"YYYY. MMMM D., dddd H:mm"}};_.locale(x,null,!0);export{x as default};
