import{l as m}from"./vue-tailwind-datepicker-96876598.js";import"./index-3de9bee7.js";function _(e){return e>1&&e<5&&~~(e/10)!==1}function n(e,t,r,u){var s=e+" ";switch(r){case"s":return t||u?"pár sekund":"pár sekundami";case"m":return t?"minuta":u?"minutu":"minutou";case"mm":return t||u?s+(_(e)?"minuty":"minut"):s+"minutami";case"h":return t?"hodina":u?"hodinu":"hodinou";case"hh":return t||u?s+(_(e)?"hodiny":"hodin"):s+"hodinami";case"d":return t||u?"den":"dnem";case"dd":return t||u?s+(_(e)?"dny":"dní"):s+"dny";case"M":return t||u?"měsíc":"měsícem";case"MM":return t||u?s+(_(e)?"měsíce":"měsíců"):s+"měsíci";case"y":return t||u?"rok":"rokem";case"yy":return t||u?s+(_(e)?"roky":"let"):s+"lety"}}var a={name:"cs",weekdays:"neděle_pondělí_úterý_středa_čtvrtek_pátek_sobota".split("_"),weekdaysShort:"ne_po_út_st_čt_pá_so".split("_"),weekdaysMin:"ne_po_út_st_čt_pá_so".split("_"),months:"leden_únor_březen_duben_květen_červen_červenec_srpen_září_říjen_listopad_prosinec".split("_"),monthsShort:"led_úno_bře_dub_kvě_čvn_čvc_srp_zář_říj_lis_pro".split("_"),weekStart:1,yearStart:4,ordinal:function(e){return e+"."},formats:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd D. MMMM YYYY H:mm",l:"D. M. YYYY"},relativeTime:{future:"za %s",past:"před %s",s:n,m:n,mm:n,h:n,hh:n,d:n,dd:n,M:n,MM:n,y:n,yy:n}};m.locale(a,null,!0);export{a as default};
