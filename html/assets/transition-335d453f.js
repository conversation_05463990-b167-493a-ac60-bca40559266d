import{N as oe,C as ue,e as h,t as se,u as H,o as G,c as de,l as p,g as fe,H as Q,p as ve,T as pe}from"./hidden-f039557c.js";import{r as W}from"./dialog-8577fd56.js";import{G as X,a as g,a9 as Y,I as L,B as S,J as Z,K as C,C as me,H as x,n as ce,j as D}from"./index-ad469968.js";function he(e){let t={called:!1};return(...a)=>{if(!t.called)return t.called=!0,e(...a)}}function O(e,...t){e&&t.length>0&&e.classList.add(...t)}function w(e,...t){e&&t.length>0&&e.classList.remove(...t)}var j=(e=>(e.Finished="finished",e.Cancelled="cancelled",e))(j||{});function ge(e,t){let a=W();if(!e)return a.dispose;let{transitionDuration:i,transitionDelay:o}=getComputedStyle(e),[n,l]=[i,o].map(r=>{let[s=0]=r.split(",").filter(Boolean).map(d=>d.includes("ms")?parseFloat(d):parseFloat(d)*1e3).sort((d,f)=>f-d);return s});return n!==0?a.setTimeout(()=>t("finished"),n+l):t("finished"),a.add(()=>t("cancelled")),a.dispose}function K(e,t,a,i,o,n){let l=W(),r=n!==void 0?he(n):()=>{};return w(e,...o),O(e,...t,...a),l.nextFrame(()=>{w(e,...a),O(e,...i),l.add(ge(e,s=>(w(e,...i,...t),O(e,...o),r(s))))}),l.add(()=>w(e,...t,...a,...i,...o)),l.add(()=>r("cancelled")),l.dispose}function c(e=""){return e.split(" ").filter(t=>t.trim().length>1)}let P=Symbol("TransitionContext");var be=(e=>(e.Visible="visible",e.Hidden="hidden",e))(be||{});function ye(){return D(P,null)!==null}function Se(){let e=D(P,null);if(e===null)throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");return e}function Ee(){let e=D($,null);if(e===null)throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");return e}let $=Symbol("NestingContext");function F(e){return"children"in e?F(e.children):e.value.filter(({state:t})=>t==="visible").length>0}function _(e){let t=g([]),a=g(!1);S(()=>a.value=!0),Z(()=>a.value=!1);function i(n,l=h.Hidden){let r=t.value.findIndex(({id:s})=>s===n);r!==-1&&(H(l,{[h.Unmount](){t.value.splice(r,1)},[h.Hidden](){t.value[r].state="hidden"}}),!F(t)&&a.value&&(e==null||e()))}function o(n){let l=t.value.find(({id:r})=>r===n);return l?l.state!=="visible"&&(l.state="visible"):t.value.push({id:n,state:"visible"}),()=>i(n,h.Unmount)}return{children:t,register:o,unregister:i}}let k=oe.RenderStrategy,Te=X({props:{as:{type:[Object,String],default:"div"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:""},enterFrom:{type:[String],default:""},enterTo:{type:[String],default:""},entered:{type:[String],default:""},leave:{type:[String],default:""},leaveFrom:{type:[String],default:""},leaveTo:{type:[String],default:""}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:a,slots:i,expose:o}){let n=g(0);function l(){n.value|=p.Opening,t("beforeEnter")}function r(){n.value&=~p.Opening,t("afterEnter")}function s(){n.value|=p.Closing,t("beforeLeave")}function d(){n.value&=~p.Closing,t("afterLeave")}if(!ye()&&ue())return()=>Y(Le,{...e,onBeforeEnter:l,onAfterEnter:r,onBeforeLeave:s,onAfterLeave:d},i);let f=g(null),u=g("visible"),q=L(()=>e.unmount?h.Unmount:h.Hidden);o({el:f,$el:f});let{show:b,appear:M}=Se(),{register:R,unregister:B}=Ee(),U={value:!0},y=se(),E={value:!1},I=_(()=>{!E.value&&u.value!=="hidden"&&(u.value="hidden",B(y),d())});S(()=>{let v=R(y);Z(v)}),C(()=>{if(q.value===h.Hidden&&y){if(b&&u.value!=="visible"){u.value="visible";return}H(u.value,{hidden:()=>B(y),visible:()=>R(y)})}});let N=c(e.enter),J=c(e.enterFrom),ee=c(e.enterTo),V=c(e.entered),te=c(e.leave),ne=c(e.leaveFrom),le=c(e.leaveTo);S(()=>{C(()=>{if(u.value==="visible"){let v=G(f);if(v instanceof Comment&&v.data==="")throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")}})});function ae(v){let A=U.value&&!M.value,m=G(f);!m||!(m instanceof HTMLElement)||A||(E.value=!0,b.value&&l(),b.value||s(),v(b.value?K(m,N,J,ee,V,T=>{E.value=!1,T===j.Finished&&r()}):K(m,te,ne,le,V,T=>{E.value=!1,T===j.Finished&&(F(I)||(u.value="hidden",B(y),d()))})))}return S(()=>{me([b],(v,A,m)=>{ae(m),U.value=!1},{immediate:!0})}),x($,I),de(L(()=>H(u.value,{visible:p.Open,hidden:p.Closed})|n.value)),()=>{let{appear:v,show:A,enter:m,enterFrom:T,enterTo:Ce,entered:Fe,leave:Be,leaveFrom:Ae,leaveTo:Oe,...z}=e,re={ref:f},ie={...z,...M&&b&&fe.isServer?{class:ce([a.class,z.class,...N,...J])}:{}};return Q({theirProps:ie,ourProps:re,slot:{},slots:i,attrs:a,features:k,visible:u.value==="visible",name:"TransitionChild"})}}}),we=Te,Le=X({inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:""},enterFrom:{type:[String],default:""},enterTo:{type:[String],default:""},entered:{type:[String],default:""},leave:{type:[String],default:""},leaveFrom:{type:[String],default:""},leaveTo:{type:[String],default:""}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:a,slots:i}){let o=ve(),n=L(()=>e.show===null&&o!==null?(o.value&p.Open)===p.Open:e.show);C(()=>{if(![!0,!1].includes(n.value))throw new Error('A <Transition /> is used but it is missing a `:show="true | false"` prop.')});let l=g(n.value?"visible":"hidden"),r=_(()=>{l.value="hidden"}),s=g(!0),d={show:n,appear:L(()=>e.appear||!s.value)};return S(()=>{C(()=>{s.value=!1,n.value?l.value="visible":F(r)||(l.value="hidden")})}),x($,r),x(P,d),()=>{let f=pe(e,["show","appear","unmount","onBeforeEnter","onBeforeLeave","onAfterEnter","onAfterLeave"]),u={unmount:e.unmount};return Q({ourProps:{...u,as:"template"},theirProps:{},slot:{},slots:{...i,default:()=>[Y(we,{onBeforeEnter:()=>t("beforeEnter"),onAfterEnter:()=>t("afterEnter"),onBeforeLeave:()=>t("beforeLeave"),onAfterLeave:()=>t("afterLeave"),...a,...u,...f},i.default)]},attrs:{},features:k,visible:l.value==="visible",name:"Transition"})}}});export{Le as S,Te as h};
