import{L as le,a as c,H as ne,B as Y,r as J,o as t,c as L,w as f,e as r,h as V,d as e,u as l,b as s,V as oe,C,v as x,F as T,f as F,n as ee,t as S,k as j,Z as ae,q as G,j as te,s as re,E as ie,y as de,I as ue}from"./index-0d8d3833.js";import{_ as ce}from"./AppTopbar-2155fa89.js";import{c as g}from"./checkPermission.service-455e2b2e.js";import{x as me,O as pe,_ as ve,Z as xe,g as ye,A as be,i as fe,$ as ge,a0 as he,X as ke,s as _e}from"./index-5b2a5028.js";import{_ as Q}from"./basicModal-0e3662fb.js";import{X,e as we,E as W}from"./index-cf95b4f0.js";import{_ as $e}from"./pagination-a853c4ff.js";import{d as Ce}from"./debounce-c6fdd1f3.js";import{N as Pe,$ as Ie,K as Me,U as Se,_ as se}from"./combobox-9ff0ca81.js";import{S as K}from"./transition-4cce3ae4.js";import"./dialog-a8268362.js";import"./hidden-b3b94d42.js";import"./listbox-26696037.js";import"./use-tracked-pointer-9b5e4d20.js";import"./use-resolve-button-type-370dbe2f.js";import"./use-controllable-9e1e9dcb.js";import"./use-tree-walker-b3b264f7.js";const Re={key:0,class:"p-6"},Ee={key:0},Oe={class:"relative mt-1"},ze={class:"absolute mt-1 z-10 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},Ve={key:0},De={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},Ue={key:1,class:"h-20 max-h-20 overflow-hidden"},Ne={key:2},je=["onClick"],Ge={class:"block truncate w-3/4 py-2"},Le={class:"inline-block w-1/4 border-l px-3 py-2"},Te={key:0,class:"px-4 pb-4"},qe={key:1,class:"p-6"},Ae={class:"border-t p-5"},Be={class:"text-right space-x-3"},Ze={__name:"addProtocolItemModal",props:{protocol:{type:Object,required:!0}},emits:["reloadGroups"],setup(D,{expose:O,emit:z}){const R=D,E=le(),P=z,I=c(!1),v=c(!1),n=c([]);c("");const p=c(1),b=c(null),a=c(null),_=c(!1),h=c(!1);let o=c("");const U=ne(()=>o.value===""?n.value:n.value.filter(k=>k.name.toLowerCase().includes(o.value.toLowerCase())||k.evidence_number.toString().toLowerCase().includes(o.value.toLowerCase())));Y(o,()=>{_.value=!0,M()}),Y(()=>E.perPage,(k,i)=>{v.value=!0,p.value=1,M()});const M=Ce(async()=>{try{const k=await j.get(`/api/items?page=${p.value}&perpage=${E.perPage}&search=${o.value}&state=NOT_ASSIGNED`);n.value=k.data.data,b.value=k.data.meta}catch(k){console.error(k)}finally{_.value=!1,v.value=!1}},300);async function m(k){_.value=!0,p.value=k,await M(),await ae(),setTimeout(()=>{h.value=!0},0)}function d(k){k.stopPropagation(),h.value=!h.value}function $(k){a.value=k,h.value=!1}function u(){I.value=!1}async function N(){p.value=1,o.value="",h.value=!1,I.value=!0,a.value=null,await M()}async function H(){var k;await j.post("api/moves/items",{move_id:R.protocol.id,item_id:(k=a.value)==null?void 0:k.id}).then(i=>{G.success(i.data.message)}).catch(i=>{console.log(i)}),u(),P("reloadProtocolDetail",!0)}return O({openModal:N}),(k,i)=>{const q=J("VueSpinner");return t(),L(l(K),{appear:"",show:I.value,as:"template",onClose:i[9]||(i[9]=y=>u())},{default:f(()=>[r(Q,{size:"xs"},{"modal-title":f(()=>i[10]||(i[10]=[V("Přidat položku do protokolu")])),"modal-close-button":f(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:i[0]||(i[0]=y=>u())},[r(l(X),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":f(()=>[e("div",null,[v.value?(t(),s("div",qe,[r(q,{class:"mx-auto text-spinner-color",size:"40"})])):(t(),s("div",Re,[n.value?(t(),s("div",Ee,[i[12]||(i[12]=e("span",{class:"text-sm mb-2"},"Vyberte položku:",-1)),r(l(Pe),{modelValue:a.value,"onUpdate:modelValue":[i[6]||(i[6]=y=>a.value=y),$]},{default:f(()=>[e("div",Oe,[e("div",{class:"relative w-full block rounded-lg border-0 py-0.5 px-1 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",onClick:i[2]||(i[2]=y=>h.value=!0)},[r(l(Ie),{class:"w-full border-none py-2 pl-3 pr-10 text-sm leading-5 text-gray-900 focus:ring-0",displayValue:y=>(y==null?void 0:y.name)||"",onChange:i[1]||(i[1]=y=>oe(o)?o.value=y.target.value:o=y.target.value),placeholder:"Začněte psát název položky, nebo klikněte na šipku vpravo."},null,8,["displayValue"]),r(l(Me),{class:"absolute inset-y-0 right-0 flex items-center pr-2",onClick:C(d,["prevent"])},{default:f(()=>[r(l(we),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),r(l(K),{show:h.value,leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:i[5]||(i[5]=y=>oe(o)?o.value="":o="")},{default:f(()=>[e("div",ze,[r(l(Se),null,{default:f(()=>[_.value?x("",!0):(t(),s("div",Ve,[U.value.length===0&&l(o)!==""?(t(),s("div",De," Žádné položky nenalezeny. ")):x("",!0)])),_.value?(t(),s("div",Ue,[r(q,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(t(),s("div",Ne,[r(l(se),{as:"template"},{default:f(()=>i[11]||(i[11]=[e("li",{class:"relative select-none px-4 pointer-events-none border-b"},[e("div",{class:"flex justify-between gap-6 truncate"},[e("span",{class:"block truncate w-3/4 py-2"},"Název položky"),e("span",{class:"inline-block w-1/4 border-l px-3 py-2"},"Evidenční číslo")])],-1)])),_:1}),(t(!0),s(T,null,F(U.value,y=>(t(),L(l(se),{as:"template",key:y.id,value:y},{default:f(({selected:A,active:B})=>[e("li",{class:ee(["relative cursor-pointer select-none px-4",{"bg-teal-600 text-white":B,"hover:bg-teal-600 hover:text-white":!B}]),onClick:w=>$(y)},[e("div",{class:ee(["flex justify-between gap-6 truncate",{"font-medium":A,"font-normal":!A}])},[e("span",Ge,S(y.name),1),e("span",Le,S(y.evidence_number),1)],2)],10,je)]),_:2},1032,["value"]))),128))]))]),_:1}),_.value?x("",!0):(t(),s("div",Te,[b.value!==null?(t(),L($e,{key:0,meta:b.value,onSetPage:m,modelValue:p.value,"onUpdate:modelValue":i[3]||(i[3]=y=>p.value=y),onMousedown:i[4]||(i[4]=C(()=>{},["prevent"]))},null,8,["meta","modelValue"])):x("",!0)]))])]),_:1},8,["show"])])]),_:1},8,["modelValue"])])):x("",!0)])),e("div",Ae,[e("div",Be,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:i[7]||(i[7]=C(y=>u(),["prevent"]))}," Zavřít "),e("button",{onClick:i[8]||(i[8]=C(y=>H(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Přidat ")])])])]),_:1})]),_:1},8,["show"])}}},Fe={class:"border-t p-5"},Xe={class:"text-right space-x-3"},Ke={__name:"deleteProtocolItemModal",props:{selectedItem:{type:Object,required:!0}},emits:["reloadGroups"],setup(D,{expose:O,emit:z}){const R=D,E=z,P=c(!1);te("debugModeGlobalVar");const I=c(!1);function v(){P.value=!1}function n(){P.value=!0}async function p(){g.check("moves.items")||g.check("property.master")?await j.post("api/moves/items/"+R.selectedItem.pivot.id+"/delete").then(b=>{G.success(b.data.message)}).catch(b=>{console.log(b)}):I.value=!1,v(),E("reloadProtocolDetail",!0)}return O({openModal:n}),(b,a)=>(t(),L(l(K),{appear:"",show:P.value,as:"template",onClose:a[3]||(a[3]=_=>v())},{default:f(()=>[r(Q,{size:"xs"},{"modal-title":f(()=>a[4]||(a[4]=[V("Odebrat položku z protokolu")])),"modal-close-button":f(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:a[0]||(a[0]=_=>v())},[r(l(X),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":f(()=>[e("div",null,[a[5]||(a[5]=e("div",{class:"p-6"},[e("span",null,"Opravdu si přejete položku odebrat?")],-1)),e("div",Fe,[e("div",Xe,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:a[1]||(a[1]=C(_=>v(),["prevent"]))}," Zavřít "),e("button",{onClick:a[2]||(a[2]=C(_=>p(),["prevent"])),class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-red-600",type:"submit"}," Odebrat ")])])])]),_:1})]),_:1},8,["show"]))}},He={class:"border-t p-5"},Je={class:"text-right space-x-3"},Qe={__name:"ProcessProtocolModal",props:{protocolId:{type:Number,required:!0}},emits:["reloadGroups"],setup(D,{expose:O,emit:z}){const R=D,E=z,P=c(!1);te("debugModeGlobalVar");const I=c(!1);function v(){P.value=!1}function n(){P.value=!0}async function p(){g.check("moves.process")||g.check("property.master")?(console.log(R),await j.post("api/moves/"+R.protocolId+"/process").then(b=>{G.success(b.data.message)}).catch(b=>{console.log(b)})):I.value=!1,v(),E("reloadProtocolDetail",!0)}return O({openModal:n}),(b,a)=>(t(),L(l(K),{appear:"",show:P.value,as:"template",onClose:a[3]||(a[3]=_=>v())},{default:f(()=>[r(Q,{size:"xs"},{"modal-title":f(()=>a[4]||(a[4]=[V("Uzavřít protokol")])),"modal-close-button":f(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:a[0]||(a[0]=_=>v())},[r(l(X),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":f(()=>[e("div",null,[a[5]||(a[5]=e("div",{class:"p-6"},[e("span",null,"Opravdu si přejete protokol uzavřít?")],-1)),e("div",He,[e("div",Je,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:a[1]||(a[1]=C(_=>v(),["prevent"]))}," Zavřít "),e("button",{onClick:a[2]||(a[2]=C(_=>p(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Uzavřít ")])])])]),_:1})]),_:1},8,["show"]))}},We={key:0,class:"px-6 pt-6"},Ye={key:0,class:"space-y-3"},et={key:1,class:"p-6"},tt={class:"mt-2"},ot={key:2,class:"p-6"},st={class:"px-6 pb-6"},lt={key:0,class:"space-y-3 mb-3"},nt=["onClick"],at={key:0,class:"mb-2"},rt={class:"text-sm font-medium"},it={class:"mt-1"},dt={key:1,class:"space-y-3"},ut={class:"border-t p-5"},ct={class:"text-right space-x-3"},mt={__name:"scanProtocolItemsModal",props:{protocol:{type:Object,required:!0}},emits:["reloadGroups"],setup(D,{expose:O,emit:z}){const R=D,E=z,P=c(!1),I=c(!1),v=c(""),n=c(!1),p=c(null),b=c(null);c(null);function a(){P.value=!1}async function _(){P.value=!0,v.value=null,b.value=null,n.value=!1,p.value=null}Y(v,()=>{n.value=!1});async function h(){n.value=!1,I.value=!0,await j.post("api/moves/items/multiple/check",{move_id:R.protocol.id,evidence_numbers:v.value}).then(m=>{G.success(m.data.message);const d=Array.from(new Map(m.data.data.items.map($=>[$.evidence_number,$])).values());p.value={errors:m.data.data.errors,items:d},o()}).catch(m=>{console.log(m)}),I.value=!1}async function o(){var d,$,u;if((d=p.value)!=null&&d.errors&&((u=($=p.value)==null?void 0:$.items)==null?void 0:u.length)==0){n.value=!1;return}const m=p.value.items.some(N=>N.errors!==null);n.value=!m}function U(m,d){var $;if(($=p.value)!=null&&$.items&&(p.value.items=p.value.items.filter(u=>u.id!==m)),v.value){let u=v.value.split(";");u=u.filter(N=>N!==d.toString()),v.value=u.join(";")}}async function M(){var $;if(!(($=p.value)!=null&&$.items))return;const m=p.value.items.filter(u=>u.errors===null).map(u=>u.id);if(m.length===0){G.warning("Žádné položky bez chyb k odeslání.");return}const d={move_id:R.protocol.id,items:m};await j.post("api/moves/items/multiple",d).then(u=>{b.value=u.data,u.data.success?(G.success(u.data.message),a(),E("reloadProtocolDetail",!0)):(G.error(u.data.message),console.log(u.data.data))}).catch(u=>{console.log(u),b.value=u.response.data.data.errors})}return O({openModal:_}),(m,d)=>{const $=J("VueSpinner");return t(),L(l(K),{appear:"",show:P.value,as:"template",onClose:d[5]||(d[5]=u=>a())},{default:f(()=>[r(Q,{size:"sm"},{"modal-title":f(()=>d[6]||(d[6]=[V("Přidat položky do protokolu")])),"modal-close-button":f(()=>[e("button",{type:"button",tabindex:"-1",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:d[0]||(d[0]=u=>a())},[r(l(X),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":f(()=>{var u,N,H,k,i,q,y,A,B;return[e("div",null,[b.value?(t(),s("div",We,[b.value&&((u=b.value)==null?void 0:u.length)>0?(t(),s("div",Ye,[(t(!0),s(T,null,F(b.value,(w,Z)=>(t(),s("div",null,[(t(),s("div",{key:Z,class:"text-sm border-2 rounded-md border-red-600 p-4 relative"},[e("p",null,[r(l(W),{class:"h-5 w-5 text-red-600 inline mr-2","aria-hidden":"true"}),V(S(w),1)])]))]))),256))])):x("",!0)])):x("",!0),I.value?(t(),s("div",ot,[r($,{class:"mx-auto text-spinner-color",size:"40"})])):(t(),s("div",et,[e("div",null,[d[8]||(d[8]=e("label",{for:"itemsCodes",class:"block text-sm font-normal leading-6 text-gray-900"},"Evidenční čísla:",-1)),e("div",tt,[r(l(re),{modelValue:v.value,"onUpdate:modelValue":d[1]||(d[1]=w=>v.value=w),as:"textarea",name:"itemsCodes",id:"itemsCodes",cols:"30",autofocus:"",rows:"3",class:"resize-none block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Načtěte položky, nebo vložte evidenční čísla..."},null,8,["modelValue"]),r(l(ie),{name:"itemsCodes",class:"text-rose-400 text-sm block pt-1"}),d[7]||(d[7]=e("p",{class:"text-xs text-gray-600 mt-2"},[V("Vložte evidenční čísla ve formátu se středníkem na konci: "),e("strong",null,"100002;100003;100004;")],-1))])])])),e("div",st,[(N=p.value)!=null&&N.items&&((k=(H=p.value)==null?void 0:H.items)==null?void 0:k.length)>0?(t(),s("div",lt,[(t(!0),s(T,null,F((i=p.value)==null?void 0:i.items,(w,Z)=>(t(),s("div",null,[(t(),s("div",{key:Z,class:ee(["text-sm rounded-md p-4 relative border-2",w.errors?"border-orange-500":"border-green-600"])},[e("button",{type:"button",class:"flex justify-center items-center rounded-md border border-transparent bg-neutral-200 shadow-sm hover:bg-neutral-300 absolute top-2 right-2",onClick:oo=>U(w.id,w.evidence_number)},[r(l(X),{class:"mx-auto h-6 w-6 p-1 text-neutral-600","aria-hidden":"true"})],8,nt),w.errors?(t(),s("p",at,[r(l(W),{class:"h-5 w-5 text-orange-500 inline mr-2","aria-hidden":"true"}),V(S(w.errors),1)])):x("",!0),e("p",rt,S(w.name),1),e("p",it,[d[9]||(d[9]=e("span",{class:"font-semibold"},"EČ:",-1)),V(" "+S(w.evidence_number),1)])],2))]))),256))])):x("",!0),(q=p.value)!=null&&q.errors&&((A=(y=p.value)==null?void 0:y.errors)==null?void 0:A.length)>0?(t(),s("div",dt,[(t(!0),s(T,null,F((B=p.value)==null?void 0:B.errors,(w,Z)=>(t(),s("div",null,[(t(),s("div",{key:Z,class:"text-sm rounded-md"},[e("p",null,[r(l(W),{class:"h-5 w-5 text-red-600 inline mr-2","aria-hidden":"true"}),V(S(w.message),1)])]))]))),256))])):x("",!0)]),e("div",ut,[e("div",ct,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:d[2]||(d[2]=C(w=>a(),["prevent"]))}," Zavřít "),n.value?(t(),s("button",{key:0,onClick:d[3]||(d[3]=C(w=>M(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Přidat ")):(t(),s("button",{key:1,onClick:d[4]||(d[4]=C(w=>h(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Ověřit položky "))])])])]}),_:1})]),_:1},8,["show"])}}},pt={class:"space-y-6"},vt={class:"px-0"},xt={class:"bg-white border border-zinc-200/70 rounded-md p-5 flex justify-between items-center"},yt={class:"sm:flex items-center gap-10"},bt={key:0},ft={key:0,class:"px-2 py-1 rounded-md text-sm text-green-600 inline-block"},gt={class:"flex items-center"},ht={key:1,class:"px-2 py-1 rounded-md text-sm text-green-600 inline-block"},kt={class:"flex items-center"},_t={key:2,class:"px-2 py-1 rounded-md text-sm text-red-600 inline-block"},wt={class:"flex items-center"},$t={key:3,class:"px-2 py-1 rounded-md text-sm text-red-600 inline-block"},Ct={class:"flex items-center"},Pt={key:0,class:"flex items-center text-sm"},It={key:0},Mt={key:1,class:"text-amber-600"},St={key:1,class:"flex items-center"},Rt={key:0},Et={key:1,class:"text-amber-600"},Ot={class:"flex items-center gap-6"},zt={key:0},Vt={key:0,class:"border border-green-600 px-2 py-1 rounded-md text-xs text-green-600 inline-block"},Dt={class:"flex"},Ut={key:1,class:"border border-red-600 px-2 py-1 rounded-md text-xs text-red-600 inline-block"},Nt={class:"flex"},jt={key:1},Gt={key:2},Lt={class:"flow-root bg-white border border-zinc-200/70 rounded-md"},Tt={class:"sm:-mx-6 lg:-mx-8"},qt={class:"inline-block min-w-full align-middle sm:px-6 lg:px-8"},At={key:0,class:"min-w-full divide-y divide-gray-200"},Bt={key:0,scope:"col",class:"py-4 pl-5 pr-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"},Zt={key:0,class:"divide-y divide-gray-200"},Ft={class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600 w-72"},Xt={key:0},Kt={key:1},Ht={class:"whitespace-nowrap py-4 pl-3 pr-3 text-sm text-gray-600"},Jt={key:0},Qt={key:1},Wt={key:0,class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600 text-end w-10"},Yt=["onClick"],eo={key:1},to={class:"flex justify-center"},ko={__name:"ProtocolsDetail",setup(D){const O=de();le();const z=c(),R=c(),E=c(),P=c();te("debugModeGlobalVar");const I=c(["inventories","protocol-detail"]),v=c(!1),n=c({});c(1);const p=c(""),b=c();ue(()=>{a()});async function a(){v.value=!0,await j.get("/api/moves/"+O.params.id).then(h=>{p.value=h.data.meta,n.value=h.data.data}).catch(h=>{console.log(h)}),v.value=!1}function _(h){j.post("api/moves/"+h.id+"/generate-pdf",{type:h.type},{responseType:"blob"}).then(o=>{const U=window.URL.createObjectURL(new Blob([o.data])),M=document.createElement("a");M.href=U,M.setAttribute("download","Protokol.pdf"),document.body.appendChild(M),M.click()}).catch(o=>{console.error("Chyba při získávání souboru:",o)})}return(h,o)=>{const U=J("router-link"),M=J("VueSpinner");return t(),s(T,null,[r(ce,{breadCrumbs:I.value},{topbarButtons:f(()=>[r(U,{to:{name:"protocols"},class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},{default:f(()=>o[8]||(o[8]=[e("div",{class:"flex"},[e("span",null,"Zpět")],-1)])),_:1})]),_:1},8,["breadCrumbs"]),e("div",pt,[e("div",vt,[e("div",xt,[e("div",yt,[e("div",null,[n.value.type?(t(),s("div",bt,[n.value.type=="TO_USER"?(t(),s("div",ft,[e("div",gt,[r(l(me),{class:"h-5 w-5 text-green-600 mr-2","aria-hidden":"true"}),o[9]||(o[9]=e("span",{class:"font-medium"},"Přiřazeno uživateli",-1))])])):x("",!0),n.value.type=="TO_ROOM"?(t(),s("div",ht,[e("div",kt,[r(l(pe),{class:"h-5 w-5 text-green-600 mr-2","aria-hidden":"true"}),o[10]||(o[10]=e("span",{class:"font-medium"},"Přiřazeno do místnosti",-1))])])):n.value.type=="FROM_USER"?(t(),s("div",_t,[e("div",wt,[r(l(ve),{class:"h-5 w-5 text-red-600 mr-2","aria-hidden":"true"}),o[11]||(o[11]=e("span",{class:"font-medium"},"Odebráno uživateli",-1))])])):n.value.type=="FROM_ROOM"?(t(),s("div",$t,[e("div",Ct,[r(l(xe),{class:"h-5 w-5 text-red-600 mr-2","aria-hidden":"true"}),o[12]||(o[12]=e("span",{class:"font-medium"},"Odebráno z místnosti",-1))])])):x("",!0)])):x("",!0)]),e("div",null,[e("div",null,[n.value.user?(t(),s("span",Pt,[r(l(ye),{class:"h-5 w-5 mr-2","aria-hidden":"true"}),n.value.user.full_name?(t(),s("span",It,S(n.value.user.full_name),1)):(t(),s("span",Mt,S(n.value.user.email)+" - Uživateli chybí jméno ",1))])):x("",!0),n.value.room?(t(),s("span",St,[r(l(be),{class:"h-5 w-5 mr-2","aria-hidden":"true"}),n.value.room.name?(t(),s("span",Rt,S(n.value.room.name),1)):(t(),s("span",Et," Místnosti chybí jméno "))])):x("",!0)])])]),e("div",Ot,[n.value.state?(t(),s("div",zt,[n.value.state=="PROCESSED"?(t(),s("div",Vt,[e("div",Dt,[r(l(fe),{class:"h-4 w-4 text-green-600 mr-1","aria-hidden":"true"}),o[13]||(o[13]=e("span",null,"Vyřízeno",-1))])])):n.value.state=="UNPROCESSED"?(t(),s("div",Ut,[e("div",Nt,[r(l(ge),{class:"h-4 w-4 text-red-600 mr-1","aria-hidden":"true"}),o[14]||(o[14]=e("span",null,"Nevyřízeno",-1))])])):x("",!0)])):x("",!0),(l(g).check("moves.items")||l(g).check("property.master"))&&n.value.state=="UNPROCESSED"?(t(),s("div",jt,[(l(g).check("moves.items")||l(g).check("property.master"))&&n.value.state=="UNPROCESSED"?(t(),s("button",{key:0,onClick:o[0]||(o[0]=C(m=>h.$refs.scanProtocolItemRef.openModal(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},"Načíst čtečkou ")):x("",!0)])):x("",!0),(l(g).check("moves.items")||l(g).check("property.master"))&&n.value.state=="UNPROCESSED"?(t(),s("div",Gt,[(l(g).check("moves.items")||l(g).check("property.master"))&&n.value.state=="UNPROCESSED"?(t(),s("button",{key:0,onClick:o[1]||(o[1]=C(m=>h.$refs.addProtocolItemRef.openModal(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},"Přidat položku ")):x("",!0)])):x("",!0),(l(g).check("inventories.read")||l(g).check("property.master"))&&n.value.state=="PROCESSED"?(t(),s("button",{key:3,onClick:o[2]||(o[2]=C(m=>_(n.value),["prevent"])),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700 flex items-center gap-2"},[r(l(he),{class:"h-6 w-6 text-white rounded-lg","aria-hidden":"true"}),o[15]||(o[15]=e("span",{class:"text-sm"},"Generovat PDF",-1))])):x("",!0)])])]),e("div",null,[e("div",Lt,[e("div",Tt,[e("div",qt,[v.value==!1?(t(),s("table",At,[e("thead",null,[e("tr",null,[o[16]||(o[16]=e("th",{scope:"col",class:"py-4 pl-5 pr-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"}," Ev. číslo ",-1)),o[17]||(o[17]=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Název ",-1)),l(g).check("moves.edit")||l(g).check("moves.delete")||l(g).check("property.master")?(t(),s("th",Bt)):x("",!0)])]),n.value&&n.value.items&&n.value.items.length?(t(),s("tbody",Zt,[(t(!0),s(T,null,F(n.value.items,m=>(t(),s("tr",{key:m.id},[e("td",Ft,[m.evidence_number?(t(),s("span",Xt,S(m.evidence_number),1)):(t(),s("span",Kt,"-"))]),e("td",Ht,[m.name?(t(),s("span",Jt,S(m.name),1)):(t(),s("span",Qt,"-"))]),l(g).check("moves.items")||l(g).check("property.master")?(t(),s("td",Wt,[(l(g).check("moves.items")||l(g).check("property.master"))&&n.value.state=="UNPROCESSED"?(t(),s("button",{key:0,onClick:C(d=>(b.value=m,h.$refs.deleteProtocolItemRef.openModal()),["prevent"])},[r(l(ke),{class:"h-8 w-8 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-1.5 rounded-lg","aria-hidden":"true"})],8,Yt)):x("",!0)])):x("",!0)]))),128))])):(t(),s("tbody",eo,o[18]||(o[18]=[e("tr",null,[e("td",{colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},"Nebyly nalezeny žádné položky.")],-1)])))])):(t(),L(M,{key:1,class:"mx-auto text-spinner-color",size:"40"}))])])])]),e("div",to,[(l(g).check("moves.process")||l(g).check("property.master"))&&n.value.state=="UNPROCESSED"?(t(),s("button",{key:0,onClick:o[3]||(o[3]=C(m=>h.$refs.processProtocolRef.openModal(),["prevent"])),class:"flex items-center gap-3 rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},[r(l(_e),{class:"h-6 w-6 text-white","aria-hidden":"true"}),o[19]||(o[19]=e("span",null,"Uzavřít protokol",-1))])):x("",!0)])]),r(Ze,{ref_key:"addProtocolItemRef",ref:z,protocol:n.value,onReloadProtocolDetail:o[4]||(o[4]=m=>a())},null,8,["protocol"]),r(mt,{ref_key:"scanProtocolItemRef",ref:R,protocol:n.value,onReloadProtocolDetail:o[5]||(o[5]=m=>a())},null,8,["protocol"]),r(Ke,{ref_key:"deleteProtocolItemRef",ref:E,selectedItem:b.value,onReloadProtocolDetail:o[6]||(o[6]=m=>a())},null,8,["selectedItem"]),r(Qe,{ref_key:"processProtocolRef",ref:P,protocolId:n.value.id,onReloadProtocolDetail:o[7]||(o[7]=m=>a())},null,8,["protocolId"])],64)}}};export{ko as default};
