import{a as b,o as n,b as i,d as e,F as G,f as A,n as Q,t as T,r as K,c as H,w as _,e as l,h as R,u as o,z as B,E as q,y as v,H as F,m as $,x as z,l as V,j as ue,D as te,T as oe,q as ce,G as me,K as ee,N as se,i as pe}from"./index-bca36496.js";import{_ as ge}from"./AppTopbar-e695fe6e.js";import{i as ve,a as be,N as fe,j as _e,k as xe,l as ye,m as he,n as ke,H as we,o as Ve,p as $e,h as Ce,q as Se,r as je,g as Ue,s as Ie,V as Me,t as ze,u as Ze,v as Pe,w as Ee,x as Ne,X as Te}from"./index-2e693ea8.js";import{c as P}from"./checkPermission.service-a01992ec.js";import{X as le,d as Oe,C as Re}from"./index-52c9ab2e.js";import{s as Y}from"./default.css_vue_type_style_index_1_src_true_lang-676d2790.js";import{_ as ae}from"./basicModal-b99a0396.js";import{S as ne}from"./transition-b684c7df.js";import{E as Be,A as Ge,F as Le,B as Fe}from"./listbox-4111813e.js";import{a as Ae}from"./switch-82b8cc46.js";import{_ as He}from"./_plugin-vue_export-helper-c27b6911.js";import"./dialog-6ee605ac.js";import"./hidden-e0dd1b29.js";import"./use-tracked-pointer-4ce72e9e.js";import"./use-resolve-button-type-f631c91c.js";import"./use-controllable-90bca8e0.js";const De={class:"pb-6"},Je={class:"block"},qe={class:"flex space-x-4","aria-label":"Tabs"},Xe=["aria-current","onClick"],We={__name:"SettingsNav",emits:["tab"],setup(O,{emit:Z}){const h=Z,m=b([{id:"system-settings",name:"Systémové nastavení",current:!0},{id:"property-settings",name:"Majetek",current:!1}]),s=x=>{m.value.forEach(C=>{C.current=C.id===x}),h("tab",x)};return(x,C)=>(n(),i("div",De,[e("div",Je,[e("nav",qe,[(n(!0),i(G,null,A(m.value,k=>(n(),i("button",{key:k.id,class:Q([k.current?"bg-main-color-200 text-main-color-700":"text-gray-500 hover:text-gray-700","rounded-md px-3 py-2 text-sm font-medium cursor-pointer"]),"aria-current":k.current?"page":void 0,onClick:f=>s(k.id)},T(k.name),11,Xe))),128))])])]))}};const Qe={class:"p-6 web-filter-modal-data"},Ke={class:"text-xl"},Ye={key:0,class:"space-y-5 border-t pt-5 mt-5 pr-5 max-h-96 overflow-y-scroll"},et={class:"space-y-4 sm:flex sm:items-center sm:space-x-10 sm:space-y-0"},tt={class:"flex items-center"},st=["for"],ot={class:"flex items-center"},lt=["for"],at={class:"flex items-center"},nt=["for"],it={class:"flex items-center"},rt=["for"],dt={key:1,class:"h-40 flex items-center"},ut={class:"border-t p-5"},ct={class:"text-right space-x-3"},mt={__name:"webFiltersModal",emits:["reloadWebFilter"],setup(O,{expose:Z,emit:h}){const m=h,s=b(!0),x=b(""),C=b({}),k=b(!1);function f(){k.value=!1}function S(p){s.value=!0,x.value=p,$.get("/api/fortinet/webfilters/"+p).then(r=>{C.value=r.data.data.filters,s.value=!1}).catch(r=>{console.log(r)}),k.value=!0}function N(){$.post("/api/fortinet/webfilters/"+x.value+"/update",{filters:C.value}).then(p=>{z.success(p.data.message),m("reloadWebFilter",!0),f()}).catch(p=>{console.log(p),z.error("Změny se nepovedlo uložit")})}return Z({openModal:S}),(p,r)=>{const j=K("VueSpinner");return n(),H(o(ne),{appear:"",show:k.value,as:"template",onClose:r[3]||(r[3]=w=>f())},{default:_(()=>[l(ae,{size:"lg"},{"modal-title":_(()=>r[4]||(r[4]=[R("Editace kategorie nevhodných stránek")])),"modal-close-button":_(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:r[0]||(r[0]=w=>f())},[l(o(le),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":_(()=>[l(o(B),{onSubmit:r[2]||(r[2]=w=>N())},{default:_(({values:w})=>[e("div",Qe,[e("div",null,[r[5]||(r[5]=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolená kategorie:",-1)),e("p",Ke,T(x.value),1)]),s.value?(n(),i("div",dt,[l(j,{class:"mx-auto text-spinner-color",size:"40"})])):(n(),i("div",Ye,[(n(!0),i(G,null,A(C.value,g=>(n(),i("div",{key:g.id,class:"flex justify-between items-center"},[e("span",null,T(g.name),1),l(o(q),{name:"filter-id"+g.id,class:"text-rose-400 text-sm block pt-1"},null,8,["name"]),e("fieldset",null,[e("div",et,[e("div",tt,[l(o(v),{rules:"requiredRadio",id:"allow-id-"+g.id,name:"filter-id"+g.id,type:"radio",value:"allow",modelValue:g.action,"onUpdate:modelValue":c=>g.action=c,class:"h-4 w-4 border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["id","name","modelValue","onUpdate:modelValue"]),e("label",{for:"allow-id-"+g.id,class:"ml-2 text-sm font-medium leading-6 text-gray-900 cursor-pointer flex items-center gap-1"},[l(o(ve),{class:"mx-auto h-5 w-5 text-green-500","aria-hidden":"true"}),r[6]||(r[6]=e("span",null,"Allow",-1))],8,st)]),e("div",ot,[l(o(v),{rules:"requiredRadio",id:"monitor-id-"+g.id,name:"filter-id"+g.id,type:"radio",value:"monitor",modelValue:g.action,"onUpdate:modelValue":c=>g.action=c,class:"h-4 w-4 border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["id","name","modelValue","onUpdate:modelValue"]),e("label",{for:"monitor-id-"+g.id,class:"ml-3 text-sm font-medium leading-6 text-gray-900 cursor-pointer flex items-center gap-1"},[l(o(be),{class:"mx-auto h-5 w-5 text-sky-500","aria-hidden":"true"}),r[7]||(r[7]=e("span",null,"Monitor",-1))],8,lt)]),e("div",at,[l(o(v),{rules:"requiredRadio",id:"block-id-"+g.id,name:"filter-id"+g.id,type:"radio",value:"block",modelValue:g.action,"onUpdate:modelValue":c=>g.action=c,class:"h-4 w-4 border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["id","name","modelValue","onUpdate:modelValue"]),e("label",{for:"block-id-"+g.id,class:"ml-3 text-sm font-medium leading-6 text-gray-900 cursor-pointer flex items-center gap-1"},[l(o(fe),{class:"mx-auto h-5 w-5 text-red-500","aria-hidden":"true"}),r[8]||(r[8]=e("span",null,"Block",-1))],8,nt)]),e("div",it,[l(o(v),{rules:"requiredRadio",id:"warning-id-"+g.id,name:"filter-id"+g.id,type:"radio",value:"warning",modelValue:g.action,"onUpdate:modelValue":c=>g.action=c,class:"h-4 w-4 border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["id","name","modelValue","onUpdate:modelValue"]),e("label",{for:"warning-id-"+g.id,class:"ml-3 text-sm font-medium leading-6 text-gray-900 cursor-pointer flex items-center gap-1"},[l(o(_e),{class:"mx-auto h-5 w-5 text-amber-500","aria-hidden":"true"}),r[9]||(r[9]=e("span",null,"Warning",-1))],8,rt)])])])]))),128))]))]),e("div",ut,[e("div",ct,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:r[1]||(r[1]=F(g=>f(),["prevent"]))}," Zavřít "),r[10]||(r[10]=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Uložit ",-1))])])]),_:1})]),_:1})]),_:1},8,["show"])}}},pt={class:"p-6"},gt={key:0,class:"min-w-full divide-y divide-gray-200"},vt={key:0,class:"divide-y divide-gray-200"},bt={class:"whitespace-nowrap py-3 pl-5 pr-3 text-left text-sm"},ft={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-600"},_t=["onClick"],xt={key:1},yt={key:1,class:"h-40 flex items-center"},ht={class:"border-t p-5"},kt={class:"text-right space-x-3"},wt={__name:"backupsModal",setup(O,{expose:Z}){const h=b(!1),m=b(!1);function s(){m.value=!1}function x(){m.value=!0,k()}const C=b({});async function k(){h.value=!0,await $.get("/api/backups/").then(p=>{C.value=p.data.data}).catch(p=>{console.log(p)}),h.value=!1}const f=b(null),S=b(null);function N(p){$.post("/api/backups/download",{name:p}).then(r=>{f.value=r.headers["file-name"],S.value=window.URL.createObjectURL(new Blob([r.data],{type:r.headers["content-type"]}));var j=f.value;j=decodeURIComponent(j),j=j.replaceAll("+"," ");var w=S.value,g=document.createElement("a");g.href=w,g.setAttribute("download",j),document.body.appendChild(g),g.click(),f.value=null,S.value=null,z.success("Soubor byl úspěšně stáhnut.")}).catch(r=>{z.error("Soubor se nepodařil stáhnout.")})}return Z({openModal:x}),(p,r)=>{const j=K("VueSpinner");return n(),H(o(ne),{appear:"",show:m.value,as:"template",onClose:r[2]||(r[2]=w=>s())},{default:_(()=>[l(ae,{size:"lg"},{"modal-title":_(()=>r[3]||(r[3]=[R("Zálohy Databáze")])),"modal-close-button":_(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:r[0]||(r[0]=w=>s())},[l(o(le),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":_(()=>[e("div",pt,[h.value==!1?(n(),i("table",gt,[r[5]||(r[5]=e("thead",null,[e("tr",null,[e("th",{scope:"col",class:"py-4 pl-5 pr-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"}," Název souboru "),e("th",{scope:"col",class:"py-4 pl-10 pr-5 text-right text-sm font-semibold text-gray-900 rounded-tr-md bg-gray-100/70"}," Akce ")])],-1)),C.value&&C.value.length?(n(),i("tbody",vt,[(n(!0),i(G,null,A(C.value,w=>(n(),i("tr",{key:w},[e("td",bt,T(w.name),1),e("td",ft,[o(P).check("backup.download")?(n(),i("button",{key:0,type:"button",onClick:F(g=>N(w.name),["prevent"]),class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100 text-right ml-auto"},[l(o(xe),{class:"mx-auto h-9 w-9 p-2 text-main-color-600","aria-hidden":"true"})],8,_t)):V("",!0)])]))),128))])):(n(),i("tbody",xt,r[4]||(r[4]=[e("tr",null,[e("td",{colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},"Nebyly nalezeny žádné zálohy...")],-1)])))])):(n(),i("div",yt,[l(j,{class:"mx-auto text-spinner-color",size:"40"})]))]),e("div",ht,[e("div",kt,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:r[1]||(r[1]=F(w=>s(),["prevent"]))}," Zavřít ")])])]),_:1})]),_:1},8,["show"])}}};const Vt={class:"space-y-12"},$t={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ct={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},St={key:0,class:"p-5 pb-8"},jt={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3"},Ut={class:"flex items-center"},It={key:1,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},Mt={class:"mt-8 grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-6"},zt={class:"sm:col-span-3"},Zt={class:"mt-2"},Pt={class:"sm:col-span-3"},Et={class:"mt-2"},Nt={class:"sm:col-span-3"},Tt={class:"mt-2"},Ot={class:"sm:col-span-3"},Rt={class:"mt-2"},Bt={class:"sm:col-span-3"},Gt={class:"mt-2"},Lt={key:1,class:"h-40 flex items-center"},Ft={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},At={key:0,class:"p-5 pb-8"},Ht={class:"flex justify-between items-center"},Dt={class:"flex items-center"},Jt={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},qt={class:"mt-8 grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-6"},Xt={class:"sm:col-span-3"},Wt={class:"mt-2"},Qt={class:"sm:col-span-3"},Kt={class:"mt-2"},Yt={class:"sm:col-span-3"},es={class:"mt-2"},ts={class:"sm:col-span-3"},ss={class:"mt-2"},os={class:"sm:col-span-3"},ls={class:"mt-2"},as={key:1,class:"h-40 flex items-center"},ns={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},is={key:0,class:"p-5 pb-8"},rs={class:"flex justify-between items-center"},ds={class:"flex items-center"},us={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},cs={class:"mt-8 grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-6"},ms={class:"sm:col-span-3"},ps={class:"mt-2"},gs={class:"sm:col-span-3"},vs={class:"mt-2"},bs={class:"sm:col-span-3"},fs={class:"mt-2"},_s={class:"sm:col-span-3"},xs={class:"mt-2"},ys={class:"sm:col-span-3"},hs={class:"flex gap-x-6 mt-4"},ks={class:"flex gap-x-3"},ws={class:"flex h-6 items-center"},Vs={key:1,class:"h-40 flex items-center"},$s={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},Cs={key:0},Ss={class:"p-5"},js={class:"flex justify-between items-center"},Us={class:"flex items-center"},Is={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},Ms={class:"mt-8 grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-6"},zs={class:"sm:col-span-3"},Zs={class:"mt-2"},Ps={class:"sm:col-span-3"},Es={class:"mt-2"},Ns={class:"border-t mt-1 pt-6 px-5 pb-8"},Ts={class:"space-y-3"},Os=["onClick"],Rs={key:1,class:"h-40 flex items-center"},Bs={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},Gs={key:0,class:"p-5 pb-8"},Ls={class:"flex justify-between items-center"},Fs={class:"flex items-center"},As={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},Hs={class:"mt-8 grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-6 border-b pb-8 mb-6"},Ds={class:"sm:col-span-3"},Js={class:"mt-2"},qs={class:"sm:col-span-3"},Xs={class:"mt-2"},Ws={class:"sm:col-span-3"},Qs={class:"mt-2"},Ks={class:"sm:col-span-3"},Ys={class:"mt-2"},eo={class:"sm:col-span-3"},to={class:"mt-2"},so={class:"sm:col-span-3"},oo={class:"mt-2"},lo={class:"sm:col-span-3"},ao={class:"mt-2"},no={class:"sm:col-span-3"},io={class:"flex gap-x-6 mt-4"},ro={class:"flex gap-x-3"},uo={class:"flex h-6 items-center"},co={class:"flex gap-x-3"},mo={class:"flex h-6 items-center"},po={class:"mt-8 grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-6"},go={class:"sm:col-span-6"},vo={class:"mt-2 mb-6"},bo={key:1,class:"h-40 flex items-center"},fo={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},_o={key:0,class:"p-5"},xo={class:"flex justify-between items-center"},yo={class:"flex items-center"},ho={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},ko={class:"mt-6 pb-4 gap-4"},wo={class:"grow mb-2"},Vo={class:"text-base font-normal leading-6 text-gray-900 flex items-center gap-2"},$o={class:"border-b pt-2 pb-6 grid grid-cols-1 gap-6"},Co={class:"mt-2 pb-6 gap-4"},So={class:"grow mb-2"},jo={class:"text-base font-normal leading-6 text-gray-900 flex items-center gap-2"},Uo={class:"border-b pt-2 pb-4 grid grid-cols-1 gap-6"},Io={class:"mt-2 pb-6 gap-4"},Mo={class:"grow mb-2"},zo={class:"text-base font-normal leading-6 text-gray-900 flex items-center gap-2"},Zo={class:"pt-2 pb-4 grid grid-cols-1 sm:grid-cols-2 gap-6"},Po={class:"relative"},Eo={key:0,class:"block truncate"},No={key:0,class:"bg-main-color-200 rounded-lg inline-block py-1 px-2 ml-2"},To={key:1,class:"block truncate text-gray-400"},Oo={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},Ro={key:0,class:"bg-main-color-200 rounded-lg inline-block py-1 px-2 ml-2"},Bo={key:0,class:"absolute inset-y-0 left-0 flex items-center pl-3 text-main-color-600"},Go={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},Lo={key:0,class:"p-5"},Fo={class:"flex justify-between items-center"},Ao={class:"flex items-center"},Ho={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},Do={class:"mt-6 pb-4 flex justify-center items-end gap-4"},Jo={class:"grow"},qo={class:"mt-2"},Xo={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block mb-6"},Wo={__name:"SystemSettings",setup(O){const Z=b(),h=b(),m=ue("debugModeGlobalVar"),s=b({}),x=b(!0),C=b({}),k=b({}),f=b({});b({}),b("");const S=b([]);b("");const N=b([]),p=b(""),r=b([]);te(async()=>{x.value=!0,await re(),await de(),await j(),await M(),w(),x.value=!1});async function j(){await $.get("/api/settings").then(d=>{s.value=d.data.data}).catch(d=>{console.log(d)})}function w(){!s.value||!f.value.length||!k.value.length||(p.value=k.value.find(d=>d.id==s.value.guest_default_organization_unit)||"",S.value=s.value.student_default_groups?s.value.student_default_groups.split(",").map(d=>f.value.find(t=>t.id==d)).filter(Boolean):[],N.value=s.value.employee_default_groups?s.value.employee_default_groups.split(",").map(d=>f.value.find(t=>t.id==d)).filter(Boolean):[],r.value=s.value.guest_default_groups?s.value.guest_default_groups.split(",").map(d=>f.value.find(t=>t.id==d)).filter(Boolean):[])}function g(){$.post("/api/settings/active-directory/check",{ldap_hosts:s.value.ldap_hosts,ldap_port:s.value.ldap_port,ldap_base_dn:s.value.ldap_base_dn,ldap_username:s.value.ldap_username,ldap_password:s.value.ldap_password,ldap_account_prefix:s.value.ldap_account_prefix,ldap_account_suffix:s.value.ldap_account_suffix,ldap_use_ssl:s.value.ldap_use_ssl,ldap_use_tls:s.value.ldap_use_tls}).then(d=>{z.success(d.data.message)}).catch(d=>{})}function c(){$.post("/api/settings/active-directory",{ldap_hosts:s.value.ldap_hosts,ldap_port:s.value.ldap_port,ldap_base_dn:s.value.ldap_base_dn,ldap_username:s.value.ldap_username,ldap_password:s.value.ldap_password,ldap_account_prefix:s.value.ldap_account_prefix,ldap_account_suffix:s.value.ldap_account_suffix,ldap_use_ssl:s.value.ldap_use_ssl,ldap_use_tls:s.value.ldap_use_tls,ldap_username_scheme:s.value.ldap_username_scheme}).then(d=>{z.success(d.data.message)}).catch(d=>{})}function u(){$.post("/api/settings/fortinet",{fortinet_ip:s.value.fortinet_ip,fortinet_token:s.value.fortinet_token}).then(d=>{z.success(d.data.message)}).catch(d=>{z.error("Změny se nepovedlo uložit")})}function U(){$.post("/api/settings/school-smtp",{school_mail_host:s.value.school_mail_host,school_mail_port:s.value.school_mail_port,school_mail_username:s.value.school_mail_username,school_mail_password:s.value.school_mail_password,school_mail_encryption:s.value.school_mail_encryption}).then(d=>{z.success(d.data.message)}).catch(d=>{z.error("Změny se nepovedlo uložit")})}function y(){$.post("/api/settings/gsm-gate",{gsm_gate_ip:s.value.gsm_gate_ip,gsm_gate_account:s.value.gsm_gate_account,gsm_gate_password:s.value.gsm_gate_password,gsm_gate_sim_port:s.value.gsm_gate_sim_port,gsm_send_sms:s.value.gsm_send_sms}).then(d=>{z.success(d.data.message)}).catch(d=>{z.error("Změny se nepovedlo uložit")})}function I(){$.post("/api/settings/mysql",{db_host:s.value.db_host,db_port:s.value.db_port,db_database:s.value.db_database,db_username:s.value.db_username,db_password:s.value.db_password}).then(d=>{z.success(d.data.message)}).catch(d=>{console.log(d),z.error("Změny se nepovedlo uložit")})}async function M(){await $.get("/api/fortinet/webfilters").then(d=>{C.value=d.data.data.webfilters}).catch(d=>{console.log(d)})}function D(){$.post("/api/settings/license/check",{license_key:s.value.license_key}).then(d=>{z.success(d.data.message)}).catch(d=>{})}function J(){$.post("/api/settings/license",{license_key:s.value.license_key}).then(d=>{z.success(d.data.message)}).catch(d=>{})}async function ie(){var d;await $.post("/api/settings/default-groups-ous",{student_default_groups:S.value.map(t=>t.id).join(","),employee_default_groups:N.value.map(t=>t.id).join(","),guest_default_organization_unit:((d=p.value)==null?void 0:d.id)||"",guest_default_groups:r.value.map(t=>t.id).join(",")}).then(t=>{z.success(t.data.message)}).catch(t=>{console.log(t),z.error("Chyba při ukládání dat")})}async function re(){await $.get("/api/organization-units?perpage=9999").then(d=>{k.value=d.data.data}).catch(d=>{console.log(d)})}async function de(){await $.get("/api/groups?perpage=9999").then(d=>{f.value=d.data.data}).catch(d=>{console.log(d)})}return(d,t)=>{const X=K("VueSpinner");return n(),i("div",null,[e("div",Vt,[e("div",$t,[e("div",null,[e("div",Ct,[x.value?(n(),i("div",Lt,[l(X,{class:"mx-auto text-spinner-color",size:"40"})])):(n(),i("div",St,[l(o(B),{onSubmit:t[6]||(t[6]=E=>I())},{default:_(({values:E})=>[e("div",jt,[e("div",Ut,[l(o(ye),{class:"w-7"}),t[43]||(t[43]=e("p",{class:"ml-4 text-lg text-gray-900"},"MySQL Databáze",-1))]),e("div",null,[o(P).check("backup.read")?(n(),i("button",{key:0,onClick:t[0]||(t[0]=F(a=>d.$refs.backupsRef.openModal(),["prevent"])),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"}," Zálohy ")):V("",!0),o(P).check("settings.edit")?(n(),i("button",It,"Uložit")):V("",!0)])]),e("div",Mt,[e("div",zt,[t[44]||(t[44]=e("label",{for:"db-username",class:"block text-sm font-normal leading-6 text-gray-900"},"Přihlašovací jméno:",-1)),e("div",Zt,[l(o(v),{modelValue:s.value.db_username,"onUpdate:modelValue":t[1]||(t[1]=a=>s.value.db_username=a),id:"db-username",name:"db-username",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte přihlašovací jméno k MySQL databázi..."},null,8,["modelValue"])])]),e("div",Pt,[t[45]||(t[45]=e("label",{for:"db-password",class:"block text-sm font-normal leading-6 text-gray-900"},"Heslo:",-1)),e("div",Et,[l(o(v),{modelValue:s.value.db_password,"onUpdate:modelValue":t[2]||(t[2]=a=>s.value.db_password=a),id:"db-password",name:"db-password",type:"password",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte heslo k MySQL databázi..."},null,8,["modelValue"])])]),e("div",Nt,[t[46]||(t[46]=e("label",{for:"db-database",class:"block text-sm font-normal leading-6 text-gray-900"},"Název databáze:",-1)),e("div",Tt,[l(o(v),{modelValue:s.value.db_database,"onUpdate:modelValue":t[3]||(t[3]=a=>s.value.db_database=a),id:"db-database",name:"db-database",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte název databáze..."},null,8,["modelValue"])])]),e("div",Ot,[t[47]||(t[47]=e("label",{for:"db-host",class:"block text-sm font-normal leading-6 text-gray-900"},"Host:",-1)),e("div",Rt,[l(o(v),{modelValue:s.value.db_host,"onUpdate:modelValue":t[4]||(t[4]=a=>s.value.db_host=a),id:"db-host",name:"db-host",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte host MySQL databáze..."},null,8,["modelValue"])])]),e("div",Bt,[t[48]||(t[48]=e("label",{for:"db-port",class:"block text-sm font-normal leading-6 text-gray-900"},"Port:",-1)),e("div",Gt,[l(o(v),{modelValue:s.value.db_port,"onUpdate:modelValue":t[5]||(t[5]=a=>s.value.db_port=a),id:"db-port",name:"db-port",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte port MySQL databáze..."},null,8,["modelValue"])])])])]),_:1})]))]),e("div",Ft,[l(o(B),{onSubmit:t[12]||(t[12]=E=>U())},{default:_(({values:E})=>[x.value?(n(),i("div",as,[l(X,{class:"mx-auto text-spinner-color",size:"40"})])):(n(),i("div",At,[e("div",Ht,[e("div",Dt,[l(o(he),{class:"w-7"}),t[49]||(t[49]=e("p",{class:"ml-4 text-lg text-gray-900"},"SMTP",-1))]),e("div",null,[o(P).check("settings.edit")?(n(),i("button",Jt,"Uložit")):V("",!0)])]),e("div",qt,[e("div",Xt,[t[50]||(t[50]=e("label",{for:"school-mail-host",class:"block text-sm font-normal leading-6 text-gray-900"},"Host:",-1)),e("div",Wt,[l(o(v),{modelValue:s.value.school_mail_host,"onUpdate:modelValue":t[7]||(t[7]=a=>s.value.school_mail_host=a),id:"school-mail-host",name:"school-mail-host",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte host k SMTP serveru..."},null,8,["modelValue"])])]),e("div",Qt,[t[51]||(t[51]=e("label",{for:"school-mail-port",class:"block text-sm font-normal leading-6 text-gray-900"},"Port:",-1)),e("div",Kt,[l(o(v),{modelValue:s.value.school_mail_port,"onUpdate:modelValue":t[8]||(t[8]=a=>s.value.school_mail_port=a),id:"school-mail-port",name:"school-mail-port",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte port k SMTP serveru..."},null,8,["modelValue"])])]),e("div",Yt,[t[52]||(t[52]=e("label",{for:"school-mail-username",class:"block text-sm font-normal leading-6 text-gray-900"},"Přihlašovací jméno:",-1)),e("div",es,[l(o(v),{modelValue:s.value.school_mail_username,"onUpdate:modelValue":t[9]||(t[9]=a=>s.value.school_mail_username=a),id:"school-mail-username",name:"school-mail-username",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte přihlašovací jméno..."},null,8,["modelValue"])])]),e("div",ts,[t[53]||(t[53]=e("label",{for:"school-mail-password",class:"block text-sm font-normal leading-6 text-gray-900"},"Heslo:",-1)),e("div",ss,[l(o(v),{modelValue:s.value.school_mail_password,"onUpdate:modelValue":t[10]||(t[10]=a=>s.value.school_mail_password=a),id:"school-mail-password",name:"school-mail-password",type:"password",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte heslo..."},null,8,["modelValue"])])]),e("div",os,[t[54]||(t[54]=e("label",{for:"school-mail-encryption",class:"block text-sm font-normal leading-6 text-gray-900"},"Šifrování:",-1)),e("div",ls,[l(o(v),{modelValue:s.value.school_mail_encryption,"onUpdate:modelValue":t[11]||(t[11]=a=>s.value.school_mail_encryption=a),id:"school-mail-encryption",name:"school-mail-encryption",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte šifrování (tls)..."},null,8,["modelValue"])])])])]))]),_:1})]),e("div",ns,[l(o(B),{onSubmit:t[18]||(t[18]=E=>y())},{default:_(({values:E})=>[x.value?(n(),i("div",Vs,[l(X,{class:"mx-auto text-spinner-color",size:"40"})])):(n(),i("div",is,[e("div",rs,[e("div",ds,[l(o(ke),{class:"w-7"}),t[55]||(t[55]=e("p",{class:"ml-4 text-lg text-gray-900"},"Gsm brána",-1))]),e("div",null,[o(P).check("settings.edit")?(n(),i("button",us,"Uložit")):V("",!0)])]),e("div",cs,[e("div",ms,[t[56]||(t[56]=e("label",{for:"gsm-gate-ip",class:"block text-sm font-normal leading-6 text-gray-900"},"Ip adresa:",-1)),e("div",ps,[l(o(v),{modelValue:s.value.gsm_gate_ip,"onUpdate:modelValue":t[13]||(t[13]=a=>s.value.gsm_gate_ip=a),id:"dgsm-gate-ip",name:"gsm-gate-ip",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte IP adresu GSM brány..."},null,8,["modelValue"])])]),e("div",gs,[t[57]||(t[57]=e("label",{for:"gsm-gate-sim-port",class:"block text-sm font-normal leading-6 text-gray-900"},"Sim port:",-1)),e("div",vs,[l(o(v),{modelValue:s.value.gsm_gate_sim_port,"onUpdate:modelValue":t[14]||(t[14]=a=>s.value.gsm_gate_sim_port=a),id:"gsm-gate-sim-port",name:"gsm-gate-sim-port",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte port SIM karty, který se má používat..."},null,8,["modelValue"])])]),e("div",bs,[t[58]||(t[58]=e("label",{for:"gsm-gate-account",class:"block text-sm font-normal leading-6 text-gray-900"},"Uživatelské jméno:",-1)),e("div",fs,[l(o(v),{modelValue:s.value.gsm_gate_account,"onUpdate:modelValue":t[15]||(t[15]=a=>s.value.gsm_gate_account=a),id:"gsm-gate-account",name:"gsm-gate-account",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte uživatelské jméno k GSM bráně..."},null,8,["modelValue"])])]),e("div",_s,[t[59]||(t[59]=e("label",{for:"gsm-gate-password",class:"block text-sm font-normal leading-6 text-gray-900"},"Heslo:",-1)),e("div",xs,[l(o(v),{modelValue:s.value.gsm_gate_password,"onUpdate:modelValue":t[16]||(t[16]=a=>s.value.gsm_gate_password=a),id:"gsm-gate-password",name:"gsm-gate-password",type:"password",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte heslo k GSM bráně...."},null,8,["modelValue"])])]),e("div",ys,[t[61]||(t[61]=e("label",{for:"",class:"block text-sm font-normal leading-6 text-gray-900"},"Ostatní:",-1)),e("div",hs,[e("div",ks,[e("div",ws,[l(o(v),{modelValue:s.value.gsm_send_sms,"onUpdate:modelValue":t[17]||(t[17]=a=>s.value.gsm_send_sms=a),value:!s.value.gsm_send_sms,id:"gsm-send-sms",name:"gsm-send-sms",type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"])]),t[60]||(t[60]=e("div",{class:"text-sm leading-6"},[e("label",{for:"gsm-send-sms",class:"font-medium text-gray-900"},"Posílání SMS přes definouvanou GSM bránu")],-1))])])])])]))]),_:1})]),e("div",$s,[l(o(B),{onSubmit:t[21]||(t[21]=E=>u())},{default:_(({values:E})=>[x.value?(n(),i("div",Rs,[l(X,{class:"mx-auto text-spinner-color",size:"40"})])):(n(),i("div",Cs,[e("div",Ss,[e("div",js,[e("div",Us,[l(o(we),{class:"w-7"}),t[62]||(t[62]=e("p",{class:"ml-4 text-lg text-gray-900"},"Fortinet",-1))]),e("div",null,[o(P).check("settings.edit")?(n(),i("button",Is,"Uložit")):V("",!0)])]),e("div",Ms,[e("div",zs,[t[63]||(t[63]=e("label",{for:"fortinet-ip",class:"block text-sm font-normal leading-6 text-gray-900"},"IP adresa:",-1)),e("div",Zs,[l(o(v),{modelValue:s.value.fortinet_ip,"onUpdate:modelValue":t[19]||(t[19]=a=>s.value.fortinet_ip=a),id:"fortinet-ip",name:"fortinet-ip",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte IP adresu fortinet..."},null,8,["modelValue"])])]),e("div",Ps,[t[64]||(t[64]=e("label",{for:"fortinet-token",class:"block text-sm font-normal leading-6 text-gray-900"},"Token:",-1)),e("div",Es,[l(o(v),{modelValue:s.value.fortinet_token,"onUpdate:modelValue":t[20]||(t[20]=a=>s.value.fortinet_token=a),id:"fortinet-token",name:"fortinet-token",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte přístupový token..."},null,8,["modelValue"])])])])]),e("div",Ns,[t[65]||(t[65]=e("span",{class:"text-sm font-semibold inline-block pb-4"},"Skupiny blokace nevhodných stránek:",-1)),e("div",Ts,[(n(!0),i(G,null,A(C.value,a=>(n(),i("div",{key:a,class:"flex items-center gap-5"},[o(P).check("settings.edit")?(n(),i("button",{key:0,onClick:F(W=>d.$refs.webFiltersRef.openModal(a),["prevent"]),class:"rounded-md bg-main-color-200/75 w-8 h-8 flex justify-center items-center text-main-color-600 shadow-sm hover:bg-main-color-200"},[l(o(Ve),{class:"h-4 w-4 text-main-color-600","aria-hidden":"true"})],8,Os)):V("",!0),e("span",null,T(a),1)]))),128))])])]))]),_:1})])]),e("div",null,[e("div",Bs,[l(o(B),{onSubmit:t[33]||(t[33]=E=>c())},{default:_(({values:E})=>[x.value?(n(),i("div",bo,[l(X,{class:"mx-auto text-spinner-color",size:"40"})])):(n(),i("div",Gs,[e("div",Ls,[e("div",Fs,[l(o($e),{class:"w-7"}),t[66]||(t[66]=e("p",{class:"ml-4 text-lg text-gray-900"},"Active Directory",-1))]),e("div",null,[e("button",{onClick:t[22]||(t[22]=F(a=>g(),["prevent"])),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},t[67]||(t[67]=[e("span",null,"Otestovat připojení",-1)])),o(P).check("settings.edit")?(n(),i("button",As,"Uložit")):V("",!0)])]),e("div",Hs,[e("div",Ds,[t[68]||(t[68]=e("label",{for:"ldap-username",class:"block text-sm font-normal leading-6 text-gray-900"},"Přihlašovací jméno:",-1)),e("div",Js,[l(o(v),{modelValue:s.value.ldap_username,"onUpdate:modelValue":t[23]||(t[23]=a=>s.value.ldap_username=a),id:"ldap-username",name:"ldap-username",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte přihlašovací jméno k active directory..."},null,8,["modelValue"])])]),e("div",qs,[t[69]||(t[69]=e("label",{for:"ldap-password",class:"block text-sm font-normal leading-6 text-gray-900"},"Heslo:",-1)),e("div",Xs,[l(o(v),{modelValue:s.value.ldap_password,"onUpdate:modelValue":t[24]||(t[24]=a=>s.value.ldap_password=a),id:"ldap-password",name:"ldap-password",type:"password",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte heslo k active directory..."},null,8,["modelValue"])])]),e("div",Ws,[t[70]||(t[70]=e("label",{for:"ldap-account-prefix",class:"block text-sm font-normal leading-6 text-gray-900"},"Prefix:",-1)),e("div",Qs,[l(o(v),{modelValue:s.value.ldap_account_prefix,"onUpdate:modelValue":t[25]||(t[25]=a=>s.value.ldap_account_prefix=a),id:"ldap-account-prefix",name:"ldap-account-prefix",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte prefix..."},null,8,["modelValue"])])]),e("div",Ks,[t[71]||(t[71]=e("label",{for:"ldap-account-suffix",class:"block text-sm font-normal leading-6 text-gray-900"},"Suffix:",-1)),e("div",Ys,[l(o(v),{modelValue:s.value.ldap_account_suffix,"onUpdate:modelValue":t[26]||(t[26]=a=>s.value.ldap_account_suffix=a),id:"ldap-account-suffix",name:"ldap-account-suffix",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte suffix..."},null,8,["modelValue"])])]),e("div",eo,[t[72]||(t[72]=e("label",{for:"ldap-hosts",class:"block text-sm font-normal leading-6 text-gray-900"},"Host:",-1)),e("div",to,[l(o(v),{modelValue:s.value.ldap_hosts,"onUpdate:modelValue":t[27]||(t[27]=a=>s.value.ldap_hosts=a),id:"ldap-hosts",name:"ldap-hosts",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte hosts na službu active directory..."},null,8,["modelValue"])])]),e("div",so,[t[73]||(t[73]=e("label",{for:"ldap-port",class:"block text-sm font-normal leading-6 text-gray-900"},"Port:",-1)),e("div",oo,[l(o(v),{modelValue:s.value.ldap_port,"onUpdate:modelValue":t[28]||(t[28]=a=>s.value.ldap_port=a),id:"ldap-port",name:"ldap-port",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte port na službu active directory"},null,8,["modelValue"])])]),e("div",lo,[t[74]||(t[74]=e("label",{for:"ldap-base-dn",class:"block text-sm font-normal leading-6 text-gray-900"},"Base DN:",-1)),e("div",ao,[l(o(v),{modelValue:s.value.ldap_base_dn,"onUpdate:modelValue":t[29]||(t[29]=a=>s.value.ldap_base_dn=a),id:"ldap-base-dn",name:"ldap-base-dn",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte base dn služby active directory..."},null,8,["modelValue"])])]),e("div",no,[t[77]||(t[77]=e("label",{for:"",class:"block text-sm font-normal leading-6 text-gray-900"},"Protokoly:",-1)),e("div",io,[e("div",ro,[e("div",uo,[l(o(v),{modelValue:s.value.ldap_use_ssl,"onUpdate:modelValue":t[30]||(t[30]=a=>s.value.ldap_use_ssl=a),value:!s.value.ldap_use_ssl,id:"ldap-use-ssl",name:"ldap-use-ssl",type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"])]),t[75]||(t[75]=e("div",{class:"text-sm leading-6"},[e("label",{for:"ldap-use-ssl",class:"font-medium text-gray-900"},"SSL")],-1))]),e("div",co,[e("div",mo,[l(o(v),{modelValue:s.value.ldap_use_tls,"onUpdate:modelValue":t[31]||(t[31]=a=>s.value.ldap_use_tls=a),value:!s.value.ldap_use_tls,id:"ldap-use-tls",name:"ldap-use-tls",type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"])]),t[76]||(t[76]=e("div",{class:"text-sm leading-6"},[e("label",{for:"ldap-use-tls",class:"font-medium text-gray-900"},"TLS")],-1))])])])]),e("div",po,[e("div",go,[t[78]||(t[78]=e("label",{for:"ldap-username-scheme",class:"block text-sm font-normal leading-6 text-gray-900"},"Schéma přihlašovacího jména:",-1)),e("div",vo,[l(o(v),{modelValue:s.value.ldap_username_scheme,"onUpdate:modelValue":t[32]||(t[32]=a=>s.value.ldap_username_scheme=a),id:"ldap-username-scheme",name:"ldap-username-scheme",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte přihlašovací jméno k active directory..."},null,8,["modelValue"])]),t[79]||(t[79]=e("div",null,[e("span",{class:"text-sm font-semibold inline-block pb-4"},"Dostupné možnosti jsou:"),e("ul",{class:"list-disc ml-6 text-sm space-y-1 mb-4"},[e("li",null,[e("span",null,"{JMENO} a {PRIJMENI} - dosadí celé jméno nebo příjmení")]),e("li",null,[e("span",null,"{PRVNI.PISMENO.JMENO} a {PRVNI.PISMENO.PRIJMENI} - dosadí první písmeno ze jména nebo příjmení")]),e("li",null,[e("span",null,"{JMENO_X} a {PRIJMENI_X}"),R(" - dosadí X (počet písmen) ze jména nebo příjmení")])]),e("span",{class:"text-sm font-semibold inline-block pb-4"},"Příklady použití:"),e("ul",{class:"list-disc ml-6 text-sm space-y-1"},[e("li",null,[R("{JMENO}.{PRIJMENI} = "),e("span",null,"petr.novak")]),e("li",null,[R("{PRIJMENI}{PRVNI.PISMENO.JMENO} = "),e("span",null,"novakp")]),e("li",null,[R("{JMENO_2}.{PRIJMENI_3} = "),e("span",null,"pe.nov")])])],-1))])])]))]),_:1})]),e("div",fo,[l(o(B),{onSubmit:t[39]||(t[39]=E=>ie())},{default:_(({values:E})=>[x.value?V("",!0):(n(),i("div",_o,[e("div",xo,[e("div",yo,[l(o(Ce),{class:"w-7"}),t[80]||(t[80]=e("p",{class:"ml-4 text-lg text-gray-900"},"Výchozí nastavení typových rolí",-1))]),e("div",null,[o(P).check("settings.edit")?(n(),i("button",ho,"Uložit")):V("",!0)])]),e("div",ko,[e("div",wo,[e("div",Vo,[l(o(Se),{class:"w-6 h-6"}),t[81]||(t[81]=e("span",null,"Žák",-1))])]),e("div",$o,[e("div",null,[t[82]||(t[82]=e("span",{class:"text-sm font-normal leading-6 text-gray-900 pb-2 inline-block"},"Skupiny:",-1)),l(o(v),{name:"selectedStudentGroups"},{default:_(({handleChange:a,value:W})=>[l(o(Y),{name:"selectedStudentGroups",modelValue:S.value,"onUpdate:modelValue":[t[34]||(t[34]=L=>S.value=L),a],mode:"tags",label:"name","value-prop":"id",options:f.value,object:!0,placeholder:"Zvolte si skupiny",class:"!rounded-lg !text-sm"},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1}),l(o(q),{name:"selectedStudentGroups",class:"text-rose-400 text-sm block pt-1"})])])]),e("div",Co,[e("div",So,[e("div",jo,[l(o(je),{class:"w-6 h-6"}),t[83]||(t[83]=e("span",null,"Zaměstnanec",-1))])]),e("div",Uo,[e("div",null,[t[84]||(t[84]=e("span",{class:"text-sm font-normal leading-6 text-gray-900 pb-2 inline-block"},"Skupiny:",-1)),l(o(v),{name:"selectedEmployeeGroups"},{default:_(({handleChange:a,value:W})=>[l(o(Y),{name:"selectedEmployeeGroups",modelValue:N.value,"onUpdate:modelValue":[t[35]||(t[35]=L=>N.value=L),a],mode:"tags",label:"name","value-prop":"id",options:f.value,object:!0,placeholder:"Zvolte si skupiny",class:"!rounded-lg !text-sm"},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1}),l(o(q),{name:"selectedEmployeeGroups",class:"text-rose-400 text-sm block pt-1"})])])]),e("div",Io,[e("div",Mo,[e("div",zo,[l(o(Ue),{class:"w-6 h-6"}),t[85]||(t[85]=e("span",null,"Host",-1))])]),e("div",Zo,[e("div",null,[t[88]||(t[88]=e("span",{class:"text-sm font-normal leading-6 text-gray-900 pb-2 inline-block"},"Organizační jednotka:",-1)),l(o(Be),{modelValue:p.value,"onUpdate:modelValue":t[37]||(t[37]=a=>p.value=a)},{default:_(()=>[e("div",Po,[l(o(Ge),{class:"relative w-full text-left cursor-pointer block rounded-lg border-0 h-[42px] px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},{default:_(()=>[p.value&&p.value.name?(n(),i("span",Eo,[e("div",null,[R(T(p.value.name)+" ",1),p.value.is_class?(n(),i("span",No,t[86]||(t[86]=[e("span",{class:"flex items-center"},[e("span",{class:"text-xs text-main-color-700 font-semibold"},"Třída")],-1)]))):V("",!0)])])):(n(),i("span",To,"Zvolte OU")),e("span",Oo,[l(o(Oe),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]),_:1}),l(o(v),{modelValue:p.value,"onUpdate:modelValue":t[36]||(t[36]=a=>p.value=a),name:"selectedHostOu",rules:"required",class:"hidden"},null,8,["modelValue"]),l(o(q),{name:"selectedHostOu",class:"text-rose-400 text-sm block pt-1"}),l(oe,{"leave-active-class":"transition duration-100 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:_(()=>[k.value&&k.value.length?(n(),H(o(Le),{key:0,class:"absolute z-20 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:_(()=>[(n(!0),i(G,null,A(k.value,a=>(n(),H(o(Fe),{key:a.name,value:a,as:"template"},{default:_(({active:W,selected:L})=>[e("li",{class:Q([W?"bg-main-color-100 text-main-color-900":"text-gray-900","relative cursor-pointer select-none py-2 pl-10 pr-4"])},[e("span",{class:Q([L?"font-medium":"font-normal","block truncate"])},[e("div",null,[R(T(a.name)+" ",1),a.is_class?(n(),i("span",Ro,t[87]||(t[87]=[e("span",{class:"flex items-center"},[e("span",{class:"text-xs text-main-color-700 font-semibold"},"Třída")],-1)]))):V("",!0)])],2),L?(n(),i("span",Bo,[l(o(Re),{class:"h-5 w-5","aria-hidden":"true"})])):V("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})):V("",!0)]),_:1})])]),_:1},8,["modelValue"])]),e("div",null,[t[89]||(t[89]=e("span",{class:"text-sm font-normal leading-6 text-gray-900 pb-2 inline-block"},"Skupiny:",-1)),l(o(v),{name:"selectedHostGroups"},{default:_(({handleChange:a,value:W})=>[l(o(Y),{name:"selectedHostGroups",modelValue:r.value,"onUpdate:modelValue":[t[38]||(t[38]=L=>r.value=L),a],mode:"tags",label:"name","value-prop":"id",options:f.value,object:!0,placeholder:"Zvolte si skupiny",class:"!rounded-lg !text-sm"},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1}),l(o(q),{name:"selectedHostGroups",class:"text-rose-400 text-sm block pt-1"})])])])]))]),_:1})]),e("div",Go,[l(o(B),{onSubmit:t[42]||(t[42]=E=>J())},{default:_(({values:E})=>[x.value?V("",!0):(n(),i("div",Lo,[e("div",Fo,[e("div",Ao,[l(o(Ie),{class:"w-7"}),t[90]||(t[90]=e("p",{class:"ml-4 text-lg text-gray-900"},"Licence",-1))]),e("div",null,[o(P).check("settings.edit")?(n(),i("button",Ho,"Uložit")):V("",!0)])]),e("div",Do,[e("div",Jo,[t[91]||(t[91]=e("label",{for:"licence_key",class:"block text-sm font-normal leading-6 text-gray-900"},"Licenční klíč:",-1)),e("div",qo,[l(o(v),{modelValue:s.value.license_key,"onUpdate:modelValue":t[40]||(t[40]=a=>s.value.license_key=a),id:"licence_key",name:"licence_key",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte licenční klíč..."},null,8,["modelValue"])])]),e("div",null,[o(P).check("settings.edit")?(n(),i("button",{key:0,onClick:t[41]||(t[41]=F(a=>D(),["prevent"])),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"}," Ověřit ")):V("",!0)])])]))]),_:1})])])])]),o(m)?(n(),i("div",Xo,[R(T(s.value)+" ",1),t[92]||(t[92]=e("br",null,null,-1)),t[93]||(t[93]=e("br",null,null,-1)),e("span",null,"webfilters: "+T(C.value),1)])):V("",!0),l(mt,{ref_key:"webFiltersRef",ref:Z,onReloadWebFilters:M},null,512),l(wt,{ref_key:"backupsRef",ref:h},null,512)])}}},Qo={key:0,class:"bg-white border border-zinc-200/70 rounded-md p-5 pb-8"},Ko={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3"},Yo={class:"flex items-center"},el={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},tl={class:"grid grid-cols-1 sm:grid-cols-2 gap-x-6 xl:gap-x-10 gap-y-6 mt-8"},sl={class:"col-span-2"},ol={class:"flex justify-between items-center"},ll=["for"],al={class:"mt-2"},nl={__name:"CustomColumnsSettings",emits:["reloadTable"],setup(O,{emit:Z}){const h=b();b();const m=b(!1),s=Z;te(()=>{m.value=!0,x()});function x(){$.get("/api/settings/property/custom-columns").then(f=>{h.value=f.data.data,m.value=!1}).catch(f=>{console.log(f)})}async function C(){try{const f=await $.get("/api/settings/property/custom-columns");ce().setCustomColumns(f.data.data)}catch(f){console.error("Chyba při načítání vlastních sloupců:",f)}}async function k(){try{const f=await $.post("/api/settings/property/custom-columns",{custom_columns:JSON.stringify(h.value)});z.success(f.data.message),await C(),s("reloadTable",!0),m.value=!1}catch(f){console.log(f)}}return(f,S)=>{const N=K("VueSpinner");return n(),H(o(B),{onSubmit:S[0]||(S[0]=p=>k())},{default:_(({values:p})=>{var r;return[m.value?(n(),H(N,{key:1,class:"mx-auto text-spinner-color",size:"40"})):(n(),i("div",Qo,[e("div",Ko,[e("div",Yo,[l(o(Me),{class:"w-7"}),S[1]||(S[1]=e("p",{class:"ml-4 text-lg text-gray-900"},"Vlastní sloupce",-1))]),e("div",null,[o(P).check("settings.edit")?(n(),i("button",el,"Uložit")):V("",!0)])]),e("div",tl,[(r=h.value)!=null&&r.length?(n(!0),i(G,{key:0},A(h.value,(j,w)=>(n(),i("div",{key:j.id},[e("div",sl,[e("div",ol,[e("label",{for:"custom_column"+w,class:"block text-sm font-normal leading-6 text-gray-900 pb-1"},T("Vlastní sloupec "+(w+1)),9,ll),e("div",null,[l(o(Ae),{onClick:F(g=>j.enable=!j.enable,["prevent"]),class:Q([j.enable?"bg-green-500":"bg-red-500","relative inline-flex h-[20px] w-[40px] shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75"])},{default:_(()=>[S[2]||(S[2]=e("span",{class:"sr-only"},"Use setting",-1)),e("span",{"aria-hidden":"true",class:Q([j.enable?"translate-x-5":"translate-x-0","pointer-events-none inline-block h-[16px] w-[16px] transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out"])},null,2)]),_:2},1032,["onClick","class"])])]),e("div",al,[l(o(v),{modelValue:h.value[w].custom_name,"onUpdate:modelValue":g=>h.value[w].custom_name=g,type:"text",name:"custom_column"+w,id:"custom_column"+w,class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte vlastní název sloupce "+(w+1)+"..."},null,8,["modelValue","onUpdate:modelValue","name","id","placeholder"]),l(o(q),{name:"custom_column"+w,class:"text-rose-400 text-sm block pt-1"},null,8,["name"])])])]))),128)):V("",!0)])]))]}),_:1})}}},{createElementVNode:il,openBlock:rl,createElementBlock:dl}=ze;var ul=function(Z,h){return rl(),dl("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true"},[il("path",{"fill-rule":"evenodd",d:"M12 1.5a5.25 5.25 0 00-5.25 5.25v3a3 3 0 00-3 3v6.75a3 3 0 003 3h10.5a3 3 0 003-3v-6.75a3 3 0 00-3-3v-3c0-2.9-2.35-5.25-5.25-5.25zm3.75 8.25v-3a3.75 3.75 0 10-7.5 0v3h7.5z","clip-rule":"evenodd"})])},cl=ul;const ml={key:0,class:"bg-white border border-zinc-200/70 rounded-md p-5 pb-8"},pl={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3"},gl={class:"flex items-center"},vl={class:"grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-6 mt-8"},bl={class:"bg-gray-100 p-4 rounded-xl"},fl={class:"text-gray-700 text-sm"},_l=["onClick"],xl={class:"bg-gray-100 p-4 rounded-xl"},yl={class:"text-gray-700 text-sm"},hl={key:0,class:"flex items-center gap-1"},kl=["onClick"],wl=["onClick"],Vl=["onClick"],$l={key:1},Cl={__name:"PropertyTableSettings",props:{reload:Number},setup(O){const Z=O,h={barcodes:{active:!0,position:1,locked:!0,custom_name:"Čárový kód"},evidence_number:{active:!0,position:2,locked:!0,custom_name:"Ev. číslo"},invoice_number:{active:!0,position:3,locked:!1,custom_name:"Číslo faktury"},name:{active:!0,position:4,locked:!1,custom_name:"Název položky"},price:{active:!0,position:5,locked:!1,custom_name:"Cena za kus"},buyed_at:{active:!0,position:6,locked:!1,custom_name:"Datum zakoupení"},room:{active:!0,position:7,locked:!1,custom_name:"Přiřazená třída"},user:{active:!0,position:8,locked:!1,custom_name:"Přiřazený uživatel"},state:{active:!0,position:9,locked:!0,custom_name:"Stav"},actions:{active:!0,position:10,locked:!0,custom_name:"Akce"}},m=b(h);b();const s=b(!1);te(async()=>{s.value=!0,await r(),await j(),s.value=!1}),me(()=>Z.reload,c=>{c&&(s.value=!0,(async()=>(console.log(c),await r(),await j(),await $.post("/api/settings/custom-table-settings/property-list",{settings:m.value}).then(u=>{}).catch(u=>{console.log(u)}),s.value=!1))())});function x(c){if(m.value[c]){if(!m.value[c].active){let u=p.value.map(y=>y.position).sort((y,I)=>y-I),U;u.length>=2?U=u[Math.max(0,u.length-2)]:U=u.length+1,Object.entries(m.value).forEach(([y,I])=>{I.active&&I.position>=U&&(I.position+=1)}),m.value[c].position=U}m.value[c].active=!m.value[c].active,C()}}function C(){p.value.sort((u,U)=>u.position-U.position).forEach((u,U)=>{m.value[u.name].position=U+1})}function k(c){const u=p.value.findIndex(U=>U.name===c);u>0&&S(u,u-1)}function f(c){const u=p.value.findIndex(U=>U.name===c);u<p.value.length-1&&S(u,u+1)}function S(c,u){const U=p.value[c],y=p.value[u],I=U.position;m.value[U.name].position=y.position,m.value[y.name].position=I}const N=ee(()=>Object.entries(m.value||{}).filter(([c,u])=>!u.active).map(([c,u])=>({name:c,...u}))),p=ee(()=>Object.entries(m.value||{}).filter(([c,u])=>u.active).map(([c,u])=>({name:c,...u})).sort((c,u)=>(c.position??1/0)-(u.position??1/0)));async function r(){await $.get("/api/settings/custom-table-settings/property-list").then(c=>{var u;(u=c==null?void 0:c.data)!=null&&u.data&&(m.value=JSON.parse(c.data.data))}).catch(c=>{console.log(c)})}async function j(){try{const u=(await $.get("/api/settings/property/custom-columns")).data.data,U=Object.fromEntries(Object.entries(u).map(([I,M])=>{var D,J;return!isNaN(I)&&M.name?[M.name,{custom_name:M.custom_name,enable:M.enable,active:((D=m.value[M.name])==null?void 0:D.active)??M.active,position:((J=m.value[M.name])==null?void 0:J.position)??M.position}]:[I,M]})),y=Object.fromEntries(Object.entries(U).filter(([I,M])=>M.enable));m.value=Object.fromEntries(Object.entries({...m.value,...y}).filter(([I,M])=>{const D=u.find(J=>J.name===I);return!(D&&M.enable===1&&D.enable===0)}))}catch(c){console.error(c)}}async function w(){s.value=!0,m.value=h,await j(),await g(),s.value=!1}async function g(){await $.post("/api/settings/custom-table-settings/property-list",{settings:m.value}).then(c=>{s.value=!1,z.success(c.data.message)}).catch(c=>{console.log(c)})}return(c,u)=>{const U=K("VueSpinner");return n(),i("div",null,[s.value?(n(),H(U,{key:1,class:"mx-auto text-spinner-color",size:"40"})):(n(),i("div",ml,[e("div",pl,[e("div",gl,[l(o(Ze),{class:"w-7"}),u[2]||(u[2]=e("p",{class:"ml-4 text-lg text-gray-900"},"Nastavení tabulky",-1))]),e("div",null,[o(P).check("settings.edit")?(n(),i("button",{key:0,onClick:u[0]||(u[0]=y=>w()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},"Resetovat pořadí")):V("",!0),o(P).check("settings.edit")?(n(),i("button",{key:1,onClick:u[1]||(u[1]=y=>g()),class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},"Uložit")):V("",!0)])]),e("div",vl,[e("div",null,[u[3]||(u[3]=e("p",{class:"text-center pb-3 font-semibold"},"Skryté sloupce",-1)),e("div",bl,[e("div",null,[l(se,{name:"fade",tag:"div",class:"space-y-3"},{default:_(()=>[(n(!0),i(G,null,A(N.value,(y,I)=>(n(),i("div",{key:y.name,class:"flex justify-between items-center bg-white p-3 rounded-xl"},[e("p",fl,T(y.custom_name||y.name),1),e("button",{onClick:M=>x(y.name)},[l(o(Pe),{class:"h-7 w-7 text-green-700 bg-green-200/60 hover:bg-green-200 duration-150 p-1 rounded-lg","aria-hidden":"true"})],8,_l)]))),128))]),_:1})])])]),e("div",null,[u[4]||(u[4]=e("p",{class:"text-center pb-3 font-semibold"},"Viditelné sloupce",-1)),e("div",xl,[l(se,{name:"fade",tag:"div",class:"space-y-3"},{default:_(()=>[(n(!0),i(G,null,A(p.value,(y,I)=>(n(),i("div",{key:y.name,class:"flex justify-between items-center bg-white p-3 rounded-xl duration-300"},[e("p",yl,T(y.custom_name||y.name),1),y.locked?(n(),i("div",$l,[l(o(cl),{class:"h-7 w-7 text-gray-600 duration-150 p-1 rounded-lg","aria-hidden":"true"})])):(n(),i("div",hl,[I>0&&!p.value[I-1].locked?(n(),i("button",{key:0,onClick:M=>k(y.name),class:"flex items-center justify-center text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 rounded-lg w-7 h-7"},[l(o(Ee),{class:"h-6 w-6 duration-150 p-1","aria-hidden":"true"})],8,kl)):V("",!0),I<p.value.length-1&&!p.value[I+1].locked?(n(),i("button",{key:1,onClick:M=>f(y.name),class:"flex items-center justify-center text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 rounded-lg w-7 h-7"},[l(o(Ne),{class:"h-6 w-6 duration-150 p-1","aria-hidden":"true"})],8,wl)):V("",!0),e("button",{onClick:M=>x(y.name)},[l(o(Te),{class:"h-7 w-7 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-1 rounded-lg","aria-hidden":"true"})],8,Vl)]))]))),128))]),_:1})])])])]))])}}},Sl={class:"grid grid-cols-1 xl:grid-cols-2 gap-6"},jl={__name:"PropertySettings",setup(O){const Z=b(0),h=()=>{Z.value=Z.value+1};return(m,s)=>(n(),i("div",Sl,[l(nl,{onReloadTable:h}),l(Cl,{reload:Z.value},null,8,["reload"])]))}};const Ul={__name:"Settings",setup(O){const Z=b(["skolasys-root","settings"]),h=b("system-settings"),m=b(1);function s(C){m.value=m.value+1,console.log(m.value),h.value=C}const x=ee(()=>h.value==="system-settings"?Wo:jl);return(C,k)=>(n(),i(G,null,[l(ge,{breadCrumbs:Z.value},{topbarButtons:_(()=>k[0]||(k[0]=[])),_:1},8,["breadCrumbs"]),l(We,{onTab:s}),l(oe,{name:"page-slide",mode:"out-in"},{default:_(()=>[(n(),H(pe(x.value),{key:m.value}))]),_:1})],64))}},Dl=He(Ul,[["__scopeId","data-v-f0b3a01d"]]);export{Dl as default};
