import{U as u,Y as m,q as f}from"./dialog-a0a12ec2.js";import{a as p,D as v,o as x,c as y,w as t,e as s,u as l,d as a,n as _,U as i}from"./index-04cb9130.js";import{h as r}from"./transition-14eed2e8.js";const w={class:"fixed inset-0 overflow-y-auto lg:pl-72"},g={class:"flex min-h-full items-center justify-center p-4 text-center"},h={class:"flex justify-between items-center border-b p-5"},q={__name:"basicModal",props:{size:{type:String,required:!1}},setup(d){const e=p(""),c=d;return v(()=>{e.value=c.size}),(o,n)=>(x(),y(l(f),{as:"div",class:"relative z-10"},{default:t(()=>[s(l(r),{as:"template",enter:"duration-300 ease-out","enter-from":"opacity-0","enter-to":"opacity-100",leave:"duration-200 ease-in","leave-from":"opacity-100","leave-to":"opacity-0"},{default:t(()=>n[0]||(n[0]=[a("div",{class:"fixed inset-0 bg-black bg-opacity-30"},null,-1)])),_:1}),a("div",w,[a("div",g,[s(l(r),{as:"template",enter:"duration-300 ease-out","enter-from":"opacity-0 scale-95","enter-to":"opacity-100 scale-100",leave:"duration-200 ease-in","leave-from":"opacity-100 scale-100","leave-to":"opacity-0 scale-95"},{default:t(()=>[s(l(u),{class:_(["w-full transform rounded-lg bg-white text-left align-middle shadow-xl transition-all",{"max-w-5xl":e.value==="xl","max-w-4xl":e.value==="lg","max-w-2xl":e.value==="md","max-w-xl":e.value==="sm","max-w-3xl":!e.value||e.value!=="lg"}])},{default:t(()=>[a("div",h,[s(l(m),{as:"h3",class:"text-2xl font-light leading-6"},{default:t(()=>[i(o.$slots,"modal-title")]),_:3}),i(o.$slots,"modal-close-button")]),a("div",null,[a("div",null,[i(o.$slots,"modal-content")])])]),_:3},8,["class"])]),_:3})])])]),_:3}))}};export{q as _};
