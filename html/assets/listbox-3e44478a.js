import{u as T,y as H,S as q,F as Q,o as p,c as G,l as E,f as J,K as W,b as Z,H as A,T as X,t as M,p as Y,N as j,I as _,a as d}from"./hidden-f039557c.js";import{x as $,a as m,u as ee}from"./use-tracked-pointer-2ea74235.js";import{b as te}from"./use-resolve-button-type-15920ae7.js";import{d as le,e as ae}from"./use-controllable-b2bd8058.js";import{G as C,a as P,I as b,H as oe,B,C as z,a9 as N,F as ie,J as ne,K as ue,Z as k,j as re,ac as L}from"./index-ad469968.js";function se(t,x){return t===x}var de=(t=>(t[t.Open=0]="Open",t[t.Closed=1]="Closed",t))(de||{}),ve=(t=>(t[t.Single=0]="Single",t[t.Multi=1]="Multi",t))(ve||{}),pe=(t=>(t[t.Pointer=0]="Pointer",t[t.Other=1]="Other",t))(pe||{});function ce(t){requestAnimationFrame(()=>requestAnimationFrame(t))}let U=Symbol("ListboxContext");function F(t){let x=re(U,null);if(x===null){let y=new Error(`<${t} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(y,F),y}return x}let Oe=C({name:"Listbox",emits:{"update:modelValue":t=>!0},props:{as:{type:[Object,String],default:"template"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],default:()=>se},horizontal:{type:[Boolean],default:!1},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},name:{type:String,optional:!0},multiple:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(t,{slots:x,attrs:y,emit:I}){let e=P(1),c=P(null),g=P(null),O=P(null),v=P([]),l=P(""),a=P(null),h=P(1);function S(o=i=>i){let i=a.value!==null?v.value[a.value]:null,u=_(o(v.value.slice()),f=>p(f.dataRef.domRef)),r=i?u.indexOf(i):null;return r===-1&&(r=null),{options:u,activeOptionIndex:r}}let w=b(()=>t.multiple?1:0),[R,D]=le(b(()=>t.modelValue===void 0?T(w.value,{[1]:[],[0]:void 0}):t.modelValue),o=>I("update:modelValue",o),b(()=>t.defaultValue)),n={listboxState:e,value:R,mode:w,compare(o,i){if(typeof t.by=="string"){let u=t.by;return(o==null?void 0:o[u])===(i==null?void 0:i[u])}return t.by(o,i)},orientation:b(()=>t.horizontal?"horizontal":"vertical"),labelRef:c,buttonRef:g,optionsRef:O,disabled:b(()=>t.disabled),options:v,searchQuery:l,activeOptionIndex:a,activationTrigger:h,closeListbox(){t.disabled||e.value!==1&&(e.value=1,a.value=null)},openListbox(){t.disabled||e.value!==0&&(e.value=0)},goToOption(o,i,u){if(t.disabled||e.value===1)return;let r=S(),f=$(o===m.Specific?{focus:m.Specific,id:i}:{focus:o},{resolveItems:()=>r.options,resolveActiveIndex:()=>r.activeOptionIndex,resolveId:V=>V.id,resolveDisabled:V=>V.dataRef.disabled});l.value="",a.value=f,h.value=u??1,v.value=r.options},search(o){if(t.disabled||e.value===1)return;let i=l.value!==""?0:1;l.value+=o.toLowerCase();let u=(a.value!==null?v.value.slice(a.value+i).concat(v.value.slice(0,a.value+i)):v.value).find(f=>f.dataRef.textValue.startsWith(l.value)&&!f.dataRef.disabled),r=u?v.value.indexOf(u):-1;r===-1||r===a.value||(a.value=r,h.value=1)},clearSearch(){t.disabled||e.value!==1&&l.value!==""&&(l.value="")},registerOption(o,i){let u=S(r=>[...r,{id:o,dataRef:i}]);v.value=u.options,a.value=u.activeOptionIndex},unregisterOption(o){let i=S(u=>{let r=u.findIndex(f=>f.id===o);return r!==-1&&u.splice(r,1),u});v.value=i.options,a.value=i.activeOptionIndex,h.value=1},select(o){t.disabled||D(T(w.value,{[0]:()=>o,[1]:()=>{let i=L(n.value.value).slice(),u=L(o),r=i.findIndex(f=>n.compare(u,L(f)));return r===-1?i.push(u):i.splice(r,1),i}}))}};H([g,O],(o,i)=>{var u;n.closeListbox(),q(i,Q.Loose)||(o.preventDefault(),(u=p(g))==null||u.focus())},b(()=>e.value===0)),oe(U,n),G(b(()=>T(e.value,{[0]:E.Open,[1]:E.Closed})));let s=b(()=>{var o;return(o=p(g))==null?void 0:o.closest("form")});return B(()=>{z([s],()=>{if(!s.value||t.defaultValue===void 0)return;function o(){n.select(t.defaultValue)}return s.value.addEventListener("reset",o),()=>{var i;(i=s.value)==null||i.removeEventListener("reset",o)}},{immediate:!0})}),()=>{let{name:o,modelValue:i,disabled:u,...r}=t,f={open:e.value===0,disabled:u,value:R.value};return N(ie,[...o!=null&&R.value!=null?ae({[o]:R.value}).map(([V,K])=>N(J,W({features:Z.Hidden,key:V,as:"input",type:"hidden",hidden:!0,readOnly:!0,name:V,value:K}))):[],A({ourProps:{},theirProps:{...y,...X(r,["defaultValue","onUpdate:modelValue","horizontal","multiple","by"])},slot:f,slots:x,attrs:y,name:"Listbox"})])}}}),Se=C({name:"ListboxButton",props:{as:{type:[Object,String],default:"button"},id:{type:String,default:()=>`headlessui-listbox-button-${M()}`}},setup(t,{attrs:x,slots:y,expose:I}){let e=F("ListboxButton");I({el:e.buttonRef,$el:e.buttonRef});function c(l){switch(l.key){case d.Space:case d.Enter:case d.ArrowDown:l.preventDefault(),e.openListbox(),k(()=>{var a;(a=p(e.optionsRef))==null||a.focus({preventScroll:!0}),e.value.value||e.goToOption(m.First)});break;case d.ArrowUp:l.preventDefault(),e.openListbox(),k(()=>{var a;(a=p(e.optionsRef))==null||a.focus({preventScroll:!0}),e.value.value||e.goToOption(m.Last)});break}}function g(l){switch(l.key){case d.Space:l.preventDefault();break}}function O(l){e.disabled.value||(e.listboxState.value===0?(e.closeListbox(),k(()=>{var a;return(a=p(e.buttonRef))==null?void 0:a.focus({preventScroll:!0})})):(l.preventDefault(),e.openListbox(),ce(()=>{var a;return(a=p(e.optionsRef))==null?void 0:a.focus({preventScroll:!0})})))}let v=te(b(()=>({as:t.as,type:x.type})),e.buttonRef);return()=>{var l,a;let h={open:e.listboxState.value===0,disabled:e.disabled.value,value:e.value.value},{id:S,...w}=t,R={ref:e.buttonRef,id:S,type:v.value,"aria-haspopup":"listbox","aria-controls":(l=p(e.optionsRef))==null?void 0:l.id,"aria-expanded":e.disabled.value?void 0:e.listboxState.value===0,"aria-labelledby":e.labelRef.value?[(a=p(e.labelRef))==null?void 0:a.id,S].join(" "):void 0,disabled:e.disabled.value===!0?!0:void 0,onKeydown:c,onKeyup:g,onClick:O};return A({ourProps:R,theirProps:w,slot:h,attrs:x,slots:y,name:"ListboxButton"})}}}),ye=C({name:"ListboxOptions",props:{as:{type:[Object,String],default:"ul"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:()=>`headlessui-listbox-options-${M()}`}},setup(t,{attrs:x,slots:y,expose:I}){let e=F("ListboxOptions"),c=P(null);I({el:e.optionsRef,$el:e.optionsRef});function g(l){switch(c.value&&clearTimeout(c.value),l.key){case d.Space:if(e.searchQuery.value!=="")return l.preventDefault(),l.stopPropagation(),e.search(l.key);case d.Enter:if(l.preventDefault(),l.stopPropagation(),e.activeOptionIndex.value!==null){let a=e.options.value[e.activeOptionIndex.value];e.select(a.dataRef.value)}e.mode.value===0&&(e.closeListbox(),k(()=>{var a;return(a=p(e.buttonRef))==null?void 0:a.focus({preventScroll:!0})}));break;case T(e.orientation.value,{vertical:d.ArrowDown,horizontal:d.ArrowRight}):return l.preventDefault(),l.stopPropagation(),e.goToOption(m.Next);case T(e.orientation.value,{vertical:d.ArrowUp,horizontal:d.ArrowLeft}):return l.preventDefault(),l.stopPropagation(),e.goToOption(m.Previous);case d.Home:case d.PageUp:return l.preventDefault(),l.stopPropagation(),e.goToOption(m.First);case d.End:case d.PageDown:return l.preventDefault(),l.stopPropagation(),e.goToOption(m.Last);case d.Escape:l.preventDefault(),l.stopPropagation(),e.closeListbox(),k(()=>{var a;return(a=p(e.buttonRef))==null?void 0:a.focus({preventScroll:!0})});break;case d.Tab:l.preventDefault(),l.stopPropagation();break;default:l.key.length===1&&(e.search(l.key),c.value=setTimeout(()=>e.clearSearch(),350));break}}let O=Y(),v=b(()=>O!==null?(O.value&E.Open)===E.Open:e.listboxState.value===0);return()=>{var l,a,h,S;let w={open:e.listboxState.value===0},{id:R,...D}=t,n={"aria-activedescendant":e.activeOptionIndex.value===null||(l=e.options.value[e.activeOptionIndex.value])==null?void 0:l.id,"aria-multiselectable":e.mode.value===1?!0:void 0,"aria-labelledby":(S=(a=p(e.labelRef))==null?void 0:a.id)!=null?S:(h=p(e.buttonRef))==null?void 0:h.id,"aria-orientation":e.orientation.value,id:R,onKeydown:g,role:"listbox",tabIndex:0,ref:e.optionsRef};return A({ourProps:n,theirProps:D,slot:w,attrs:x,slots:y,features:j.RenderStrategy|j.Static,visible:v.value,name:"ListboxOptions"})}}}),he=C({name:"ListboxOption",props:{as:{type:[Object,String],default:"li"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},id:{type:String,default:()=>`headlessui-listbox.option-${M()}`}},setup(t,{slots:x,attrs:y,expose:I}){let e=F("ListboxOption"),c=P(null);I({el:c,$el:c});let g=b(()=>e.activeOptionIndex.value!==null?e.options.value[e.activeOptionIndex.value].id===t.id:!1),O=b(()=>T(e.mode.value,{[0]:()=>e.compare(L(e.value.value),L(t.value)),[1]:()=>L(e.value.value).some(n=>e.compare(L(n),L(t.value)))})),v=b(()=>T(e.mode.value,{[1]:()=>{var n;let s=L(e.value.value);return((n=e.options.value.find(o=>s.some(i=>e.compare(L(i),L(o.dataRef.value)))))==null?void 0:n.id)===t.id},[0]:()=>O.value})),l=b(()=>({disabled:t.disabled,value:t.value,textValue:"",domRef:c}));B(()=>{var n,s;let o=(s=(n=p(c))==null?void 0:n.textContent)==null?void 0:s.toLowerCase().trim();o!==void 0&&(l.value.textValue=o)}),B(()=>e.registerOption(t.id,l)),ne(()=>e.unregisterOption(t.id)),B(()=>{z([e.listboxState,O],()=>{e.listboxState.value===0&&O.value&&T(e.mode.value,{[1]:()=>{v.value&&e.goToOption(m.Specific,t.id)},[0]:()=>{e.goToOption(m.Specific,t.id)}})},{immediate:!0})}),ue(()=>{e.listboxState.value===0&&g.value&&e.activationTrigger.value!==0&&k(()=>{var n,s;return(s=(n=p(c))==null?void 0:n.scrollIntoView)==null?void 0:s.call(n,{block:"nearest"})})});function a(n){if(t.disabled)return n.preventDefault();e.select(t.value),e.mode.value===0&&(e.closeListbox(),k(()=>{var s;return(s=p(e.buttonRef))==null?void 0:s.focus({preventScroll:!0})}))}function h(){if(t.disabled)return e.goToOption(m.Nothing);e.goToOption(m.Specific,t.id)}let S=ee();function w(n){S.update(n)}function R(n){S.wasMoved(n)&&(t.disabled||g.value||e.goToOption(m.Specific,t.id,0))}function D(n){S.wasMoved(n)&&(t.disabled||g.value&&e.goToOption(m.Nothing))}return()=>{let{disabled:n}=t,s={active:g.value,selected:O.value,disabled:n},{id:o,value:i,disabled:u,...r}=t,f={id:o,ref:c,role:"option",tabIndex:n===!0?void 0:-1,"aria-disabled":n===!0?!0:void 0,"aria-selected":O.value,disabled:void 0,onClick:a,onFocus:h,onPointerenter:w,onMouseenter:w,onPointermove:R,onMousemove:R,onPointerleave:D,onMouseleave:D};return A({ourProps:f,theirProps:r,slot:s,attrs:y,slots:x,name:"ListboxOption"})}}});export{Se as A,he as B,Oe as E,ye as F};
