# Návod pro aktualizaci aplikace s persistent storage

## <PERSON><PERSON><PERSON><PERSON> postup aktualizace

### 1. Zast<PERSON><PERSON> aplikace
```bash
docker-compose down
```

### 2. <PERSON><PERSON><PERSON><PERSON> (doporučeno)
```bash
# Vyt<PERSON>ř z<PERSON>lohu před aktualizací
chmod +x scripts/backup_restore.sh
./scripts/backup_restore.sh backup
```

### 3. Aktualizace kódu
```bash
# Smaž starou verzi
rm -rf html/

# Rozbal novou verzi
unzip nova_verze_aplikace.zip
```

### 4. Spuštění nové verze
```bash
# Spusť s automatickým obnovením dat
docker-compose up -d
```

### 5. <PERSON><PERSON><PERSON><PERSON>
```bash
# Zkontroluj stav
./scripts/backup_restore.sh status

# Zkontroluj logy
docker logs adsys_app
```

## Co se automaticky zachová

✅ **<PERSON><PERSON> se:**
- `.env` soubor s konfigurací
- Všechny logy v `storage/logs/`
- Nahraná data v `storage/app/`
- Cache a sessions
- Databázová data (MariaDB volume)

❌ **Neuchová se:**
- Změny v kódu aplikace
- Nové soubory mimo storage
- Změny v konfiguraci Apache/PHP

## Řešení problémů

### Aplikace se nespustí
```bash
# Zkontroluj logy
docker logs adsys_app

# Zkontroluj .env
docker exec adsys_app cat /var/www/html/backend/.env | head -5
```

### .env se neobnovil
```bash
# Ruční obnovení
./scripts/backup_restore.sh restore

# Nebo zkopíruj ručně
docker cp backup_env adsys_app:/var/www/html/backend/.env
docker exec adsys_app chown www-data:www-data /var/www/html/backend/.env
```

### Problémy s právy
```bash
# Oprava práv
docker exec adsys_app chown -R www-data:www-data /var/www/html/backend/storage
docker exec adsys_app chmod -R 775 /var/www/html/backend/storage
```

### Databáze nefunguje
```bash
# Zkontroluj databázový kontejner
docker logs adsys_db

# Zkontroluj připojení
docker exec adsys_app php backend/artisan migrate:status
```

## Užitečné příkazy

### Monitoring
```bash
# Stav všech kontejnerů
docker-compose ps

# Logy aplikace
docker logs -f adsys_app

# Logy databáze
docker logs -f adsys_db

# Velikost persistent dat
docker exec adsys_app du -sh /var/www/html/backend/storage
```

### Správa záloh
```bash
# Vytvoř zálohu
./scripts/backup_restore.sh backup

# Zobraz stav
./scripts/backup_restore.sh status

# Obnov ze zálohy
./scripts/backup_restore.sh restore
```

### Údržba
```bash
# Vyčisti staré logy (starší než 30 dní)
docker exec adsys_app find /var/www/html/backend/storage/logs -name "*.log" -mtime +30 -delete

# Vyčisti cache
docker exec adsys_app php backend/artisan cache:clear
docker exec adsys_app php backend/artisan config:cache
```

## Poznámky

1. **První spuštění**: Při prvním spuštění se vytvoří prázdné persistent volumes
2. **Automatika**: Vše se děje automaticky při startu kontejneru
3. **Bezpečnost**: Persistent volumes obsahují citlivá data - chraň je
4. **Zálohy**: Doporučuje se pravidelné zálohování před aktualizacemi
5. **Monitoring**: Sleduj logy a stav aplikace po aktualizaci
