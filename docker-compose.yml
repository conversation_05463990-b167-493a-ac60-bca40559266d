services:
  app:
    build: .
    container_name: adsys_app
    restart: unless-stopped
    working_dir: /var/www/html
    ports:
      - "80:80"
    volumes:
      - ./html:/var/www/html
      # Persistent storage pro .env a storage
      - persistent_env:/var/www/html/backend/.env_persistent
      - persistent_storage:/var/www/html/backend/storage
    networks:
      - adsys
    depends_on:
      - db
    env_file:
      - .env

  db:
    image: mariadb:10.5
    container_name: adsys_db
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: adsys_db
      MYSQL_USER: adsys_admin
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - dbdata:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - adsys
    env_file:
      - .env

volumes:
  dbdata:
  persistent_env:
  persistent_storage:

networks:
  adsys: