#!/bin/sh

cd /var/www/html

echo "=== Aktualizace aplikace ==="

# Správa persistent storage před aktualizací
echo "Správa persistent storage..."
/var/www/scripts/manage_persistent_storage.sh

echo "Spouštím migrace"
php backend/artisan migrate

echo "Spouštím app:check-permissions"
php backend/artisan app:check-permissions

echo "Spouštím čistění cache..."
php backend/artisan config:cache
php backend/artisan route:cache
php backend/artisan view:cache

# Nastavení práv (ale zachování persistent storage)
echo "Nastavuji práva..."
chown -R www-data:www-data /var/www/html
# Znovu nastav práva pro persistent storage
chown -R www-data:www-data /var/www/html/backend/storage
chmod -R 775 /var/www/html/backend/storage

# Vytvoření log adresáře pro supervisor
mkdir -p /var/log/supervisor

echo "Aktualizace dokončena."
echo "====================="

# Spuštění supervisord místo apache2-foreground
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf