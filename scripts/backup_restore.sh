#!/bin/bash

# Skript pro zálohování a obnovení persistent dat
# Použití: ./backup_restore.sh [backup|restore|status]

BACKUP_DIR="./backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
CONTAINER_NAME="adsys_app"

# Funkce pro vytvoření zálohy
backup_data() {
    echo "=== Zálohování persistent dat ==="
    
    # Vytvoř backup složku
    mkdir -p "$BACKUP_DIR"
    
    # Záloha .env souboru
    echo "Zálohuji .env soubor..."
    docker cp "$CONTAINER_NAME:/var/www/html/backend/.env" "$BACKUP_DIR/env_${TIMESTAMP}.backup" 2>/dev/null || {
        echo "VAROVÁNÍ: Nepodařilo se zálohovat .env soubor"
    }
    
    # Záloha storage složky
    echo "Zálohuji storage složku..."
    docker exec "$CONTAINER_NAME" tar -czf "/tmp/storage_${TIMESTAMP}.tar.gz" -C "/var/www/html/backend" storage 2>/dev/null || {
        echo "CHYBA: Nepodařilo se vytvořit tar archiv storage složky"
        return 1
    }
    
    docker cp "$CONTAINER_NAME:/tmp/storage_${TIMESTAMP}.tar.gz" "$BACKUP_DIR/" || {
        echo "CHYBA: Nepodařilo se zkopírovat storage archiv"
        return 1
    }
    
    # Vyčisti dočasný soubor
    docker exec "$CONTAINER_NAME" rm -f "/tmp/storage_${TIMESTAMP}.tar.gz"
    
    echo "Záloha dokončena:"
    echo "  - .env: $BACKUP_DIR/env_${TIMESTAMP}.backup"
    echo "  - storage: $BACKUP_DIR/storage_${TIMESTAMP}.tar.gz"
}

# Funkce pro obnovení ze zálohy
restore_data() {
    echo "=== Obnovení persistent dat ==="
    
    if [ ! -d "$BACKUP_DIR" ]; then
        echo "CHYBA: Backup složka neexistuje: $BACKUP_DIR"
        return 1
    fi
    
    # Seznam dostupných záloh
    echo "Dostupné zálohy .env:"
    ls -la "$BACKUP_DIR"/env_*.backup 2>/dev/null || echo "  Žádné zálohy .env"
    
    echo "Dostupné zálohy storage:"
    ls -la "$BACKUP_DIR"/storage_*.tar.gz 2>/dev/null || echo "  Žádné zálohy storage"
    
    # Najdi nejnovější zálohy
    LATEST_ENV=$(ls -t "$BACKUP_DIR"/env_*.backup 2>/dev/null | head -1)
    LATEST_STORAGE=$(ls -t "$BACKUP_DIR"/storage_*.tar.gz 2>/dev/null | head -1)
    
    if [ -n "$LATEST_ENV" ]; then
        echo "Obnovuji .env z: $LATEST_ENV"
        docker cp "$LATEST_ENV" "$CONTAINER_NAME:/var/www/html/backend/.env"
        docker exec "$CONTAINER_NAME" chown www-data:www-data "/var/www/html/backend/.env"
        docker exec "$CONTAINER_NAME" chmod 644 "/var/www/html/backend/.env"
    else
        echo "VAROVÁNÍ: Žádná záloha .env k obnovení"
    fi
    
    if [ -n "$LATEST_STORAGE" ]; then
        echo "Obnovuji storage z: $LATEST_STORAGE"
        
        # Zkopíruj archiv do kontejneru
        docker cp "$LATEST_STORAGE" "$CONTAINER_NAME:/tmp/restore_storage.tar.gz"
        
        # Rozbal archiv
        docker exec "$CONTAINER_NAME" tar -xzf "/tmp/restore_storage.tar.gz" -C "/var/www/html/backend"
        
        # Nastav práva
        docker exec "$CONTAINER_NAME" chown -R www-data:www-data "/var/www/html/backend/storage"
        docker exec "$CONTAINER_NAME" chmod -R 775 "/var/www/html/backend/storage"
        
        # Vyčisti dočasný soubor
        docker exec "$CONTAINER_NAME" rm -f "/tmp/restore_storage.tar.gz"
    else
        echo "VAROVÁNÍ: Žádná záloha storage k obnovení"
    fi
    
    echo "Obnovení dokončeno."
}

# Funkce pro zobrazení stavu
show_status() {
    echo "=== Stav persistent storage ==="
    
    # Kontrola kontejneru
    if ! docker ps | grep -q "$CONTAINER_NAME"; then
        echo "CHYBA: Kontejner $CONTAINER_NAME neběží"
        return 1
    fi
    
    # Kontrola .env
    echo "Kontrola .env souboru:"
    if docker exec "$CONTAINER_NAME" test -f "/var/www/html/backend/.env"; then
        ENV_SIZE=$(docker exec "$CONTAINER_NAME" wc -l < "/var/www/html/backend/.env")
        ENV_MODIFIED=$(docker exec "$CONTAINER_NAME" stat -c %y "/var/www/html/backend/.env")
        echo "  ✓ Existuje ($ENV_SIZE řádků, změněn: $ENV_MODIFIED)"
    else
        echo "  ✗ Neexistuje"
    fi
    
    # Kontrola storage
    echo "Kontrola storage složky:"
    if docker exec "$CONTAINER_NAME" test -d "/var/www/html/backend/storage"; then
        STORAGE_SIZE=$(docker exec "$CONTAINER_NAME" du -sh "/var/www/html/backend/storage" | cut -f1)
        echo "  ✓ Existuje (velikost: $STORAGE_SIZE)"
        
        # Kontrola podsložek
        echo "  Podsložky:"
        docker exec "$CONTAINER_NAME" ls -la "/var/www/html/backend/storage/" | grep "^d" | awk '{print "    " $9 " (" $5 " bytes)"}'
        
        # Kontrola logů
        LOG_COUNT=$(docker exec "$CONTAINER_NAME" find "/var/www/html/backend/storage/logs" -name "*.log" 2>/dev/null | wc -l)
        echo "  Počet log souborů: $LOG_COUNT"
    else
        echo "  ✗ Neexistuje"
    fi
    
    # Kontrola volumes
    echo "Docker volumes:"
    docker volume ls | grep -E "(persistent_env|persistent_storage)" | while read line; do
        echo "  $line"
    done
    
    # Kontrola záloh
    echo "Dostupné zálohy:"
    if [ -d "$BACKUP_DIR" ]; then
        ENV_BACKUPS=$(ls "$BACKUP_DIR"/env_*.backup 2>/dev/null | wc -l)
        STORAGE_BACKUPS=$(ls "$BACKUP_DIR"/storage_*.tar.gz 2>/dev/null | wc -l)
        echo "  .env zálohy: $ENV_BACKUPS"
        echo "  storage zálohy: $STORAGE_BACKUPS"
    else
        echo "  Žádné zálohy (složka $BACKUP_DIR neexistuje)"
    fi
}

# Hlavní logika
case "$1" in
    backup)
        backup_data
        ;;
    restore)
        restore_data
        ;;
    status)
        show_status
        ;;
    *)
        echo "Použití: $0 [backup|restore|status]"
        echo ""
        echo "Příkazy:"
        echo "  backup  - Vytvoří zálohu .env a storage"
        echo "  restore - Obnoví nejnovější zálohu"
        echo "  status  - Zobrazí stav persistent storage"
        exit 1
        ;;
esac
