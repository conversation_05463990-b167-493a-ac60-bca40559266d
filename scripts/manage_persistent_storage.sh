#!/bin/bash

# Skript pro správu persistent storage
# Zajišťuje zachování .env souboru a storage složky při aktualizacích

BACKEND_DIR="/var/www/html/backend"
ENV_FILE="$BACKEND_DIR/.env"
ENV_PERSISTENT="/var/www/html/backend/.env_persistent"
STORAGE_DIR="$BACKEND_DIR/storage"

echo "=== Správa persistent storage ==="

# Funkce pro kopírování .env souboru
manage_env_file() {
    echo "Správa .env souboru..."
    
    # Pokud existuje persistent .env a aktuální .env neexistuje nebo je starš<PERSON>
    if [ -f "$ENV_PERSISTENT" ]; then
        if [ ! -f "$ENV_FILE" ] || [ "$ENV_PERSISTENT" -nt "$ENV_FILE" ]; then
            echo "Obnovuji .env z persistent storage..."
            cp "$ENV_PERSISTENT" "$ENV_FILE"
            chown www-data:www-data "$ENV_FILE"
            chmod 644 "$ENV_FILE"
        fi
    fi
    
    # Pokud existuje aktuální .env, zkopíruj ho do persistent storage
    if [ -f "$ENV_FILE" ]; then
        echo "Zálohuju .env do persistent storage..."
        cp "$ENV_FILE" "$ENV_PERSISTENT"
        chown www-data:www-data "$ENV_PERSISTENT"
        chmod 644 "$ENV_PERSISTENT"
    fi
}

# Funkce pro správu storage složky
manage_storage_dir() {
    echo "Správa storage složky..."
    
    # Ujisti se, že storage složka existuje a má správná práva
    if [ -d "$STORAGE_DIR" ]; then
        echo "Nastavuji práva pro storage složku..."
        chown -R www-data:www-data "$STORAGE_DIR"
        chmod -R 755 "$STORAGE_DIR"
        
        # Speciální práva pro logs a cache
        if [ -d "$STORAGE_DIR/logs" ]; then
            chmod -R 775 "$STORAGE_DIR/logs"
        fi
        
        if [ -d "$STORAGE_DIR/framework/cache" ]; then
            chmod -R 775 "$STORAGE_DIR/framework/cache"
        fi
        
        if [ -d "$STORAGE_DIR/framework/sessions" ]; then
            chmod -R 775 "$STORAGE_DIR/framework/sessions"
        fi
        
        if [ -d "$STORAGE_DIR/framework/views" ]; then
            chmod -R 775 "$STORAGE_DIR/framework/views"
        fi
        
        if [ -d "$STORAGE_DIR/app" ]; then
            chmod -R 775 "$STORAGE_DIR/app"
        fi
    else
        echo "VAROVÁNÍ: Storage složka neexistuje!"
    fi
}

# Funkce pro vytvoření potřebných složek
create_required_dirs() {
    echo "Vytváření potřebných složek..."
    
    # Vytvoř základní Laravel storage strukturu pokud neexistuje
    mkdir -p "$STORAGE_DIR/app/public"
    mkdir -p "$STORAGE_DIR/framework/cache/data"
    mkdir -p "$STORAGE_DIR/framework/sessions"
    mkdir -p "$STORAGE_DIR/framework/testing"
    mkdir -p "$STORAGE_DIR/framework/views"
    mkdir -p "$STORAGE_DIR/logs"
    mkdir -p "$STORAGE_DIR/app/temp"
    mkdir -p "$STORAGE_DIR/app/backups"
    mkdir -p "$STORAGE_DIR/app/database"
    
    # Nastavení práv
    chown -R www-data:www-data "$STORAGE_DIR"
    chmod -R 755 "$STORAGE_DIR"
    chmod -R 775 "$STORAGE_DIR/logs"
    chmod -R 775 "$STORAGE_DIR/framework/cache"
    chmod -R 775 "$STORAGE_DIR/framework/sessions"
    chmod -R 775 "$STORAGE_DIR/framework/views"
    chmod -R 775 "$STORAGE_DIR/app"
}

# Hlavní logika
main() {
    echo "Spouštím správu persistent storage..."
    
    # Počkej na připojení volume
    sleep 2
    
    # Vytvoř potřebné složky
    create_required_dirs
    
    # Spravuj .env soubor
    manage_env_file
    
    # Spravuj storage složku
    manage_storage_dir
    
    echo "Persistent storage je připraven."
    echo "================================"
}

# Spusť hlavní funkci
main
